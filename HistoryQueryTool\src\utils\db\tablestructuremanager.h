#ifndef TABLESTRUCTUREMANAGER_H
#define TABLESTRUCTUREMANAGER_H

#include <QObject>
#include <QMap>
#include <QString>
#include <QStringList>
#include <QSqlDatabase>

/**
 * @brief 表结构管理器类，用于检查表结构并确定在查询界面中哪些选项应该被锁定
 */
class TableStructureManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit TableStructureManager(QObject *parent = nullptr);

    /**
     * @brief 初始化表结构管理器
     * @param db 数据库连接
     * @return 是否初始化成功
     */
    bool initialize(const QSqlDatabase &db);

    /**
     * @brief 检查表是否包含指定字段
     * @param tableName 表名
     * @param fieldName 字段名
     * @return 是否包含该字段
     */
    bool hasField(const QString &tableName, const QString &fieldName) const;

    /**
     * @brief 获取表的所有字段
     * @param tableName 表名
     * @return 字段列表
     */
    QStringList getTableFields(const QString &tableName) const;

    /**
     * @brief 检查表是否应该锁定主机名选择
     * @param tableName 表名
     * @return 是否应该锁定
     */
    bool shouldLockHostname(const QString &tableName) const;

    /**
     * @brief 检查表是否应该锁定设备选择
     * @param tableName 表名
     * @return 是否应该锁定
     */
    bool shouldLockDevice(const QString &tableName) const;

    /**
     * @brief 获取所有表名
     * @return 表名列表
     */
    QStringList getTableNames() const;

private:
    /**
     * @brief 检查表结构
     * @param db 数据库连接
     * @return 是否检查成功
     */
    bool checkTableStructure(const QSqlDatabase &db);

    // 存储表结构信息的数据结构
    struct TableInfo {
        bool hasHostnameField;  // 是否有主机名字段
        bool hasDeviceField;   // 是否有设备字段
        QStringList fields;    // 所有字段
    };

    // 表结构信息映射
    QMap<QString, TableInfo> m_tableInfoMap;

    // 需要检查的表
    QStringList m_tables;

    // 需要检查的字段
    static const QString HOSTNAME_FIELD;
    static const QString DEVICE_FIELD;
};

#endif // TABLESTRUCTUREMANAGER_H 