#!/bin/bash

# SSH访问测试脚本
# 使用SSH方式测试目录访问

# 配置信息
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="admin"
REMOTE_PASS="admin"

# 测试目录
TEST_DIRS=(
    "/home/<USER>/zhuanJiKong-Ewenjian/"
    "/home/<USER>/zhuanJiKong-Ewenjian2/"
    "/home/<USER>/zhuanJiKong-Ewenjian3/"
)

echo "=========================================="
echo "SSH访问测试脚本"
echo "=========================================="

# 检查依赖
if ! command -v sshpass >/dev/null 2>&1; then
    echo "错误: 缺少sshpass命令"
    exit 1
fi

echo "1. 测试SSH连接..."
if sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no -o ConnectTimeout=10 "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH连接成功'" 2>/dev/null; then
    echo "✓ SSH连接成功"
else
    echo "✗ SSH连接失败"
    exit 1
fi

echo ""
echo "2. 查看用户主目录..."
sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "pwd && ls -la" 2>/dev/null

echo ""
echo "3. 查看/home目录..."
sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "ls -la /home" 2>/dev/null

echo ""
echo "4. 测试目标目录..."
for dir in "${TEST_DIRS[@]}"; do
    echo "测试目录: $dir"
    result=$(sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "ls -la \"$dir\"" 2>&1)
    if echo "$result" | grep -q "No such file or directory\|cannot access"; then
        echo "  ✗ 目录不存在或无权限访问"
    elif echo "$result" | grep -q "Permission denied"; then
        echo "  ✗ 权限被拒绝"
    elif [ -n "$result" ]; then
        echo "  ✓ 目录访问成功"
        echo "$result" | head -5
    else
        echo "  ? 未知状态"
    fi
    echo "----------------------------------------"
done

echo ""
echo "5. 查找可能的目录结构..."
echo "查找zhuanJiKong相关目录:"
sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "find /home -name '*zhuanJiKong*' -type d 2>/dev/null | head -10"

echo ""
echo "查找包含WPD文件的目录:"
sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "find /home -name '*.WPD' 2>/dev/null | head -10"

echo ""
echo "测试完成！"
