#ifndef TABLENAMEMANAGER_H
#define TABLENAMEMANAGER_H

#include <QString>
#include <QDate>

/**
 * @brief 表名管理器类
 * 统一管理各种表名的生成规则
 */
class TableNameManager
{
public:
    // 遥测历史数据表名
    static QString getMinuteTelemetryTable(const QDate &date);
    static QString getSecondTelemetryTable(const QDate &date);
    
    // 其他历史数据表名
    static QString getSOEEventTable();
    static QString getYXChangeTable();
    static QString getLimitAlarmTable();
    static QString getSystemEventTable();
    static QString getOperationRecordTable();
    static QString getUserLoginTable();
    
private:
    // 表名前缀常量
    static const QString MINUTE_TABLE_PREFIX;
    static const QString SECOND_TABLE_PREFIX;
};

#endif // TABLENAMEMANAGER_H 