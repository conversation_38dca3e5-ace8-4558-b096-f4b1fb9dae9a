/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralWidget;
    QStatusBar *statusBar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QStringLiteral("MainWindow"));
        MainWindow->resize(1024, 768);
        MainWindow->setStyleSheet(QLatin1String("QMainWindow {\n"
"    background-color: #f5f5f5;\n"
"}\n"
"\n"
"QToolBar {\n"
"    background-color: #2c3e50;\n"
"    border: none;\n"
"    spacing: 3px;\n"
"    min-height: 40px;\n"
"}\n"
"\n"
"QToolBar QToolButton {\n"
"    background-color: transparent;\n"
"    color: white;\n"
"    border: none;\n"
"    padding: 5px;\n"
"    margin: 2px;\n"
"}\n"
"\n"
"QToolBar QToolButton:hover {\n"
"    background-color: #34495e;\n"
"    border-radius: 4px;\n"
"}\n"
"\n"
"QStatusBar {\n"
"    background-color: #ecf0f1;\n"
"    color: #2c3e50;\n"
"}\n"
"\n"
"QSplitter::handle {\n"
"    background-color: #bdc3c7;\n"
"}\n"
"\n"
"QSplitter::handle:horizontal {\n"
"    width: 2px;\n"
"}\n"
"\n"
"QSplitter::handle:vertical {\n"
"    height: 2px;\n"
"}\n"
""));
        centralWidget = new QWidget(MainWindow);
        centralWidget->setObjectName(QStringLiteral("centralWidget"));
        MainWindow->setCentralWidget(centralWidget);
        statusBar = new QStatusBar(MainWindow);
        statusBar->setObjectName(QStringLiteral("statusBar"));
        MainWindow->setStatusBar(statusBar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QApplication::translate("MainWindow", "\345\216\206\345\217\262\346\225\260\346\215\256\346\237\245\350\257\242\345\267\245\345\205\267", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
