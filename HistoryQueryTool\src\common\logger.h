#ifndef LOGGER_H
#define LOGGER_H

#include <QDebug>
#include <QString>

/**
 * @brief 日志管理器类
 * 用于控制应用程序的日志输出
 */
class Logger
{
public:
    /**
     * @brief 设置是否启用调试日志
     * @param enable true启用，false禁用
     */
    static void setDebugEnabled(bool enable);
    
    /**
     * @brief 获取调试日志是否启用
     * @return true已启用，false已禁用
     */
    static bool isDebugEnabled();
    
    /**
     * @brief 输出调试日志
     * @param message 日志消息
     */
    static void debug(const QString &message);
    
    /**
     * @brief 输出信息日志（始终输出）
     * @param message 日志消息
     */
    static void info(const QString &message);
    
    /**
     * @brief 输出警告日志（始终输出）
     * @param message 日志消息
     */
    static void warning(const QString &message);
    
    /**
     * @brief 输出错误日志（始终输出）
     * @param message 日志消息
     */
    static void error(const QString &message);
    
private:
    static bool s_debugEnabled;
};

// 便捷宏定义
#define LOG_DEBUG(msg) Logger::debug(msg)
#define LOG_INFO(msg) Logger::info(msg)
#define LOG_WARNING(msg) Logger::warning(msg)
#define LOG_ERROR(msg) Logger::error(msg)

#endif // LOGGER_H 