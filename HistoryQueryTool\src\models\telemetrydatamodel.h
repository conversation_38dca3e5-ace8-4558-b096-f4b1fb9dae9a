#ifndef TELEMETRYDATAMODEL_H
#define TELEMETRYDATAMODEL_H

#include <QAbstractTableModel>
#include <QDateTime>
#include <QVector>
#include <QMap>
#include <QVariant>
#include "../common/datastructures.h"

/**
 * @brief 遥测数据模型类，专门处理遥测历史数据
 */
class TelemetryDataModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    // 列定义
    enum Column {
        TIMESTAMP = 0,  // 时间戳列
        VALUE,          // 值列
        QUALITY,        // 质量码列
        COLUMN_COUNT    // 列总数
    };
    
    explicit TelemetryDataModel(QObject *parent = nullptr);
    
    /**
     * @brief 重写行数方法
     */
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    
    /**
     * @brief 重写列数方法
     */
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    
    /**
     * @brief 重写数据方法
     */
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    
    /**
     * @brief 重写表头数据方法
     */
    QVariant headerData(int section, Qt::Orientation orientation,
                       int role = Qt::DisplayRole) const override;
    
    /**
     * @brief 设置模型数据
     * @param dataPoints 数据点集合
     */
    void setData(const QVector<TelemetryDataPoint> &dataPoints);
    
    /**
     * @brief 添加数据点
     * @param dataPoint 数据点
     */
    void addDataPoint(const TelemetryDataPoint &dataPoint);
    
    /**
     * @brief 清空数据
     */
    void clear();
    
    /**
     * @brief 获取某个索引的数据点
     * @param index 索引
     * @return 数据点
     */
    TelemetryDataPoint dataPointAt(int index) const;
    
    /**
     * @brief 获取所有数据点
     * @return 数据点集合
     */
    QVector<TelemetryDataPoint> dataPoints() const;
    
    /**
     * @brief 设置遥测点属性
     * @param properties 属性映射
     */
    void setProperties(const QMap<QString, QVariant> &properties);
    
    /**
     * @brief 获取遥测点属性
     * @return 属性映射
     */
    QMap<QString, QVariant> properties() const;
    
    /**
     * @brief 获取遥测点名称
     * @return 名称
     */
    QString name() const;
    
    /**
     * @brief 获取遥测点单位
     * @return 单位
     */
    QString unit() const;
    
    /**
     * @brief 获取遥测点系数
     * @return 系数
     */
    double rate() const;
    
    /**
     * @brief 获取遥测点偏移量
     * @return 偏移量
     */
    double offset() const;

public slots:
    /**
     * @brief 从 BLOB 数据加载和解析遥测数据点。
     * @param blobData 包含原始遥测数据的 QByteArray。
     * @param baseTimestampEpochSecs BLOB 中第一个数据点对应的基准时间戳 (自纪元以来的秒数)。
     * @param isMinuteData 如果为 true，则按分钟数据解析 (25 点)；否则按秒数据解析 (300 点)。
     */
    void loadDataFromBlob(const QByteArray &blobData, qint64 baseTimestampEpochSecs, bool isMinuteData);

public:
    /**
     * @brief 解析单个BLOB数据，返回数据点集合。
     * @param blobData 包含原始遥测数据的 QByteArray。
     * @param baseTimestampEpochSecs BLOB中第一个数据点对应的基准时间戳 (自纪元以来的秒数)。
     * @param isMinuteData 如果为 true，则按分钟数据解析 (25 点)；否则按秒数据解析 (300 点)。
     * @return 解析出的 TelemetryDataPoint 集合。
     */
    static QVector<TelemetryDataPoint> parseBlobData(const QByteArray &blobData, qint64 baseTimestampEpochSecs, bool isMinuteData);

private:
    // 数据点集合
    QVector<TelemetryDataPoint> m_dataPoints;

    // 遥测点属性
    QMap<QString, QVariant> m_properties;

    // 时间戳偏移量（秒），用于修正数据采集时间与写入时间的差异
    // 默认为-300秒（-5分钟），表示实际采集时间比写入时间早5分钟
    static const int TIME_OFFSET_SECONDS = -300;
};

#endif // TELEMETRYDATAMODEL_H 