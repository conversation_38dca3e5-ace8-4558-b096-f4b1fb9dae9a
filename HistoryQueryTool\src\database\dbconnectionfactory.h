#ifndef DBCONNECTIONFACTORY_H
#define DBCONNECTIONFACTORY_H

#include <QSqlDatabase>
#include <QString>

struct DBConfig;

/**
 * @brief 数据库连接工厂类
 * 统一管理数据库连接的创建，避免重复代码
 */
class DBConnectionFactory
{
public:
    /**
     * @brief 创建参数库连接
     * @param connectionName 连接名称
     * @return 数据库连接对象
     */
    static QSqlDatabase createParaDBConnection(const QString &connectionName);
    
    /**
     * @brief 创建历史库连接
     * @param connectionName 连接名称
     * @return 数据库连接对象
     */
    static QSqlDatabase createHisDBConnection(const QString &connectionName);
    
    /**
     * @brief 从配置创建数据库连接
     * @param config 数据库配置
     * @param connectionName 连接名称
     * @return 数据库连接对象
     */
    static QSqlDatabase createConnectionFromConfig(const DBConfig &config, const QString &connectionName);
};

#endif // DBCONNECTIONFACTORY_H 