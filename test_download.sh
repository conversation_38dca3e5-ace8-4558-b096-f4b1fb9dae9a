#!/bin/bash

# 测试下载脚本 - 直接下载所有WPD文件

# 配置信息
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="admin"
REMOTE_PASS="admin"

# 测试目录
REMOTE_DIR="/home/<USER>/zhuanJi<PERSON>ong-<PERSON>/"
LOCAL_DIR="/home/<USER>/qsjglyc/"

echo "=========================================="
echo "测试下载脚本"
echo "=========================================="

# 创建本地目录
mkdir -p "$LOCAL_DIR"

echo "1. 获取远程目录文件列表..."
echo "远程目录: $REMOTE_DIR"

# 获取文件列表
file_list=$(sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "ls -la '$REMOTE_DIR'")

echo "文件列表:"
echo "$file_list"

echo ""
echo "2. 查找WPD文件..."

# 查找WPD文件
wpd_files=$(echo "$file_list" | grep "\.WPD$" | awk '{print $NF}')

if [ -z "$wpd_files" ]; then
    echo "未找到WPD文件"
    exit 1
fi

echo "找到的WPD文件:"
echo "$wpd_files"

echo ""
echo "3. 开始下载文件..."

# 下载每个WPD文件
download_count=0
while IFS= read -r filename; do
    if [ -n "$filename" ]; then
        echo "下载文件: $filename"
        
        remote_file="$REMOTE_DIR/$filename"
        local_file="$LOCAL_DIR/$filename"
        
        if sshpass -p "$REMOTE_PASS" scp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST:$remote_file" "$local_file" 2>/dev/null; then
            if [ -f "$local_file" ]; then
                filesize=$(stat -c%s "$local_file" 2>/dev/null || echo "0")
                echo "  ✓ 下载成功: $filename ($filesize bytes)"
                ((download_count++))
            else
                echo "  ✗ 下载后文件不存在: $filename"
            fi
        else
            echo "  ✗ 下载失败: $filename"
        fi
    fi
done <<< "$wpd_files"

echo ""
echo "4. 下载完成"
echo "成功下载 $download_count 个文件到 $LOCAL_DIR"

echo ""
echo "5. 检查本地文件..."
ls -la "$LOCAL_DIR"

echo ""
echo "测试完成！"
