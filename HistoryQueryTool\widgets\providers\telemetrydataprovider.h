#ifndef TELEMETRYDATAPROVIDER_H
#define TELEMETRYDATAPROVIDER_H

#include <QObject>
#include <QSqlQuery>
#include <QDateTime>
#include <QStringList>
#include <QMap>
#include <QVariant>
#include "../../common/enums.h"

class DBManager;

/**
 * @brief 遥测历史数据提供者类
 * 负责分钟级和秒级遥测数据的查询和处理
 */
class TelemetryDataProvider : public QObject
{
    Q_OBJECT
public:
    explicit TelemetryDataProvider(QObject *parent = nullptr);
    
    /**
     * @brief 设置数据库管理器
     * @param dbManager 数据库管理器指针
     */
    void setDBManager(DBManager *dbManager);
    
    /**
     * @brief 执行分钟级遥测历史数据查询
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    QSqlQuery executeMinuteTelemetryQuery(int deviceId, int deviceIndex,
                                         const QDateTime &startTime, 
                                         const QDateTime &endTime);
    
    /**
     * @brief 执行秒级遥测历史数据查询
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    QSqlQuery executeSecondTelemetryQuery(int deviceId, int deviceIndex,
                                         const QDateTime &startTime, 
                                         const QDateTime &endTime);
    
    /**
     * @brief 构建分钟级遥测查询SQL语句
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return SQL查询语句
     */
    QString buildMinuteTelemetrySQL(int deviceId, int deviceIndex,
                                   const QDateTime &startTime, 
                                   const QDateTime &endTime);
    
    /**
     * @brief 构建秒级遥测查询SQL语句
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return SQL查询语句
     */
    QString buildSecondTelemetrySQL(int deviceId, int deviceIndex,
                                   const QDateTime &startTime, 
                                   const QDateTime &endTime);
    
    /**
     * @brief 获取遥测点的属性（名称、单位等）
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @return 遥测点属性映射
     */
    QMap<QString, QVariant> getTelemetryProperties(int deviceId, int deviceIndex);
    
    /**
     * @brief 检查数据库连接是否有效
     * @return 是否连接有效
     */
    bool isDatabaseConnected() const;

private:
    /**
     * @brief 根据日期生成分钟级历史表名
     * @param date 日期
     * @return 表名
     */
    QString getMinuteTableName(const QDate &date) const;
    
    /**
     * @brief 根据日期生成秒级历史表名
     * @param date 日期
     * @return 表名
     */
    QString getSecondTableName(const QDate &date) const;
    
    /**
     * @brief 处理跨日期查询
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param isMinute 是否为分钟级数据
     * @return 查询结果
     */
    QSqlQuery executeCrossDayQuery(int deviceId, int deviceIndex,
                                  const QDateTime &startTime, 
                                  const QDateTime &endTime,
                                  bool isMinute);
    
    // 数据库管理器
    DBManager *m_dbManager;
    
    // 遥测参数表名
    QString m_telemetryParamTable;
    
    // 分钟级表名前缀
    QString m_minuteTablePrefix;
    
    // 秒级表名前缀
    QString m_secondTablePrefix;
};

#endif // TELEMETRYDATAPROVIDER_H 