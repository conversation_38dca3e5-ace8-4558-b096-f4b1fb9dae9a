#include "devicenamemapper.h"
#include "../utils/xmlconfigreader.h"
#include "../database/dbconnectionfactory.h"
#include <QDebug>

DeviceNameMapper* DeviceNameMapper::m_instance = nullptr;

DeviceNameMapper* DeviceNameMapper::instance()
{
    if (!m_instance) {
        m_instance = new DeviceNameMapper();
    }
    return m_instance;
}

DeviceNameMapper::DeviceNameMapper(QObject *parent)
    : QObject(parent),
      m_cacheExpirationSeconds(3600), // 缓存1小时过期
      m_initialized(false)
{
}

DeviceNameMapper::~DeviceNameMapper()
{
    // 关闭数据库连接
    if (m_paraDb.isOpen()) {
        m_paraDb.close();
    }
    
    // 移除数据库连接
    if (QSqlDatabase::contains("para_db_connection")) {
        QSqlDatabase::removeDatabase("para_db_connection");
    }
}

bool DeviceNameMapper::initialize()
{
    // 确保XML配置已加载
    XMLConfigReader *configReader = XMLConfigReader::instance();
    if (!configReader || !configReader->isParaDBConfigValid()) {
        m_lastError = "参数库配置无效或未加载";
        return false;
    }
    
    // 关闭已存在的连接
    if (m_paraDb.isOpen()) {
        m_paraDb.close();
    }
    
    // 移除已存在的连接
    if (QSqlDatabase::contains("para_db_connection")) {
        QSqlDatabase::removeDatabase("para_db_connection");
    }
    
    // 使用工厂类创建数据库连接
    m_paraDb = DBConnectionFactory::createParaDBConnection("para_db_connection");
    
    if (!m_paraDb.open()) {
        m_lastError = QString("无法连接到参数库: %1").arg(m_paraDb.lastError().text());
        emit initialized(false);
        return false;
    }
    
    // 验证表结构
    QSqlQuery query(m_paraDb);
    
    // 检查para_device表是否存在
    query.exec("SHOW TABLES LIKE 'para_device'");
    if (!query.next()) {
        m_lastError = "参数库中缺少para_device表";
        m_paraDb.close();
        emit initialized(false);
        return false;
    }
    
    // 检查表字段
    query.exec("DESCRIBE para_device");
    
    bool hasDeviceID = false;
    bool hasName = false;
    
    while (query.next()) {
        QString fieldName = query.value(0).toString().toLower();
        if (fieldName == "deviceid") {
            hasDeviceID = true;
        } else if (fieldName == "name") {
            hasName = true;
        }
    }
    
    if (!hasDeviceID || !hasName) {
        m_lastError = QString("参数库表缺少必要的字段: %1%2%3")
                     .arg(!hasDeviceID ? "DeviceID" : "")
                     .arg(!hasDeviceID && !hasName ? ", " : "")
                     .arg(!hasName ? "Name" : "");
        m_paraDb.close();
        emit initialized(false);
        return false;
    }
    
    // 加载设备名称缓存
    refreshCache();
    
    m_initialized = true;
    emit initialized(true);
    return true;
}

QString DeviceNameMapper::getDeviceNameById(int deviceId)
{
    // 如果未初始化，尝试初始化
    if (!m_initialized) {
        if (!initialize()) {
            return QString::number(deviceId);
        }
    }
    
    // 检查缓存是否过期
    if (m_lastCacheUpdateTime.isNull() || 
        m_lastCacheUpdateTime.secsTo(QDateTime::currentDateTime()) > m_cacheExpirationSeconds) {
        refreshCache();
    }
    
    // 查找缓存中的设备名称
    if (m_deviceNameCache.contains(deviceId)) {
        QString name = m_deviceNameCache.value(deviceId);
        return name;
    }
    
    // 如果缓存中没有，尝试从数据库查询
    if (m_paraDb.isOpen()) {
        QSqlQuery query(m_paraDb);
        
        // 查询 para_device 表 DeviceID 字段
        query.prepare("SELECT Name FROM para_device WHERE DeviceID = :deviceid");
        query.bindValue(":deviceid", deviceId);
        
        if (query.exec()) {
            if (query.next()) {
                QString deviceName = query.value(0).toString();
                m_deviceNameCache.insert(deviceId, deviceName);
                return deviceName;
            }
        }
        
        // 尝试备用查询 - 使用Address字段
        query.clear();
        query.prepare("SELECT Name FROM para_device WHERE Address = :deviceid");
        query.bindValue(":deviceid", deviceId);
        
        if (query.exec()) {
            if (query.next()) {
                QString deviceName = query.value(0).toString();
                m_deviceNameCache.insert(deviceId, deviceName);
                return deviceName;
            }
        }
        
        // 尝试第三种查询 - 使用全小写字段名
        query.clear();
        query.prepare("SELECT name FROM para_device WHERE deviceid = :deviceid");
        query.bindValue(":deviceid", deviceId);
        
        if (query.exec()) {
            if (query.next()) {
                QString deviceName = query.value(0).toString();
                m_deviceNameCache.insert(deviceId, deviceName);
                return deviceName;
            }
        }
    } else {
        // 尝试重新连接数据库
        if (initialize()) {
            return getDeviceNameById(deviceId);
        }
    }
    
    // 返回设备ID作为默认值
    return QString::number(deviceId);
}

void DeviceNameMapper::refreshCache()
{
    // 清空缓存
    m_deviceNameCache.clear();
    
    // 确保数据库已连接
    if (!m_paraDb.isOpen()) {
        if (!initialize()) {
            return;
        }
    }
    
    // 查询所有设备ID和名称
    QSqlQuery query(m_paraDb);
    
    if (query.exec("SELECT DeviceID, Name FROM para_device")) {
        while (query.next()) {
            int deviceId = query.value(0).toInt();
            QString deviceName = query.value(1).toString();
            m_deviceNameCache.insert(deviceId, deviceName);
        }
    } else {
        // 尝试使用小写字段名查询
        if (query.exec("SELECT deviceid, name FROM para_device")) {
            while (query.next()) {
                int deviceId = query.value(0).toInt();
                QString deviceName = query.value(1).toString();
                m_deviceNameCache.insert(deviceId, deviceName);
            }
        }
    }
    
    // 更新缓存时间
    m_lastCacheUpdateTime = QDateTime::currentDateTime();
}

QMap<int, QString> DeviceNameMapper::getAllDeviceNames() const {
    return m_deviceNameCache;
} 