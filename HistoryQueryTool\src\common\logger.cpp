#include "logger.h"
#include <QDateTime>

// 静态成员初始化
bool Logger::s_debugEnabled = false;

void Logger::setDebugEnabled(bool enable)
{
    s_debugEnabled = enable;
}

bool Logger::isDebugEnabled()
{
    return s_debugEnabled;
}

void Logger::debug(const QString &message)
{
    if (s_debugEnabled) {
        qDebug() << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")
                 << "Debug:" << message;
    }
}

void Logger::info(const QString &message)
{
    qInfo() << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")
            << "Info:" << message;
}

void Logger::warning(const QString &message)
{
    qWarning() << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")
               << "Warning:" << message;
}

void Logger::error(const QString &message)
{
    qCritical() << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")
                << "Error:" << message;
} 