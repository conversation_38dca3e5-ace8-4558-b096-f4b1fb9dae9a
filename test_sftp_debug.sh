#!/bin/bash

# SFTP调试测试脚本
# 用于诊断远程目录访问问题

# 配置信息（请根据实际情况修改）
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="admin"                # 请替换为实际用户名
REMOTE_PASS="admin"                # 请替换为实际密码

# 测试目录
TEST_DIR="/home/<USER>/zhuanJiKong-Ewen<PERSON>an/"

echo "=========================================="
echo "SFTP调试测试脚本"
echo "=========================================="

# 检查依赖
if ! command -v sshpass >/dev/null 2>&1; then
    echo "错误: 缺少sshpass命令"
    echo "安装命令: sudo yum install sshpass"
    exit 1
fi

echo "1. 测试基本连接..."
echo "quit" | sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST"
if [ $? -eq 0 ]; then
    echo "✓ 基本连接成功"
else
    echo "✗ 基本连接失败"
    exit 1
fi

echo ""
echo "2. 查看用户主目录..."
cat > /tmp/test_home.txt << EOF
pwd
ls -la
quit
EOF

sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no -b /tmp/test_home.txt "$REMOTE_USER@$REMOTE_HOST"
rm -f /tmp/test_home.txt

echo ""
echo "3. 尝试访问 /home 目录..."
cat > /tmp/test_home_dir.txt << EOF
cd /home
pwd
ls -la
quit
EOF

sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no -b /tmp/test_home_dir.txt "$REMOTE_USER@$REMOTE_HOST"
rm -f /tmp/test_home_dir.txt

echo ""
echo "4. 尝试访问目标目录..."
cat > /tmp/test_target.txt << EOF
cd $TEST_DIR
pwd
ls -la
quit
EOF

echo "尝试访问: $TEST_DIR"
sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no -b /tmp/test_target.txt "$REMOTE_USER@$REMOTE_HOST"
rm -f /tmp/test_target.txt

echo ""
echo "5. 逐级测试目录..."
for path in "/home" "/home/<USER>" "/home/<USER>/zhuanJiKong-Ewenjian"; do
    echo "测试路径: $path"
    cat > /tmp/test_step.txt << EOF
cd $path
pwd
ls -la
quit
EOF
    sshpass -p "$REMOTE_PASS" sftp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no -b /tmp/test_step.txt "$REMOTE_USER@$REMOTE_HOST"
    echo "----------------------------------------"
done
rm -f /tmp/test_step.txt

echo ""
echo "测试完成！"
echo ""
echo "如果看到 'Couldn't change directory' 或 'No such file or directory' 错误："
echo "1. 目录路径可能不正确"
echo "2. 用户可能没有访问权限"
echo "3. 目录可能不存在"
echo ""
echo "建议："
echo "1. 联系服务器管理员确认正确的目录路径"
echo "2. 确认用户权限设置"
echo "3. 检查目录是否真实存在"
