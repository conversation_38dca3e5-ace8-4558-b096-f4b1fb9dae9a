<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>768</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>历史数据查询工具</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QMainWindow {
    background-color: #f5f5f5;
}

QToolBar {
    background-color: #2c3e50;
    border: none;
    spacing: 3px;
    min-height: 40px;
}

QToolBar QToolButton {
    background-color: transparent;
    color: white;
    border: none;
    padding: 5px;
    margin: 2px;
}

QToolBar QToolButton:hover {
    background-color: #34495e;
    border-radius: 4px;
}

QStatusBar {
    background-color: #ecf0f1;
    color: #2c3e50;
}

QSplitter::handle {
    background-color: #bdc3c7;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}
</string>
  </property>
  <widget class="QWidget" name="centralWidget"/>
  <widget class="QStatusBar" name="statusBar"/>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui> 