#include "xmlconfigreader.h"
#include <QFile>
#include <QDomDocument>
#include <QDebug>

XMLConfigReader* XMLConfigReader::m_instance = nullptr;

XMLConfigReader* XMLConfigReader::instance()
{
    if (!m_instance) {
        m_instance = new XMLConfigReader();
    }
    return m_instance;
}

XMLConfigReader::XMLConfigReader() : m_configLoaded(false)
{
}

XMLConfigReader::~XMLConfigReader()
{
}

bool XMLConfigReader::loadConfig(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开配置文件:" << filePath << "，错误:" << file.errorString();
        return false;
    }

    QDomDocument doc;
    QString errorMsg;
    int errorLine, errorColumn;
    if (!doc.setContent(&file, &errorMsg, &errorLine, &errorColumn)) {
        qDebug() << "解析XML文件失败:" << errorMsg 
                 << "，行:" << errorLine << "，列:" << errorColumn;
        file.close();
        return false;
    }
    file.close();

    // 解析XML内容
    QDomElement root = doc.documentElement();
    if (root.tagName() != "root") {
        qDebug() << "XML根元素不是'root'";
        return false;
    }

    QDomElement dbElement = root.firstChildElement("db");
    if (dbElement.isNull()) {
        qDebug() << "找不到'db'元素";
        return false;
    }

    // 解析paradb配置
    QDomElement paradbElement = dbElement.firstChildElement("paradb");
    if (!paradbElement.isNull()) {
        m_paraDBConfig.name = paradbElement.attribute("name");
        m_paraDBConfig.type = paradbElement.attribute("type");
        m_paraDBConfig.server = paradbElement.attribute("server");
        m_paraDBConfig.user = paradbElement.attribute("user");
        m_paraDBConfig.pwd = paradbElement.attribute("pwd");
        m_paraDBConfig.port = paradbElement.attribute("port").toInt();
    } else {
        qDebug() << "找不到'paradb'元素";
    }

    // 解析hisdb配置
    QDomElement hisdbElement = dbElement.firstChildElement("hisdb");
    if (!hisdbElement.isNull()) {
        m_hisDBConfig.name = hisdbElement.attribute("name");
        m_hisDBConfig.type = hisdbElement.attribute("type");
        m_hisDBConfig.server = hisdbElement.attribute("server");
        m_hisDBConfig.user = hisdbElement.attribute("user");
        m_hisDBConfig.pwd = hisdbElement.attribute("pwd");
        m_hisDBConfig.port = hisdbElement.attribute("port").toInt();
    } else {
        qDebug() << "找不到'hisdb'元素";
    }

    m_configLoaded = true;
    qDebug() << "成功加载XML配置文件";
    return true;
}

DBConfig XMLConfigReader::getParaDBConfig() const
{
    return m_paraDBConfig;
}

DBConfig XMLConfigReader::getHisDBConfig() const
{
    return m_hisDBConfig;
}

bool XMLConfigReader::isParaDBConfigValid() const
{
    return m_configLoaded && 
           !m_paraDBConfig.server.isEmpty() && 
           !m_paraDBConfig.name.isEmpty() &&
           !m_paraDBConfig.user.isEmpty() &&
           m_paraDBConfig.port > 0;
}

bool XMLConfigReader::isHisDBConfigValid() const
{
    return m_configLoaded && 
           !m_hisDBConfig.server.isEmpty() && 
           !m_hisDBConfig.name.isEmpty() &&
           !m_hisDBConfig.user.isEmpty() &&
           m_hisDBConfig.port > 0;
} 