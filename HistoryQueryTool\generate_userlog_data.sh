#!/bin/bash

# 数据库连接信息
DB_HOST="************"
DB_PORT="3306"
DB_USER="root"
DB_PASS="root"
DB_NAME="his_mysql"

# 设置默认记录数
DEFAULT_NUM_RECORDS=100

# 处理命令行参数
if [ $# -gt 0 ]; then
    # 验证是否为数字
    if [[ $1 =~ ^[0-9]+$ ]]; then
        NUM_RECORDS=$1
    else
        echo "错误: 参数必须是数字"
        echo "用法: $0 [记录数量]"
        exit 1
    fi
else
    # 使用默认值
    NUM_RECORDS=$DEFAULT_NUM_RECORDS
    echo "未指定记录数量，将使用默认值: $NUM_RECORDS"
fi

# 用户名数组
USERNAMES=("admin" "operator" "engineer" "supervisor" "user1" "user2" "guest" "system" "maintenance")

# 主机名数组
HOSTNAMES=("SCADA01" "SCADA02" "HMI01" "HMI02" "HISTORIAN" "EMS01" "CLIENT01" "CLIENT02" "CLIENT03")

# 应用名称数组
APP_NAMES=("HistoryQueryTool" "AlarmViewer" "ScadaClient" "ControlPanel" "ConfigManager" "DiagnosticTool")

# 登录类型
LOGTYPES=(0 1 2 3)

echo "正在连接到数据库生成 $NUM_RECORDS 条测试数据..."

# 构建插入语句
INSERT_SQL="INSERT INTO htuserlog (username, starttime, logtype, hostname, appname) VALUES "
VALUES=""

# 计算批次大小，对于大量记录，每批增加更多行
BATCH_SIZE=20
if [ $NUM_RECORDS -gt 1000 ]; then
    BATCH_SIZE=100
elif [ $NUM_RECORDS -gt 500 ]; then
    BATCH_SIZE=50
fi

for ((i=1; i<=$NUM_RECORDS; i++)); do
    # 随机选择数组元素
    USERNAME=${USERNAMES[$((RANDOM % ${#USERNAMES[@]}))]}
    HOSTNAME=${HOSTNAMES[$((RANDOM % ${#HOSTNAMES[@]}))]}
    APP_NAME=${APP_NAMES[$((RANDOM % ${#APP_NAMES[@]}))]}
    LOGTYPE=${LOGTYPES[$((RANDOM % ${#LOGTYPES[@]}))]}
    
    # 随机生成过去30天内的日期时间
    DAYS_AGO=$((RANDOM % 30))
    HOURS_AGO=$((RANDOM % 24))
    MINS_AGO=$((RANDOM % 60))
    
    # 构建值
    if [ -n "$VALUES" ]; then
        VALUES="$VALUES, "
    fi
    VALUES="$VALUES('$USERNAME', NOW() - INTERVAL $DAYS_AGO DAY - INTERVAL $HOURS_AGO HOUR - INTERVAL $MINS_AGO MINUTE, $LOGTYPE, '$HOSTNAME', '$APP_NAME')"
    
    # 每BATCH_SIZE条记录执行一次插入，避免命令行过长
    if [ $((i % BATCH_SIZE)) -eq 0 ] || [ $i -eq $NUM_RECORDS ]; then
        QUERY="$INSERT_SQL $VALUES;"
        mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "$QUERY"
        echo "已插入 $i 条记录..."
        VALUES=""
    fi
done

echo "成功生成 $NUM_RECORDS 条测试数据!"

# 显示记录总数
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "SELECT COUNT(*) AS '总记录数' FROM htuserlog;"

# 显示最近的10条记录
echo "最近10条记录:"
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASS $DB_NAME -e "SELECT id, username, starttime, logtype, hostname, appname FROM htuserlog ORDER BY id DESC LIMIT 10;" 