#ifndef DEVICENAMEMAPPER_H
#define DEVICENAMEMAPPER_H

#include <QObject>
#include <QMap>
#include <QString>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QDateTime>

class DeviceNameMapper : public QObject
{
    Q_OBJECT
public:
    static DeviceNameMapper* instance();
    
    // 初始化连接到paradb数据库
    bool initialize();
    
    // 根据设备ID获取设备名称
    QString getDeviceNameById(int deviceId);
    
    // 刷新设备名称缓存
    void refreshCache();
    
    // 是否已初始化
    bool isInitialized() const { return m_initialized; }
    
    // 获取上次错误
    QString lastError() const { return m_lastError; }
    
    QMap<int, QString> getAllDeviceNames() const;
    
signals:
    void initialized(bool success);
    
private:
    explicit DeviceNameMapper(QObject *parent = nullptr);
    ~DeviceNameMapper();
    
    static DeviceNameMapper *m_instance;
    
    // 缓存设备ID到名称的映射
    QMap<int, QString> m_deviceNameCache;
    
    // 数据库连接
    QSqlDatabase m_paraDb;
    
    // 上次缓存更新时间
    QDateTime m_lastCacheUpdateTime;
    
    // 缓存过期时间（秒）
    int m_cacheExpirationSeconds;
    
    // 初始化状态
    bool m_initialized;
    
    // 上次错误信息
    QString m_lastError;
};

#endif // DEVICENAMEMAPPER_H 