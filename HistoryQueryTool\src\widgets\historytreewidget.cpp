#include "historytreewidget.h"
#include <QHeaderView>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QMessageBox>
#include <QDir>
#include "../common/constants.h"
#include "../utils/xmlconfigreader.h"
#include "../database/dbconnectionfactory.h"
#include "../utils/devicenamemapper.h"

HistoryTreeWidget::HistoryTreeWidget(QWidget *parent) : QTreeWidget(parent)
{
    setupUI();
    
    // 连接信号和槽
    connect(this, &QTreeWidget::itemClicked, this, &HistoryTreeWidget::onItemClicked);
}

void HistoryTreeWidget::setupUI()
{
    // 设置树形控件属性
    setHeaderLabel("浏览树");
    setColumnCount(1);
    header()->setVisible(true);
    setAnimated(true);
    setIndentation(20);
    
    // 创建根节点
    rootItem = new QTreeWidgetItem(this);
    rootItem->setText(0, "历史数据查询");
    rootItem->setExpanded(true);
    
    // 创建子节点
    soeItem = new QTreeWidgetItem(rootItem);
    soeItem->setText(0, "SOE事件查询");
    
    telemetryItem = new QTreeWidgetItem(rootItem);
    telemetryItem->setText(0, "遥信变位查询");
    
    alarmItem = new QTreeWidgetItem(rootItem);
    alarmItem->setText(0, "越限告警查询");
    
    systemEventItem = new QTreeWidgetItem(rootItem);
    systemEventItem->setText(0, "系统事件查询");
    
    operationItem = new QTreeWidgetItem(rootItem);
    operationItem->setText(0, "操作记录查询");
    
    loginItem = new QTreeWidgetItem(rootItem);
    loginItem->setText(0, "用户登录查询");
    
    // 添加新的遥测历史查询选项
    minuteTelemetryItem = new QTreeWidgetItem(rootItem);
    minuteTelemetryItem->setText(0, "分钟级遥测查询");
    
    secondTelemetryItem = new QTreeWidgetItem(rootItem);
    secondTelemetryItem->setText(0, "秒级遥测查询");
}

void HistoryTreeWidget::onItemClicked(QTreeWidgetItem *item, int column)
{
    if (item == soeItem) {
        emit historyTypeSelected(SOE_EVENT);
    } else if (item == telemetryItem) {
        emit historyTypeSelected(YX_CHANGE);
    } else if (item == alarmItem) {
        emit historyTypeSelected(LIMIT_ALARM);
    } else if (item == systemEventItem) {
        emit historyTypeSelected(SYSTEM_EVENT);
    } else if (item == operationItem) {
        emit historyTypeSelected(OPERATION_RECORD);
    } else if (item == loginItem) {
        emit historyTypeSelected(USER_LOGIN);
    } else if (item == minuteTelemetryItem) {
        // 点击了分钟级遥测选项
        emit historyTypeSelected(MINUTE_TELEMETRY);
    } else if (item == secondTelemetryItem) {
        // 点击了秒级遥测选项
        emit historyTypeSelected(SECOND_TELEMETRY);
    } else if (item->parent() == minuteTelemetryItem || item->parent() && item->parent()->parent() == minuteTelemetryItem) {
        // 点击了分钟级遥测下的具体测点
        emit historyTypeSelected(MINUTE_TELEMETRY);
        // 获取设备点位信息
        QVariantMap userData = item->data(0, Qt::UserRole).toMap();
        if (userData.isEmpty() && item->childCount() > 0) {
            // 如果点击的是设备组，则不发送点位信号
            return;
        }
        int deviceId = userData.value("DeviceID").toInt();
        int deviceIndex = userData.value("DeviceIndex").toInt();
        QString name = item->text(0);
        emit telemetryPointSelected(deviceId, deviceIndex, name);
    } else if (item->parent() == secondTelemetryItem || item->parent() && item->parent()->parent() == secondTelemetryItem) {
        // 点击了秒级遥测下的具体测点
        emit historyTypeSelected(SECOND_TELEMETRY);
        // 获取设备点位信息
        QVariantMap userData = item->data(0, Qt::UserRole).toMap();
        if (userData.isEmpty() && item->childCount() > 0) {
            // 如果点击的是设备组，则不发送点位信号
            return;
        }
        int deviceId = userData.value("DeviceID").toInt();
        int deviceIndex = userData.value("DeviceIndex").toInt();
        QString name = item->text(0);
        emit telemetryPointSelected(deviceId, deviceIndex, name);
    }
}

void HistoryTreeWidget::loadTelemetryParameters()
{
    // 清空现有子节点
    clearChildItems(minuteTelemetryItem);
    clearChildItems(secondTelemetryItem);

    // 尝试连接参数库并加载数据
    if (!connectParaDB()) {
        qDebug() << "连接参数库失败";
        return;
    }
}

bool HistoryTreeWidget::connectParaDB()
{
    // 使用XMLConfigReader验证配置
    XMLConfigReader *configReader = XMLConfigReader::instance();
    if (!configReader || !configReader->isParaDBConfigValid()) {
        qDebug() << "参数库配置无效或未加载";
        return false;
    }
    
    // 使用工厂类创建数据库连接
    QSqlDatabase paraDB = DBConnectionFactory::createParaDBConnection("PARA_DB_CONNECTION");
    
    // 连接数据库
    if (!paraDB.open()) {
        qDebug() << "连接参数库失败:" << paraDB.lastError().text();
        return false;
    }
    
    // 加载遥测参数
    loadMinuteTelemetry(paraDB);
    loadSecondTelemetry(paraDB);
    
    // 关闭数据库连接
    paraDB.close();
    return true;
}

void HistoryTreeWidget::loadMinuteTelemetry(QSqlDatabase &db)
{
    QSqlQuery query(db);
    
    // 构建SQL查询，获取AutoCall=1的遥测点，只查询必要的三个字段
    // 按DeviceID和DeviceIndex从小到大排序
    QString sql = QString("SELECT %1, %2, %3 FROM %4 WHERE %5 = 1 ORDER BY %1, %2")
                    .arg(Constants::Fields::ParaYC::DEVICE_ID)
                    .arg(Constants::Fields::ParaYC::DEVICE_INDEX)
                    .arg(Constants::Fields::ParaYC::NAME)
                    .arg(Constants::Tables::PARA_YC)
                    .arg(Constants::Fields::ParaYC::AUTO_CALL);
    
    if (!query.exec(sql)) {
        qDebug() << "查询分钟级遥测参数失败:" << query.lastError().text();
        return;
    }
    
    // 创建设备ID映射表，用于组织树结构
    QMap<int, QTreeWidgetItem*> deviceItems;
    
    // 处理查询结果
    while (query.next()) {
        int deviceId = query.value(0).toInt();
        int deviceIndex = query.value(1).toInt();
        QString name = query.value(2).toString();
        
        // 使用设备ID作为分组
        if (!deviceItems.contains(deviceId)) {
            // 创建设备节点
            QTreeWidgetItem *deviceItem = new QTreeWidgetItem(minuteTelemetryItem);
            // 获取设备名称
            QString deviceName = DeviceNameMapper::instance()->getDeviceNameById(deviceId);
            deviceItem->setText(0, QString("%1 (ID:%2)").arg(deviceName).arg(deviceId));
            deviceItems[deviceId] = deviceItem;
        }
        
        // 创建测点子节点，显示格式：[DeviceIndex] 名称
        QTreeWidgetItem *pointItem = new QTreeWidgetItem(deviceItems[deviceId]);
        pointItem->setText(0, QString("[%1] %2").arg(deviceIndex).arg(name));
        
        // 存储DeviceID和DeviceIndex为用户数据，便于后续查询
        QVariantMap userData;
        userData["DeviceID"] = deviceId;
        userData["DeviceIndex"] = deviceIndex;
        pointItem->setData(0, Qt::UserRole, userData);
    }
}

void HistoryTreeWidget::loadSecondTelemetry(QSqlDatabase &db)
{
    QSqlQuery query(db);
    
    // 构建SQL查询，获取Locked=1的遥测点，只查询必要的三个字段
    // 按DeviceID和DeviceIndex从小到大排序
    QString sql = QString("SELECT %1, %2, %3 FROM %4 WHERE %5 = 1 ORDER BY %1, %2")
                    .arg(Constants::Fields::ParaYC::DEVICE_ID)
                    .arg(Constants::Fields::ParaYC::DEVICE_INDEX)
                    .arg(Constants::Fields::ParaYC::NAME)
                    .arg(Constants::Tables::PARA_YC)
                    .arg(Constants::Fields::ParaYC::LOCKED);
    
    if (!query.exec(sql)) {
        qDebug() << "查询秒级遥测参数失败:" << query.lastError().text();
        return;
    }
    
    // 创建设备ID映射表，用于组织树结构
    QMap<int, QTreeWidgetItem*> deviceItems;
    
    // 处理查询结果
    while (query.next()) {
        int deviceId = query.value(0).toInt();
        int deviceIndex = query.value(1).toInt();
        QString name = query.value(2).toString();
        
        // 使用设备ID作为分组
        if (!deviceItems.contains(deviceId)) {
            // 创建设备节点
            QTreeWidgetItem *deviceItem = new QTreeWidgetItem(secondTelemetryItem);
            // 获取设备名称
            QString deviceName = DeviceNameMapper::instance()->getDeviceNameById(deviceId);
            deviceItem->setText(0, QString("%1 (ID:%2)").arg(deviceName).arg(deviceId));
            deviceItems[deviceId] = deviceItem;
        }
        
        // 创建测点子节点，显示格式：[DeviceIndex] 名称
        QTreeWidgetItem *pointItem = new QTreeWidgetItem(deviceItems[deviceId]);
        pointItem->setText(0, QString("[%1] %2").arg(deviceIndex).arg(name));
        
        // 存储DeviceID和DeviceIndex为用户数据，便于后续查询
        QVariantMap userData;
        userData["DeviceID"] = deviceId;
        userData["DeviceIndex"] = deviceIndex;
        pointItem->setData(0, Qt::UserRole, userData);
        
        // 设置测点可选中
        pointItem->setFlags(pointItem->flags() | Qt::ItemIsSelectable);
    }
    
    qDebug() << "成功加载秒级遥测参数，设备数量:" << deviceItems.size();
}

void HistoryTreeWidget::clearChildItems(QTreeWidgetItem *parentItem)
{
    if (!parentItem) return;
    
    // 记录子项数量
    int count = parentItem->childCount();
    
    // 从后向前删除子项
    for (int i = count - 1; i >= 0; i--) {
        QTreeWidgetItem *child = parentItem->takeChild(i);
        delete child;
    }
}

void HistoryTreeWidget::setCurrentType(int type)
{
    QTreeWidgetItem *targetItem = nullptr;
    
    switch (type) {
        case SOE_EVENT:
            targetItem = soeItem;
            break;
        case YX_CHANGE:
            targetItem = telemetryItem;
            break;
        case LIMIT_ALARM:
            targetItem = alarmItem;
            break;
        case SYSTEM_EVENT:
            targetItem = systemEventItem;
            break;
        case OPERATION_RECORD:
            targetItem = operationItem;
            break;
        case USER_LOGIN:
            targetItem = loginItem;
            break;
        case MINUTE_TELEMETRY:
            targetItem = minuteTelemetryItem;
            break;
        case SECOND_TELEMETRY:
            targetItem = secondTelemetryItem;
            break;
        default:
            return;
    }
    
    if (targetItem) {
        setCurrentItem(targetItem);
        emit historyTypeSelected(type);
    }
} 