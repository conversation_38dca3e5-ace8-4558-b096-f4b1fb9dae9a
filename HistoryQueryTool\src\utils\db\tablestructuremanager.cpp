#include "tablestructuremanager.h"
#include <QDebug>
#include <QSqlQuery>
#include <QSqlError>
#include <QSqlRecord>

// 静态常量定义
const QString TableStructureManager::HOSTNAME_FIELD = "HOSTNAME";
const QString TableStructureManager::DEVICE_FIELD = "EQUIPNAME";

TableStructureManager::TableStructureManager(QObject *parent)
    : QObject(parent)
{
    // 初始化需要检查的表列表
    m_tables << "htsoelog"       // 保护事件历史数据
             << "htcoslog"       // 遥信变位历史数据
             << "htbreaklimitlog" // 越限告警历史数据
             << "syserrorinfo"   // 系统事件历史数据
             << "htoperationlog" // 操作记录历史数据
             << "htuserlog";     // 用户登录历史数据
}

bool TableStructureManager::initialize(const QSqlDatabase &db)
{
    if (!db.isOpen()) {
        qWarning() << "数据库未打开，无法初始化表结构管理器";
        return false;
    }

    return checkTableStructure(db);
}

bool TableStructureManager::hasField(const QString &tableName, const QString &fieldName) const
{
    if (!m_tableInfoMap.contains(tableName)) {
        qWarning() << "表" << tableName << "不存在";
        return false;
    }

    return m_tableInfoMap[tableName].fields.contains(fieldName, Qt::CaseInsensitive);
}

QStringList TableStructureManager::getTableFields(const QString &tableName) const
{
    if (!m_tableInfoMap.contains(tableName)) {
        qWarning() << "表" << tableName << "不存在";
        return QStringList();
    }

    return m_tableInfoMap[tableName].fields;
}

bool TableStructureManager::shouldLockHostname(const QString &tableName) const
{
    if (!m_tableInfoMap.contains(tableName)) {
        qWarning() << "表" << tableName << "不存在";
        return true; // 默认锁定
    }

    return !m_tableInfoMap[tableName].hasHostnameField;
}

bool TableStructureManager::shouldLockDevice(const QString &tableName) const
{
    if (!m_tableInfoMap.contains(tableName)) {
        qWarning() << "表" << tableName << "不存在";
        return true; // 默认锁定
    }

    return !m_tableInfoMap[tableName].hasDeviceField;
}

QStringList TableStructureManager::getTableNames() const
{
    return m_tableInfoMap.keys();
}

bool TableStructureManager::checkTableStructure(const QSqlDatabase &db)
{
    m_tableInfoMap.clear();

    for (const QString &tableName : m_tables) {
        QSqlQuery query(db);
        QString sql = QString("SHOW COLUMNS FROM %1").arg(tableName);
        
        if (!query.exec(sql)) {
            qWarning() << "查询表" << tableName << "结构失败:" << query.lastError().text();
            continue;
        }

        TableInfo tableInfo;
        tableInfo.hasHostnameField = false;
        tableInfo.hasDeviceField = false;

        while (query.next()) {
            QString fieldName = query.value(0).toString().toUpper();
            tableInfo.fields << fieldName;

            if (fieldName == HOSTNAME_FIELD) {
                tableInfo.hasHostnameField = true;
            } else if (fieldName == DEVICE_FIELD) {
                tableInfo.hasDeviceField = true;
            }
        }

        m_tableInfoMap[tableName] = tableInfo;

        qDebug() << "表" << tableName << "结构检查完成:";
        qDebug() << "  主机名字段:" << (tableInfo.hasHostnameField ? "存在" : "不存在");
        qDebug() << "  设备字段:" << (tableInfo.hasDeviceField ? "存在" : "不存在");
    }

    return !m_tableInfoMap.isEmpty();
} 