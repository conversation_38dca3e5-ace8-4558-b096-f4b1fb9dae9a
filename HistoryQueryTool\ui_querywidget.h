/********************************************************************************
** Form generated from reading UI file 'querywidget.ui'
**
** Created by: Qt User Interface Compiler version 5.9.2
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_QUERYWIDGET_H
#define UI_QUERYWIDGET_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDateEdit>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTableView>
#include <QtWidgets/QTimeEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_QueryWidget
{
public:
    QVBoxLayout *verticalLayout;
    QLabel *titleLabel;
    QGroupBox *queryConditionGroup;
    QGridLayout *gridLayout;
    QLabel *stationLabel;
    QComboBox *stationComboBox;
    QLabel *bayLabel;
    QComboBox *bayComboBox;
    QLabel *deviceLabel;
    QComboBox *deviceComboBox;
    QLabel *signalFilterLabel;
    QLineEdit *signalFilterEdit;
    QLabel *startTimeLabel;
    QDateEdit *startDateEdit;
    QTimeEdit *startTimeEdit;
    QLabel *endTimeLabel;
    QDateEdit *endDateEdit;
    QTimeEdit *endTimeEdit;
    QLabel *pageInfoLabel;
    QPushButton *queryButton;
    QTableView *resultTableView;

    void setupUi(QWidget *QueryWidget)
    {
        if (QueryWidget->objectName().isEmpty())
            QueryWidget->setObjectName(QStringLiteral("QueryWidget"));
        QueryWidget->resize(800, 600);
        verticalLayout = new QVBoxLayout(QueryWidget);
        verticalLayout->setObjectName(QStringLiteral("verticalLayout"));
        titleLabel = new QLabel(QueryWidget);
        titleLabel->setObjectName(QStringLiteral("titleLabel"));
        QFont font;
        font.setPointSize(12);
        font.setBold(true);
        font.setWeight(75);
        titleLabel->setFont(font);
        titleLabel->setAlignment(Qt::AlignCenter);

        verticalLayout->addWidget(titleLabel);

        queryConditionGroup = new QGroupBox(QueryWidget);
        queryConditionGroup->setObjectName(QStringLiteral("queryConditionGroup"));
        gridLayout = new QGridLayout(queryConditionGroup);
        gridLayout->setObjectName(QStringLiteral("gridLayout"));
        stationLabel = new QLabel(queryConditionGroup);
        stationLabel->setObjectName(QStringLiteral("stationLabel"));

        gridLayout->addWidget(stationLabel, 0, 0, 1, 1);

        stationComboBox = new QComboBox(queryConditionGroup);
        stationComboBox->setObjectName(QStringLiteral("stationComboBox"));

        gridLayout->addWidget(stationComboBox, 0, 1, 1, 1);

        bayLabel = new QLabel(queryConditionGroup);
        bayLabel->setObjectName(QStringLiteral("bayLabel"));

        gridLayout->addWidget(bayLabel, 0, 2, 1, 1);

        bayComboBox = new QComboBox(queryConditionGroup);
        bayComboBox->setObjectName(QStringLiteral("bayComboBox"));

        gridLayout->addWidget(bayComboBox, 0, 3, 1, 1);

        deviceLabel = new QLabel(queryConditionGroup);
        deviceLabel->setObjectName(QStringLiteral("deviceLabel"));

        gridLayout->addWidget(deviceLabel, 0, 4, 1, 1);

        deviceComboBox = new QComboBox(queryConditionGroup);
        deviceComboBox->setObjectName(QStringLiteral("deviceComboBox"));

        gridLayout->addWidget(deviceComboBox, 0, 5, 1, 1);

        signalFilterLabel = new QLabel(queryConditionGroup);
        signalFilterLabel->setObjectName(QStringLiteral("signalFilterLabel"));

        gridLayout->addWidget(signalFilterLabel, 1, 0, 1, 1);

        signalFilterEdit = new QLineEdit(queryConditionGroup);
        signalFilterEdit->setObjectName(QStringLiteral("signalFilterEdit"));

        gridLayout->addWidget(signalFilterEdit, 1, 1, 1, 5);

        startTimeLabel = new QLabel(queryConditionGroup);
        startTimeLabel->setObjectName(QStringLiteral("startTimeLabel"));

        gridLayout->addWidget(startTimeLabel, 2, 0, 1, 1);

        startDateEdit = new QDateEdit(queryConditionGroup);
        startDateEdit->setObjectName(QStringLiteral("startDateEdit"));
        startDateEdit->setCalendarPopup(true);

        gridLayout->addWidget(startDateEdit, 2, 1, 1, 1);

        startTimeEdit = new QTimeEdit(queryConditionGroup);
        startTimeEdit->setObjectName(QStringLiteral("startTimeEdit"));

        gridLayout->addWidget(startTimeEdit, 2, 2, 1, 1);

        endTimeLabel = new QLabel(queryConditionGroup);
        endTimeLabel->setObjectName(QStringLiteral("endTimeLabel"));

        gridLayout->addWidget(endTimeLabel, 2, 3, 1, 1);

        endDateEdit = new QDateEdit(queryConditionGroup);
        endDateEdit->setObjectName(QStringLiteral("endDateEdit"));
        endDateEdit->setCalendarPopup(true);

        gridLayout->addWidget(endDateEdit, 2, 4, 1, 1);

        endTimeEdit = new QTimeEdit(queryConditionGroup);
        endTimeEdit->setObjectName(QStringLiteral("endTimeEdit"));

        gridLayout->addWidget(endTimeEdit, 2, 5, 1, 1);

        pageInfoLabel = new QLabel(queryConditionGroup);
        pageInfoLabel->setObjectName(QStringLiteral("pageInfoLabel"));

        gridLayout->addWidget(pageInfoLabel, 3, 0, 1, 5);

        queryButton = new QPushButton(queryConditionGroup);
        queryButton->setObjectName(QStringLiteral("queryButton"));

        gridLayout->addWidget(queryButton, 3, 5, 1, 1);


        verticalLayout->addWidget(queryConditionGroup);

        resultTableView = new QTableView(QueryWidget);
        resultTableView->setObjectName(QStringLiteral("resultTableView"));
        resultTableView->setAlternatingRowColors(true);
        resultTableView->setSelectionBehavior(QAbstractItemView::SelectRows);

        verticalLayout->addWidget(resultTableView);


        retranslateUi(QueryWidget);

        QMetaObject::connectSlotsByName(QueryWidget);
    } // setupUi

    void retranslateUi(QWidget *QueryWidget)
    {
        QueryWidget->setWindowTitle(QApplication::translate("QueryWidget", "Form", Q_NULLPTR));
        titleLabel->setText(QApplication::translate("QueryWidget", "\345\216\206\345\217\262\346\225\260\346\215\256\346\237\245\350\257\242", Q_NULLPTR));
        queryConditionGroup->setTitle(QApplication::translate("QueryWidget", "\346\237\245\350\257\242\346\235\241\344\273\266", Q_NULLPTR));
        stationLabel->setText(QApplication::translate("QueryWidget", "\344\270\273\346\234\272\345\220\215:", Q_NULLPTR));
        bayLabel->setText(QApplication::translate("QueryWidget", "\344\272\213\344\273\266\347\261\273\345\236\213:", Q_NULLPTR));
        deviceLabel->setText(QApplication::translate("QueryWidget", "\350\256\276\345\244\207:", Q_NULLPTR));
        signalFilterLabel->setText(QApplication::translate("QueryWidget", "\344\277\241\345\217\267\350\277\207\346\273\244:", Q_NULLPTR));
        startTimeLabel->setText(QApplication::translate("QueryWidget", "\350\265\267\345\247\213\346\227\266\351\227\264:", Q_NULLPTR));
        endTimeLabel->setText(QApplication::translate("QueryWidget", "\347\273\223\346\235\237\346\227\266\351\227\264:", Q_NULLPTR));
        pageInfoLabel->setText(QApplication::translate("QueryWidget", "\347\254\254 0/0 \351\241\265", Q_NULLPTR));
        queryButton->setText(QApplication::translate("QueryWidget", "\346\237\245\350\257\242", Q_NULLPTR));
    } // retranslateUi

};

namespace Ui {
    class QueryWidget: public Ui_QueryWidget {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_QUERYWIDGET_H
