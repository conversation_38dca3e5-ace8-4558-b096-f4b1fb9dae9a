# SFTP自动下载脚本使用说明

## 功能概述

这个脚本用于自动从远程SFTP服务器下载指定类型的最新文件，支持：

- 每分钟自动执行下载任务
- 下载指定后缀的文件：`DQ.WPD`、`QXZ.WPD`、`NBQ.WPD`、`THEROY.WPD`、`CDQ.WPD`、`NWP.WPD`
- 避免重复下载（通过日志记录）
- 支持多个目录映射
- 详细的下载日志记录

## 配置说明

### 1. 服务器配置

编辑脚本中的以下配置：

```bash
# SFTP服务器配置
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_username"        # 请替换为实际用户名
REMOTE_PASS="your_password"        # 请替换为实际密码
```

### 2. 目录映射配置

```bash
# 目录映射配置（远程目录:本地目录）
declare -A DIR_MAPPINGS=(
    ["/home/<USER>/zhuanJiKong-<PERSON><PERSON>ji<PERSON>/"]="/home/<USER>/qsjglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian2/"]="/home/<USER>/djglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian3/"]="/home/<USER>/jcglyc/"
)
```

### 3. 文件类型配置

```bash
# 文件类型配置 - 需要下载的文件后缀
FILE_EXTENSIONS=("DQ.WPD" "QXZ.WPD" "NBQ.WPD" "THEROY.WPD" "CDQ.WPD" "NWP.WPD")
```

## 安装和使用

### 1. 准备工作

```bash
# 给脚本执行权限
chmod +x sftp_auto_download.sh

# 安装依赖（如果需要）
sudo apt-get install openssh-client sshpass  # Ubuntu/Debian
# 或
sudo yum install openssh-clients sshpass     # CentOS/RHEL
```

### 2. 测试连接

```bash
# 测试SFTP连接
./sftp_auto_download.sh --test
```

### 3. 手动执行下载

```bash
# 手动执行一次下载任务
./sftp_auto_download.sh --run
```

### 4. 安装为系统服务（每分钟自动执行）

```bash
# 安装为systemd服务
sudo ./sftp_auto_download.sh --install
```

### 5. 查看状态和日志

```bash
# 查看服务状态
./sftp_auto_download.sh --status

# 查看下载历史
./sftp_auto_download.sh --history

# 查看系统日志
sudo journalctl -u sftp-auto-download.service -f
```

### 6. 卸载服务

```bash
# 卸载服务
sudo ./sftp_auto_download.sh --uninstall
```

## 日志说明

### 系统日志
- 位置：`/var/log/sftp_auto_download.log`
- 内容：脚本运行日志、错误信息等

### 下载历史日志
- 位置：`/home/<USER>/sftplog/download_history.log`
- 格式：`时间|远程路径|本地路径|文件名|文件大小`
- 用途：记录已下载文件，避免重复下载

## 工作原理

1. **文件检测**：脚本连接到远程SFTP服务器，获取指定目录的文件列表
2. **类型筛选**：筛选出指定后缀的文件
3. **重复检查**：检查下载历史日志，避免重复下载
4. **最新文件**：对于每种文件类型，选择最新的未下载文件
5. **执行下载**：下载文件到对应的本地目录
6. **记录日志**：记录下载信息到历史日志

## 故障排除

### 1. 连接失败
- 检查网络连接
- 验证服务器地址、端口、用户名、密码
- 确认SFTP服务正常运行

### 2. 权限问题
- 确保本地目录有写权限
- 确保远程用户有读权限

### 3. 依赖缺失
```bash
# 检查并安装依赖
which sftp ssh sshpass
```

### 4. 服务未启动
```bash
# 检查服务状态
systemctl status sftp-auto-download.timer
systemctl status sftp-auto-download.service

# 重启服务
sudo systemctl restart sftp-auto-download.timer
```

## 命令参考

```bash
./sftp_auto_download.sh --help      # 显示帮助信息
./sftp_auto_download.sh --test      # 测试连接
./sftp_auto_download.sh --run       # 手动执行下载
./sftp_auto_download.sh --status    # 显示服务状态
./sftp_auto_download.sh --history   # 显示下载历史
sudo ./sftp_auto_download.sh --install    # 安装服务
sudo ./sftp_auto_download.sh --uninstall  # 卸载服务
```

## 注意事项

1. 首次使用前请修改脚本中的服务器配置信息
2. 确保本地目录存在且有写权限
3. 建议先手动测试连接和下载功能
4. 定期检查日志文件大小，必要时进行清理
5. 如果使用SSH密钥认证，请取消注释并设置密钥文件路径
