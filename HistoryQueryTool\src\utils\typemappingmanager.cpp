#include "typemappingmanager.h"
#include <QDebug>

// 初始化静态成员
TypeMappingManager* TypeMappingManager::m_instance = nullptr;

/**
 * @brief 获取TypeMappingManager的单例实例
 * @return TypeMappingManager单例实例的指针
 */
TypeMappingManager* TypeMappingManager::instance()
{
    if (!m_instance) {
        m_instance = new TypeMappingManager();
    }
    return m_instance;
}

/**
 * @brief 构造函数
 * @param parent 父对象
 */
TypeMappingManager::TypeMappingManager(QObject* parent)
    : QObject(parent)
{
    initializeMappings();
}

/**
 * @brief 初始化所有映射关系
 */
void TypeMappingManager::initializeMappings()
{
    // 清空现有映射
    m_nameToValueMaps.clear();
    m_valueToNameMaps.clear();
    
    // 1. VALUETYPE映射关系
    QMap<QString, QString> valueTypeNameToValue;
    QMap<QString, QString> valueTypeValueToName;
    
    valueTypeNameToValue["状态变位"] = "0";
    valueTypeNameToValue["告警"] = "1";
    valueTypeNameToValue["事故"] = "2";
    valueTypeNameToValue["人工置数"] = "3";
    valueTypeNameToValue["类型4"] = "4";
    valueTypeNameToValue["类型5"] = "5";
    
    // 构建反向映射
    for (auto it = valueTypeNameToValue.begin(); it != valueTypeNameToValue.end(); ++it) {
        valueTypeValueToName[it.value()] = it.key();
    }
    
    // 存储VALUETYPE映射
    m_nameToValueMaps["VALUETYPE"] = valueTypeNameToValue;
    m_valueToNameMaps["VALUETYPE"] = valueTypeValueToName;
    
    // 2. ERRTYPE映射关系
    QMap<QString, QString> errTypeNameToValue;
    QMap<QString, QString> errTypeValueToName;
    
    errTypeNameToValue["未知错误"] = "0";
    errTypeNameToValue["通信类"] = "1";
    errTypeNameToValue["硬件类"] = "2";
    errTypeNameToValue["软件异常"] = "3";
    errTypeNameToValue["配置错误"] = "4";
    
    // 构建反向映射
    for (auto it = errTypeNameToValue.begin(); it != errTypeNameToValue.end(); ++it) {
        errTypeValueToName[it.value()] = it.key();
    }
    
    // 存储ERRTYPE映射
    m_nameToValueMaps["ERRTYPE"] = errTypeNameToValue;
    m_valueToNameMaps["ERRTYPE"] = errTypeValueToName;
    
    // 3. OPERTYPE映射关系
    QMap<QString, QString> operTypeNameToValue;
    QMap<QString, QString> operTypeValueToName;
    
    operTypeNameToValue["未知操作"] = "0";
    operTypeNameToValue["遥控预置"] = "1";
    operTypeNameToValue["遥控执行"] = "2";
    operTypeNameToValue["遥调设置"] = "3";
    operTypeNameToValue["参数修改"] = "4";
    
    // 构建反向映射
    for (auto it = operTypeNameToValue.begin(); it != operTypeNameToValue.end(); ++it) {
        operTypeValueToName[it.value()] = it.key();
    }
    
    // 存储OPERTYPE映射
    m_nameToValueMaps["OPERTYPE"] = operTypeNameToValue;
    m_valueToNameMaps["OPERTYPE"] = operTypeValueToName;
    
    // 4. BREAKLIMITTYPE映射关系
    QMap<QString, QString> limitTypeNameToValue;
    QMap<QString, QString> limitTypeValueToName;
    
    limitTypeNameToValue["上限越限"] = "1";
    limitTypeNameToValue["下限越限"] = "2";
    limitTypeNameToValue["突变越限"] = "3";
    
    // 构建反向映射
    for (auto it = limitTypeNameToValue.begin(); it != limitTypeNameToValue.end(); ++it) {
        limitTypeValueToName[it.value()] = it.key();
    }
    
    // 存储BREAKLIMITTYPE映射
    m_nameToValueMaps["BREAKLIMITTYPE"] = limitTypeNameToValue;
    m_valueToNameMaps["BREAKLIMITTYPE"] = limitTypeValueToName;
    
    // 5. LOGTYPE映射关系
    QMap<QString, QString> logTypeNameToValue;
    QMap<QString, QString> logTypeValueToName;
    
    logTypeNameToValue["登录"] = "1";
    logTypeNameToValue["注销"] = "2";
    logTypeNameToValue["超时自动登出"] = "3";
    logTypeNameToValue["密码错误"] = "4";
    logTypeNameToValue["权限变更"] = "5";
    logTypeNameToValue["权限不足"] = "6";
    logTypeNameToValue["密码修改"] = "7";
    logTypeNameToValue["账户删除"] = "8";
    logTypeNameToValue["账户创建"] = "9";
    
    // 构建反向映射
    for (auto it = logTypeNameToValue.begin(); it != logTypeNameToValue.end(); ++it) {
        logTypeValueToName[it.value()] = it.key();
    }
    
    // 存储LOGTYPE映射
    m_nameToValueMaps["LOGTYPE"] = logTypeNameToValue;
    m_valueToNameMaps["LOGTYPE"] = logTypeValueToName;
    
    qDebug() << "类型映射初始化完成";
}

/**
 * @brief 根据类型字段名和值获取对应的显示名称
 * @param fieldName 类型字段名(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)
 * @param value 数据库中存储的值(通常是数字)
 * @return 对应的显示名称
 */
QString TypeMappingManager::getNameByValue(const QString& fieldName, const QString& value)
{
    // 转换字段名 - 将中文字段名映射到对应的英文字段名
    QString mappedFieldName = fieldName;
    
    // 处理中文字段名
    if (fieldName == "事件类型") {
        mappedFieldName = "VALUETYPE";
    } else if (fieldName == "错误类型") {
        mappedFieldName = "ERRTYPE";
    } else if (fieldName == "操作类型") {
        mappedFieldName = "OPERTYPE";
    } else if (fieldName == "越限类型") {
        mappedFieldName = "BREAKLIMITTYPE";
    } else if (fieldName == "日志类型") {
        mappedFieldName = "LOGTYPE";
    } else {
        // 如果不是中文字段名，转换为大写进行比较
        mappedFieldName = fieldName.toUpper();
    }
    
    // 调试输出
    qDebug() << "字段名映射:" << fieldName << "->" << mappedFieldName;
    
    // 检查是否有该字段的映射
    if (!m_valueToNameMaps.contains(mappedFieldName)) {
        qDebug() << "未找到字段映射:" << mappedFieldName;
        return value; // 没有映射，返回原值
    }
    
    // 获取该字段的值到名称映射
    const QMap<QString, QString>& valueToNameMap = m_valueToNameMaps[mappedFieldName];
    
    // 查找值对应的名称
    if (valueToNameMap.contains(value)) {
        QString result = valueToNameMap[value];
        qDebug() << "映射值:" << value << "->" << result;
        return result;
    }
    
    // 没有找到映射，返回原值或生成一个默认名称
    if (mappedFieldName == "BREAKLIMITTYPE" || 
        mappedFieldName == "VALUETYPE" || 
        mappedFieldName == "ERRTYPE" || 
        mappedFieldName == "OPERTYPE" ||
        mappedFieldName == "LOGTYPE") {
        QString result = QString("类型%1").arg(value);
        qDebug() << "生成默认名称:" << value << "->" << result;
        return result;
    }
    
    qDebug() << "未找到值映射，返回原值:" << value;
    return value;
}

/**
 * @brief 根据类型字段名和显示名称获取对应的数据库存储值
 * @param fieldName 类型字段名(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)
 * @param name 显示名称
 * @return 对应的数据库存储值
 */
QString TypeMappingManager::getValueByName(const QString& fieldName, const QString& name)
{
    // 转换字段名 - 将中文字段名映射到对应的英文字段名
    QString mappedFieldName = fieldName;
    
    // 处理中文字段名
    if (fieldName == "事件类型") {
        mappedFieldName = "VALUETYPE";
    } else if (fieldName == "错误类型") {
        mappedFieldName = "ERRTYPE";
    } else if (fieldName == "操作类型") {
        mappedFieldName = "OPERTYPE";
    } else if (fieldName == "越限类型") {
        mappedFieldName = "BREAKLIMITTYPE";
    } else if (fieldName == "日志类型") {
        mappedFieldName = "LOGTYPE";
    } else {
        // 如果不是中文字段名，转换为大写进行比较
        mappedFieldName = fieldName.toUpper();
    }
    
    // 检查是否有该字段的映射
    if (!m_nameToValueMaps.contains(mappedFieldName)) {
        return name; // 没有映射，返回原名称
    }
    
    // 获取该字段的名称到值映射
    const QMap<QString, QString>& nameToValueMap = m_nameToValueMaps[mappedFieldName];
    
    // 查找名称对应的值
    if (nameToValueMap.contains(name)) {
        return nameToValueMap[name];
    }
    
    // 没有找到映射，尝试解析"类型X"形式的名称
    QRegExp typeRegex("类型(\\d+)");
    if (typeRegex.exactMatch(name)) {
        return typeRegex.cap(1); // 返回类型号
    }
    
    return name; // 如果都不匹配，返回原名称
}

/**
 * @brief 获取指定类型字段的所有显示名称列表
 * @param fieldName 类型字段名(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)
 * @return 显示名称列表
 */
QStringList TypeMappingManager::getAllNames(const QString& fieldName)
{
    // 转换字段名 - 将中文字段名映射到对应的英文字段名
    QString mappedFieldName = fieldName;
    
    // 处理中文字段名
    if (fieldName == "事件类型") {
        mappedFieldName = "VALUETYPE";
    } else if (fieldName == "错误类型") {
        mappedFieldName = "ERRTYPE";
    } else if (fieldName == "操作类型") {
        mappedFieldName = "OPERTYPE";
    } else if (fieldName == "越限类型") {
        mappedFieldName = "BREAKLIMITTYPE";
    } else if (fieldName == "日志类型") {
        mappedFieldName = "LOGTYPE";
    } else {
        // 如果不是中文字段名，转换为大写进行比较
        mappedFieldName = fieldName.toUpper();
    }
    
    // 检查是否有该字段的映射
    if (!m_nameToValueMaps.contains(mappedFieldName)) {
        return QStringList(); // 没有映射，返回空列表
    }
    
    // 获取该字段的名称到值映射
    const QMap<QString, QString>& nameToValueMap = m_nameToValueMaps[mappedFieldName];
    
    // 提取所有名称
    return nameToValueMap.keys();
}

/**
 * @brief 判断给定的字段名是否是支持的类型映射字段
 * @param fieldName 字段名
 * @return 是否支持类型映射
 */
bool TypeMappingManager::isMappedTypeField(const QString& fieldName)
{
    QString fieldNameUpper = fieldName.toUpper();
    
    // 添加调试输出
    bool result = fieldNameUpper == "VALUETYPE" || 
           fieldNameUpper == "ERRTYPE" || 
           fieldNameUpper == "OPERTYPE" || 
           fieldNameUpper == "BREAKLIMITTYPE" ||
           fieldNameUpper == "LOGTYPE" ||
           // 添加中文字段名映射
           fieldName == "事件类型" || // 对应VALUETYPE
           fieldName == "错误类型" || // 对应ERRTYPE
           fieldName == "操作类型" || // 对应OPERTYPE
           fieldName == "越限类型" || // 对应BREAKLIMITTYPE
           fieldName == "日志类型";   // 对应LOGTYPE
    
 
    return result;
} 