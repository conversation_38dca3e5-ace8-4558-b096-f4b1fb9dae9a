#ifndef TABLEMANAGER_H
#define TABLEMANAGER_H

#include <QObject>
#include <QTableView>
#include <QSqlQueryModel>
#include <QMap>
#include <QStringList>

/**
 * @brief 表格管理类
 * 负责表格视图的显示、表头设置和导出功能
 */
class TableManager : public QObject
{
    Q_OBJECT
public:
    explicit TableManager(QObject *parent = nullptr);
    
    /**
     * @brief 设置表格视图
     * @param tableView 表格视图控件指针
     */
    void setTableView(QTableView *tableView);
    
    /**
     * @brief 设置表头
     * @param tableName 表名
     * @param model 查询模型
     */
    void setTableHeaders(const QString &tableName, QSqlQueryModel *model);
    
    /**
     * @brief 更新表格显示
     * @param model 查询模型
     * @param resultCount 结果数量
     */
    void updateTableDisplay(QSqlQueryModel *model, int resultCount);
    
    /**
     * @brief 清空表格
     * 在查询结果为空时调用，清空表格内容和表头
     */
    void clearTable();
    
    /**
     * @brief 导出查询结果到CSV文件
     * @param fileName 文件名
     * @param model 查询模型
     * @return 是否导出成功
     */
    bool exportToCSV(const QString &fileName, QSqlQueryModel *model);
    
    /**
     * @brief 打印查询结果
     * @param model 查询模型
     */
    void printResults(QSqlQueryModel *model);
    
    /**
     * @brief 获取特定表的表头
     * @param tableName 表名
     * @return 表头列表
     */
    QStringList getTableHeaders(const QString &tableName) const;

signals:
    /**
     * @brief 结果计数信号
     * @param count 结果数量
     */
    void resultCounted(int count);
    
    /**
     * @brief 消息提示信号
     * @param title 标题
     * @param message 消息内容
     */
    void messageNotification(const QString &title, const QString &message);
    
private:
    // 表格视图
    QTableView *m_tableView;
    
    // 表头配置
    mutable QMap<QString, QStringList> m_tableHeaders;
    
    // 初始化表头配置
    void initTableHeaders() const;
};

#endif // TABLEMANAGER_H 