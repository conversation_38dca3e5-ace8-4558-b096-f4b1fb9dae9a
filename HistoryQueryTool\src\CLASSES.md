# 历史查询工具类设计说明

## 设计原则

历史查询工具基于以下设计原则进行类设计：

1. **单一职责原则**: 每个类只负责一项核心功能
2. **开放/封闭原则**: 类的设计对扩展开放，对修改封闭
3. **依赖倒置原则**: 高层模块不依赖低层模块，都依赖抽象
4. **接口隔离原则**: 一个类不应强制依赖它不使用的方法
5. **组合优于继承**: 优先使用组合而非继承实现功能复用

## 核心类详细说明

### QueryWidget (查询窗口基类)

**位置**: `widgets/querywidget.h/cpp`

**职责**:
- 提供基础UI框架
- 定义所有查询窗口共有的接口
- 提供模型创建和管理功能
- 处理基本的分页和数据显示

**主要接口**:
- `setDBManager`: 设置数据库管理器
- `executeQuery`: 执行查询（虚函数）
- `refreshData`: 刷新数据（虚函数）
- `buildQuerySQL`: 构建SQL语句（虚函数）
- `model`: 获取数据模型

### SOEHistoryWidget (历史数据查询窗口)

**位置**: `widgets/soehistorywidget.h/cpp`

**职责**:
- 协调过滤器、数据提供者和表格管理器
- 处理特定类型历史数据的查询
- 管理UI交互和查询结果显示

**关键组件**:
- `FilterManager`: 管理各种过滤条件
- `HistoryDataProvider`: 提供数据查询和SQL构建
- `TableManager`: 管理表格显示和导出

**主要接口**:
- `setHistoryDataType`: 设置历史数据类型
- `executeQuery`: 执行查询（重写父类方法）
- `printResults`: 打印查询结果
- `exportResults`: 导出查询结果

### FilterManager (过滤器管理器)

**位置**: `widgets/filters/filtermanager.h/cpp`

**职责**:
- 根据历史数据类型创建和管理不同的过滤器
- 协调各过滤器的显示和隐藏
- 收集所有过滤条件

**设计模式**:
- 策略模式: 使用不同的过滤器策略处理不同数据类型

**过滤器类型**:
1. `StandardFilter`: 用于SOE和模拟量等标准历史数据
2. `UserLoginFilter`: 用于用户登录历史
3. `SystemEventFilter`: 用于系统事件历史

### HistoryDataProvider (历史数据提供者)

**位置**: `widgets/providers/historydataprovider.h/cpp`

**职责**:
- 根据过滤条件构建SQL语句
- 执行SQL查询并返回结果
- 根据历史数据类型选择适当的表

**主要功能**:
- `buildQuerySQL`: 构建查询SQL语句
- `executeQuery`: 执行查询并返回结果
- `getTableNameForType`: 获取数据类型对应的表名

### TableManager (表格管理器)

**位置**: `widgets/tables/tablemanager.h/cpp`

**职责**:
- 设置和管理表格视图
- 设置表头和格式化数据
- 提供打印和导出功能

**主要功能**:
- `setTableHeaders`: 设置表头
- `updateTableDisplay`: 更新表格显示
- `printResults`: 打印查询结果
- `exportToCSV`: 导出查询结果到CSV

### TelemetryDataProvider (遥测数据提供者)

**位置**: `widgets/providers/telemetrydataprovider.h/cpp`

**职责**:
- 负责分钟级和秒级遥测历史数据的查询和构建SQL (查询 `insertDate` 和 `curvevalue` 列)。
- 从数据库获取原始遥测数据，处理跨多个数据库行（多个5分钟数据段）的情况。
- 调用 `TelemetryDataModel::parseBlobData` 解析每一行的 `curvevalue` BLOB。
- 将查询时间范围内的所有解析后的数据点聚合成一个 `QVector<TelemetryDataPoint>`。
- 与 `DBManager` 交互进行数据库操作。
- 提供遥测点属性（如名称、单位）。

**主要接口**:
- `setDBManager`: 设置数据库管理器。
- `executeMinuteTelemetryQuery`: 执行分钟级遥测查询 (返回 `QSqlQuery` 对象，包含 `insertDate`, `curvevalue`)。
- `executeSecondTelemetryQuery`: 执行秒级遥测查询 (返回 `QSqlQuery` 对象，包含 `insertDate`, `curvevalue`)。
- `buildMinuteTelemetrySQL`: 构建分钟级遥测SQL (SELECT `insertDate`, `curvevalue`)。
- `buildSecondTelemetrySQL`: 构建秒级遥测SQL (SELECT `insertDate`, `curvevalue`)。
- `getTelemetryProperties`: 获取遥测点属性。
- `convertQueryToDataPoints(QSqlQuery &query, bool isMinuteData)`: 核心方法，接收 `QSqlQuery` (包含多行 `insertDate`, `curvevalue`)，迭代处理每一行，调用 `TelemetryDataModel::parseBlobData` 解析BLOB，并返回包含所有数据点的 `QVector<TelemetryDataPoint>`。

## 数据类关系

### StationModel (站点模型)

**位置**: `models/station/stationmodel.h/cpp`

**职责**:
- 管理站点、间隔和设备的层次结构
- 提供数据访问和查询功能
- 支持站点树的构建和更新

**主要功能**:
- `loadStations`: 从数据库加载站点数据
- `getStationById`: 根据ID获取站点
- `getDevicesByBay`: 获取间隔下的设备
- `updateStationData`: 更新站点数据
- `isConnected`: 检查数据库连接状态

### DBManager (数据库管理器)

**位置**: `database/dbmanager.h/cpp`

**职责**:
- 管理数据库连接
- 提供基本的SQL执行功能
- 处理数据库错误

**主要功能**:
- `connectToDatabase`: 连接到数据库
- `disconnectFromDatabase`: 断开数据库连接
- `executeQuery`: 执行SQL查询
- `isConnected`: 检查数据库连接状态

### TelemetryDataModel (遥测数据模型)

**位置**: `models/telemetrydatamodel.h/cpp`

**职责**:
- 继承自 `QAbstractTableModel`，专门为遥测数据提供数据模型。
- 通过 `setData(const QVector<TelemetryDataPoint> &dataPoints)` 方法接收一个完整的、已解析和聚合的遥测数据点集合，并更新模型供视图显示。
- 提供静态方法 `parseBlobData(const QByteArray &blobData, qint64 baseTimestampEpochSecs, bool isMinuteData)` 用于解析单个 `curvevalue` BLOB 数据到 `QVector<TelemetryDataPoint>`。
- 管理遥测点的显示属性，如单位、系数、偏移量。

**主要接口**:
- `rowCount`, `columnCount`, `data`, `headerData`: QAbstractTableModel的标准接口实现。
- `setData(const QVector<TelemetryDataPoint> &dataPoints)`: 设置模型数据的主要入口。
- `clear`, `addDataPoint`: 数据操作方法。
- `loadDataFromBlob(const QByteArray &blobData, qint64 baseTimestampEpochSecs, bool isMinuteData)`: (可选)可用于加载单个BLOB数据并更新模型，内部调用 `parseBlobData` 和 `setData`。
- `parseBlobData(...)`: 静态方法，解析单个BLOB。
- `setProperties`, `unit`, `rate`, `offset`: 设置和获取显示属性。

## 接口和抽象类

### BaseFilter (过滤器基类)

**位置**: `widgets/filters/filtermanager.h`

**职责**:
- 定义所有过滤器的通用接口
- 提供过滤器的基本功能

**主要接口**:
- `createFilterControls`: 创建过滤控件
- `updateVisibility`: 更新控件可见性
- `getFilters`: 获取过滤条件
- `initializeControls`: 初始化控件数据

## 类扩展点

本设计提供以下扩展点，便于将来添加新功能：

1. **新的历史数据类型**:
   - 在`enums.h`中添加新的HistoryDataType枚举值
   - 在`constants.h`中添加新表名常量
   - 在HistoryDataProvider中添加新表的处理逻辑
   - 创建新的过滤器类（继承BaseFilter）

2. **新的过滤条件**:
   - 扩展现有过滤器类或创建新的过滤器类
   - 更新FilterManager以支持新过滤器

3. **新的导出格式**:
   - 在TableManager中添加新的导出方法
   - 更新UI以支持新导出格式

4. **新的查询类型**:
   - 创建新的QueryWidget子类
   - 在主窗口中添加新查询类型的支持

## 类之间的通信

1. **事件/信号通信**:
   - TableManager -> SOEHistoryWidget: 结果计数和消息通知
   - FilterManager内部: 站点、间隔和设备选择联动

2. **直接方法调用**:
   - SOEHistoryWidget -> HistoryDataProvider: 执行查询
   - SOEHistoryWidget -> FilterManager: 获取过滤条件
   - SOEHistoryWidget -> TableManager: 更新表格显示
   - (修改) [相关Widget/Coordinator] -> TelemetryDataProvider: 调用 `execute...Query` 获取 `QSqlQuery`。
   - (修改) [相关Widget/Coordinator] -> TelemetryDataProvider: 调用 `convertQueryToDataPoints` 将 `QSqlQuery` 转换为 `QVector<TelemetryDataPoint>`。
   - TelemetryDataProvider -> DBManager: 执行数据库查询。
   - TelemetryDataProvider -> TelemetryDataModel::parseBlobData: 解析单个BLOB。
   - (修改) [相关Widget/Coordinator] -> TelemetryDataModel: 调用 `setData` 传递完整的 `QVector<TelemetryDataPoint>`。

3. **依赖注入**:
   - MainWindow注入DBManager和StationModel到SOEHistoryWidget
   - SOEHistoryWidget注入DBManager到FilterManager和HistoryDataProvider 