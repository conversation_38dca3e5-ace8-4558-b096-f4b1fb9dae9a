#include "tablemanager.h"
#include <QDebug>
#include <QHeaderView>
#include <QFile>
#include <QTextStream>
#include <QMessageBox>
#include <QPrinter>
#include <QPrintDialog>
#include <QPainter>

TableManager::TableManager(QObject *parent)
    : QObject(parent), m_tableView(nullptr)
{
}

void TableManager::setTableView(QTableView *tableView)
{
    m_tableView = tableView;
    
    if (m_tableView) {
        // 设置表格属性
        m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        m_tableView->setSelectionMode(QAbstractItemView::SingleSelection);
        m_tableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
        m_tableView->horizontalHeader()->setStretchLastSection(true);
        
        // 允许用户调整列宽
        m_tableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
        
        // 设置表格外观
        m_tableView->setFrameShape(QFrame::StyledPanel);
        m_tableView->setAlternatingRowColors(true);
        
        // 设置表格头部点击和排序
        m_tableView->setSortingEnabled(true);
        m_tableView->horizontalHeader()->setSectionsClickable(true);
    }
}

void TableManager::setTableHeaders(const QString &tableName, QSqlQueryModel *model)
{
    if (!model) {
        qWarning() << "表格模型为空，无法设置表头";
        return;
    }
    
    // 检查模型是否有数据
    if (model->rowCount() == 0) {
        qWarning() << "表格模型没有数据，无法设置表头";
        return;
    }
    
    // 如果表头映射表为空，初始化它
    if (m_tableHeaders.isEmpty()) {
        initTableHeaders();
    }
    
    // 获取列数
    int columnCount = model->columnCount();
    
    // 获取当前表的表头配置
    QStringList headers = getTableHeaders(tableName);
    
    // 设置表头
    int headerCount = qMin(headers.size(), columnCount);
    for (int i = 0; i < headerCount; ++i) {
        model->setHeaderData(i, Qt::Horizontal, headers[i]);
    }
    
    // 如果列数超过预定义的表头，为剩余列设置默认表头
    for (int i = headerCount; i < columnCount; ++i) {
        model->setHeaderData(i, Qt::Horizontal, QString("列 %1").arg(i+1));
    }
    
    qDebug() << "表头设置完成，列数:" << columnCount << "，使用表头:" << (m_tableHeaders.contains(tableName.toLower()) ? tableName : "default");
}

void TableManager::updateTableDisplay(QSqlQueryModel *model, int resultCount)
{
    if (!m_tableView) {
        qWarning() << "表格视图为空，无法更新显示";
        return;
    }
    
    // 设置模型
    m_tableView->setModel(model);
    
    // 首先设置一个合理的初始列宽
    m_tableView->resizeColumnsToContents();
    
    // 确保日期时间列有足够的宽度显示完整时间（精确到秒）
    for (int i = 0; i < model->columnCount(); ++i) {
        QString headerText = model->headerData(i, Qt::Horizontal).toString().toUpper();
        // 增强时间列识别
        if (headerText.contains("TIME") || 
            headerText.contains("DATE") || 
            headerText.contains("时间") || 
            headerText.contains("日期")) {
            
            // 查看列中的实际数据，确保列宽足够显示完整内容
            int maxWidth = 0;
            for (int row = 0; row < qMin(10, model->rowCount()); row++) {
                QString value = model->data(model->index(row, i)).toString();
                // 使用fontMetrics计算文本宽度 (兼容不同Qt版本)
                int textWidth = m_tableView->fontMetrics().width(value) + 20; // 额外边距
                maxWidth = qMax(maxWidth, textWidth);
            }
            
            // 确保时间列宽至少有180像素，或足够显示实际内容
            m_tableView->setColumnWidth(i, qMax(180, maxWidth));
            qDebug() << "为时间列" << headerText << "设置宽度:" << m_tableView->columnWidth(i);
        } else if (m_tableView->columnWidth(i) < 100) {
            // 其他列如果太窄，也设置合理宽度
            m_tableView->setColumnWidth(i, 100);
        }
    }
    
    // 发出结果计数信号
    emit resultCounted(resultCount);
    
    if (resultCount == 0) {
        emit messageNotification("查询结果", "没有找到符合条件的记录");
    }
}

void TableManager::clearTable()
{
    if (!m_tableView) {
        qWarning() << "表格视图为空，无法清空";
        return;
    }
    
    // 创建空模型
    QSqlQueryModel* emptyModel = new QSqlQueryModel(this);
    
    // 设置空模型到表格视图
    m_tableView->setModel(emptyModel);
    
    qDebug() << "表格已清空";
}

bool TableManager::exportToCSV(const QString &fileName, QSqlQueryModel *model)
{
    if (!model) {
        emit messageNotification("导出失败", "表格模型为空，无法导出数据");
        return false;
    }
    
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit messageNotification("导出失败", QString("无法打开文件进行写入: %1").arg(fileName));
        return false;
    }
    
    // 直接写入UTF-8编码的内容，包括BOM
    QByteArray content;
    
    // 添加UTF-8 BOM
    content.append(QByteArray::fromHex("EFBBBF"));
    
    // 获取行数和列数
    int rowCount = model->rowCount();
    int columnCount = model->columnCount();
    
    // 预分配列表空间
    QStringList headers;
    headers.reserve(columnCount);
    
    // 构建表头
    for (int i = 0; i < columnCount; ++i) {
        headers << model->headerData(i, Qt::Horizontal).toString();
    }
    QString headerLine = headers.join(",") + "\n";
    content.append(headerLine.toUtf8());
    
    // 批量处理数据，每次处理100行，减少内存压力
    const int BATCH_SIZE = 100;
    for (int row = 0; row < rowCount; row += BATCH_SIZE) {
        int batchEnd = qMin(row + BATCH_SIZE, rowCount);
        
        // 写入数据
        for (int currentRow = row; currentRow < batchEnd; ++currentRow) {
            QStringList rowData;
            rowData.reserve(columnCount);
            for (int col = 0; col < columnCount; ++col) {
                rowData << model->data(model->index(currentRow, col)).toString();
            }
            QString dataLine = rowData.join(",") + "\n";
            content.append(dataLine.toUtf8());
        }
    }
    
    // 一次性写入所有内容
    file.write(content);
    file.close();
    emit messageNotification("导出成功", QString("数据已成功导出到: %1").arg(fileName));
    return true;
}

void TableManager::printResults(QSqlQueryModel *model)
{
    if (!model) {
        emit messageNotification("打印失败", "表格模型为空，无法打印数据");
        return;
    }
    
#if defined(QT_PRINTSUPPORT_LIB)
    // 创建打印机
    QPrinter printer;
    
    // 显示打印对话框
    QPrintDialog dialog(&printer, nullptr);
    if (dialog.exec() != QDialog::Accepted) {
        return;
    }
    
    // 使用QPainter绘制
    QPainter painter;
    if (!painter.begin(&printer)) {
        emit messageNotification("打印错误", "无法初始化打印机");
        return;
    }
    
    // 获取行数和列数
    int rowCount = model->rowCount();
    int columnCount = model->columnCount();
    
    // 设置字体和边距
    QFont font = painter.font();
    font.setPointSize(10);
    painter.setFont(font);
    
    // 页面设置
    int pageWidth = printer.pageRect().width();
    int pageHeight = printer.pageRect().height();
    int leftMargin = 50;
    int topMargin = 50;
    int rowHeight = 30;
    int columnWidth = (pageWidth - 2 * leftMargin) / columnCount;
    
    // 绘制表头
    painter.drawLine(leftMargin, topMargin, leftMargin + columnCount * columnWidth, topMargin);
    for (int col = 0; col < columnCount; ++col) {
        QString headerText = model->headerData(col, Qt::Horizontal).toString();
        QRect headerRect(leftMargin + col * columnWidth, topMargin - rowHeight, columnWidth, rowHeight);
        painter.drawText(headerRect, Qt::AlignCenter, headerText);
        painter.drawLine(leftMargin + col * columnWidth, topMargin - rowHeight, leftMargin + col * columnWidth, topMargin);
    }
    painter.drawLine(leftMargin + columnCount * columnWidth, topMargin - rowHeight, leftMargin + columnCount * columnWidth, topMargin);
    
    // 绘制数据行
    int currentY = topMargin;
    for (int row = 0; row < rowCount; ++row) {
        // 检查是否需要换页
        if (currentY + rowHeight > pageHeight - topMargin) {
            printer.newPage();
            currentY = topMargin;
            
            // 在新页上重新绘制表头
            painter.drawLine(leftMargin, currentY, leftMargin + columnCount * columnWidth, currentY);
            for (int col = 0; col < columnCount; ++col) {
                QString headerText = model->headerData(col, Qt::Horizontal).toString();
                QRect headerRect(leftMargin + col * columnWidth, currentY - rowHeight, columnWidth, rowHeight);
                painter.drawText(headerRect, Qt::AlignCenter, headerText);
                painter.drawLine(leftMargin + col * columnWidth, currentY - rowHeight, leftMargin + col * columnWidth, currentY);
            }
            painter.drawLine(leftMargin + columnCount * columnWidth, currentY - rowHeight, leftMargin + columnCount * columnWidth, currentY);
        }
        
        // 绘制行分隔线
        painter.drawLine(leftMargin, currentY, leftMargin + columnCount * columnWidth, currentY);
        
        // 绘制单元格
        for (int col = 0; col < columnCount; ++col) {
            QString cellText = model->data(model->index(row, col)).toString();
            QRect cellRect(leftMargin + col * columnWidth, currentY, columnWidth, rowHeight);
            painter.drawText(cellRect, Qt::AlignCenter, cellText);
            painter.drawLine(leftMargin + col * columnWidth, currentY, leftMargin + col * columnWidth, currentY + rowHeight);
        }
        painter.drawLine(leftMargin + columnCount * columnWidth, currentY, leftMargin + columnCount * columnWidth, currentY + rowHeight);
        
        currentY += rowHeight;
    }
    
    // 绘制最后一行的底线
    painter.drawLine(leftMargin, currentY, leftMargin + columnCount * columnWidth, currentY);
    
    // 结束绘制
    painter.end();
#else
    emit messageNotification("功能不可用", "打印功能不可用，当前构建未包含打印支持");
#endif
}

QStringList TableManager::getTableHeaders(const QString &tableName) const
{
    // 如果表头映射表为空，初始化它
    if (m_tableHeaders.isEmpty()) {
        initTableHeaders();
    }
    
    // 返回指定表的表头
    return m_tableHeaders.value(tableName.toLower(), m_tableHeaders.value("default"));
}

void TableManager::initTableHeaders() const
{
    // SOE事件表头 (HTSOELOG) - 根据实际数据库结构
    m_tableHeaders["htsoelog"] = QStringList() 
        << "ID"            // ID
        << "主机名"        // HOSTNAME
        << "设备ID"        // EQUIPID
        << "遥信ID"        // YXID
        << "遥信名称"      // YXNAME
        << "事件类型"      // VALUETYPE
        << "事件时间"      // STARTTIME
        << "毫秒值"        // STARTMS
        << "确认状态"      // FLAGACK
        << "入库时间"      // TIMEIN
        << "确认人"        // ACKPERSON
        << "原状态值"      // OLDVALUE
        << "新状态值";     // NEWVALUE

    // 遥信变位表头 (HTCOSLOG) - 根据实际数据库结构
    m_tableHeaders["htcoslog"] = QStringList() 
        << "ID"            // ID
        << "设备ID"        // EQUIPID
        << "遥信ID"        // YXID
        << "遥信名称"      // YXNAME
        << "原状态值"      // OLDVALUE
        << "新状态值"      // NEWVALUE
        << "变位时间"      // STARTTIME
        << "毫秒值"        // MSEC
        << "确认状态"      // FLAGACK
        << "确认人"        // ACKPERSON
        << "主机名";       // HOSTNAME
    
    // 越限告警表头 (HTBREAKLIMITLOG) - 根据实际数据库结构
    m_tableHeaders["htbreaklimitlog"] = QStringList() 
        << "ID"            // ID
        << "主机名"        // HOSTNAME
        << "设备ID"        // EQUIPID
        << "遥测ID"        // YCID
        << "越限类型"      // BREAKLIMITTYPE
        << "遥测名称"      // ycname
        << "开始时间"      // STARTTIME
        << "结束时间"      // ENDTIME
        << "限值"          // LIMITVALUE
        << "越限值"        // BREAKVALUE
        << "恢复状态"      // RECOVERD
        << "确认状态"      // FLAGACK
        << "确认人";       // ACKPERSON
    
    // 系统错误信息表头 (SYSERRORINFO) - 根据实际数据库结构
    m_tableHeaders["syserrorinfo"] = QStringList() 
        << "ID"            // ID
        << "主机名"        // HOSTNAME
        << "错误来源"      // SOURCENAME
        << "错误信息"      // ERRORINFO
        << "发生时间"      // STARTTIME
        << "确认状态"      // FLAGACK
        << "错误对象ID"    // ERROBJID
        << "错误状态"      // ERRSTATE
        << "错误类型"      // ERRTYPE
        << "确认人";       // ACKPERSON
    
    // 操作记录表头 (HTOPERATIONLOG) - 根据实际数据库结构
    m_tableHeaders["htoperationlog"] = QStringList() 
        << "ID"            // ID
        << "设备ID"        // EQUIPID
        << "设备名称"      // EQUIPNAME
        << "控制信号ID"    // CTRLSIGID
        << "控制信号名称"  // CTRLSIGNAME
        << "控制类型"      // CTRLTYPE
        << "操作类型"      // OPERTYPE
        << "操作值"        // OPERVALUE
        << "操作结果"      // OPERRESULT
        << "操作时间"      // STARTTIME
        << "操作人员"      // OPERATORNAME
        << "监护人员"      // SUPERVISORNAME
        << "主机名";       // HOSTNAME
    
    // 用户登录表头 (HTUSERLOG) - 根据实际数据库结构
    m_tableHeaders["htuserlog"] = QStringList() 
        << "ID"            // ID
        << "用户名"        // USERNAME
        << "登录时间"      // STARTTIME
        << "日志类型"      // LOGTYPE
        << "主机名"        // HOSTNAME
        << "应用名称";     // APPNAME
    
    // 默认表头 - 用于未定义的表
    m_tableHeaders["default"] = QStringList() << "ID" << "时间" << "内容";
} 