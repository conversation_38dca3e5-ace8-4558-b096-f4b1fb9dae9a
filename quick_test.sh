#!/bin/bash

# 快速测试SSH命令

REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="admin"
REMOTE_PASS="admin"
REMOTE_DIR="/home/<USER>/zhuan<PERSON>i<PERSON><PERSON>-<PERSON><PERSON>/"

echo "测试SSH命令..."
echo "远程目录: $REMOTE_DIR"

echo ""
echo "1. 测试基本连接..."
if sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo '连接成功'" 2>/dev/null; then
    echo "✓ 连接成功"
else
    echo "✗ 连接失败"
    exit 1
fi

echo ""
echo "2. 获取文件列表..."
file_list=$(sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "ls -la '$REMOTE_DIR'" 2>/dev/null)
result=$?

echo "命令退出码: $result"

if [ $result -eq 0 ] && [ -n "$file_list" ]; then
    echo "✓ 文件列表获取成功"
    echo "文件数量: $(echo "$file_list" | wc -l)"
    echo ""
    echo "文件列表:"
    echo "$file_list"
    
    echo ""
    echo "3. 查找WPD文件..."
    wpd_files=$(echo "$file_list" | grep "\.WPD$")
    if [ -n "$wpd_files" ]; then
        echo "找到的WPD文件:"
        echo "$wpd_files"
    else
        echo "未找到WPD文件"
    fi
else
    echo "✗ 文件列表获取失败"
    echo "错误输出:"
    sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "ls -la '$REMOTE_DIR'" 2>&1
fi
