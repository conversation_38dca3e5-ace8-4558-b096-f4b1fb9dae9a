#ifndef DBMANAGER_H
#define DBMANAGER_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QTimer>

class DBManager : public QObject
{
    Q_OBJECT
public:
    static DBManager* instance();
    
    bool connectToDatabase(const QString &host, int port, const QString &username, 
                          const QString &password, const QString &dbName);
    bool connectToDatabase(const QString &host, const QString &username, 
                          const QString &password, const QString &dbName);
    bool testConnection(const QString &host, int port, const QString &username, 
                       const QString &password, const QString &dbName);
    void disconnectFromDatabase();
    bool isConnected() const;
    
    QSqlQuery executeQuery(const QString &queryStr);
    QString lastError() const;
    
    // 启用或禁用自动重连功能
    void setAutoReconnect(bool enable);
    bool isAutoReconnectEnabled() const;
    
signals:
    void connectionStatusChanged(bool connected);
    void queryError(const QString &errorMessage);
    void reconnectAttempt(int attemptCount);

private slots:
    void tryReconnect();
    
private:
    explicit DBManager(QObject *parent = nullptr);
    ~DBManager();
    
    // 新增：创建并打开数据库连接的私有方法
    bool createAndOpenConnection();
    
    static DBManager *m_instance;
    QSqlDatabase m_db;
    bool m_connected;
    QString m_lastError;
    
    // 自动重连相关
    bool m_autoReconnect;
    QTimer *m_reconnectTimer;
    int m_reconnectAttempts;
    int m_maxReconnectAttempts;
    
    // 保存当前连接信息，用于重连
    QString m_host;
    int m_port;
    QString m_username;
    QString m_password;
    QString m_dbName;
};

#endif // DBMANAGER_H 