#include "soehistorywidget.h"
#include "ui_querywidget.h"
#include "filters/filtermanager.h"
#include "tables/tablemanager.h"
#include "../models/station/stationmodel.h"
#include "../database/dbmanager.h"
#include "../common/logger.h"
#include <QDebug>
#include <QMessageBox>
#include <QFileDialog>
#include <QDir>
#include <QSqlRecord>
#include "historytreewidget.h"

SOEHistoryWidget::SOEHistoryWidget(QWidget *parent)
    : QueryWidget(parent),
      m_historyDataType(SOE_EVENT),
      m_currentTable("htsoelog"),
      m_currentDeviceId(0),
      m_currentDeviceIndex(0),
      m_dataProvider(new HistoryDataProvider()),
      m_telemetryDataProvider(new TelemetryDataProvider()),
      m_telemetryDataModel(new TelemetryDataModel()),
      m_filterManager(new FilterManager()),
      m_tableManager(new TableManager()),
      m_stationModel(nullptr),
      m_dbManager(nullptr)
{
    // 初始化过滤器状态保存结构
    m_savedFilterState.deviceId = 0;
    m_savedFilterState.deviceIndex = 0;
    m_savedFilterState.pointName = "";
    m_savedFilterState.interval = 60; // 默认1分钟
    m_savedFilterState.startTime = QDateTime::currentDateTime().addDays(-1);
    m_savedFilterState.endTime = QDateTime::currentDateTime();
    
    // 初始化UI
    initUI();
    
    // 初始化信号连接
    initConnections();
}

SOEHistoryWidget::~SOEHistoryWidget()
{
    // 释放资源
    delete m_dataProvider;
    delete m_telemetryDataProvider;
    delete m_telemetryDataModel;
    delete m_filterManager;
    delete m_tableManager;
    
    // 不删除m_stationModel和m_dbManager，它们是外部依赖
}

void SOEHistoryWidget::setStationModel(StationModel *model)
{
    LOG_DEBUG("设置站点模型");
    
    if (!model) {
        LOG_WARNING("传入的站点模型为空");
        return;
    }
    
    m_stationModel = model;
    m_filterManager->setStationModel(model);
    
    LOG_DEBUG("站点模型设置完成");
}

void SOEHistoryWidget::setDBManager(DBManager *manager)
{
    LOG_DEBUG("设置数据库管理器");
    
    if (!manager) {
        LOG_WARNING("传入的数据库管理器为空");
        return;
    }
    
    // 保存当前的过滤条件状态
    QMap<QString, QString> currentFilters;
    QDateTime startTime, endTime;
    if (m_filterManager) {
        currentFilters = m_filterManager->getFilters();
        startTime = m_filterManager->getStartTime();
        endTime = m_filterManager->getEndTime();
    }
    
    // 先调用父类的setDBManager方法，但禁止自动查询
    bool oldAutoQuery = QueryWidget::getAutoQueryEnabled();
    QueryWidget::setAutoQueryEnabled(false);
    QueryWidget::setDBManager(manager);
    QueryWidget::setAutoQueryEnabled(oldAutoQuery);
    
    // 然后设置到本地组件
    m_dbManager = manager;
    m_dataProvider->setDBManager(manager);
    m_telemetryDataProvider->setDBManager(manager);
    m_filterManager->setDBManager(manager);
    
    // 数据库连接后，预加载缓存但不执行查询
    if (manager->isConnected() && m_stationModel) {
        // 主动缓存站点、间隔和设备数据
        m_stationModel->preloadCache(m_currentTable);
        
        // 恢复之前的过滤条件
        if (m_filterManager) {
            m_filterManager->initializeFilters();
            
            // 恢复时间范围
            m_filterManager->setTimeRange(startTime, endTime);
        }
    }
    
    LOG_DEBUG("数据库管理器设置完成，数据缓存已初始化");
}

void SOEHistoryWidget::setHistoryDataType(HistoryDataType type)
{
    LOG_DEBUG(QString("设置历史数据类型: %1").arg(type).toStdString().c_str());
    
    // 获取当前时间范围设置，以便切换类型后保留
    QDateTime startTime, endTime;
    if (m_filterManager) {
        startTime = m_filterManager->getStartTime();
        endTime = m_filterManager->getEndTime();
    }
    
    m_historyDataType = type;
    
    // 检查是否为遥测历史数据类型
    bool isTelemetryType = (type == MINUTE_TELEMETRY || type == SECOND_TELEMETRY);
    
    if (!isTelemetryType) {
        // 普通历史数据类型
    // 获取表名
    m_currentTable = m_dataProvider->getTableNameForType(type).toLower();
    
    LOG_DEBUG(QString("当前表名设置为: %1").arg(m_currentTable).toStdString().c_str());
        
        // 使用标准模型
        if (ui && ui->resultTableView) {
            ui->resultTableView->setModel(model());
        }
    } else {
        // 遥测历史数据类型
        // 使用遥测数据模型
        if (ui && ui->resultTableView) {
            ui->resultTableView->setModel(m_telemetryDataModel);
        }
    }
    
    // 更新过滤器类型
    m_filterManager->setFilterType(type);
    m_filterManager->loadControlData();
    
    // 在切换类型后，保留原来的时间范围设置
    if (m_filterManager) {
        m_filterManager->setTimeRange(startTime, endTime);
    }
    
    // 清空表格，不显示旧数据
    if (m_tableManager) {
        m_tableManager->clearTable();
    }
    
    // 清空结果计数
    if (ui && ui->pageInfoLabel) {
        ui->pageInfoLabel->setText("");
    }
}

HistoryDataType SOEHistoryWidget::getHistoryDataType() const
{
    return m_historyDataType;
}

void SOEHistoryWidget::saveFilterState()
{
    LOG_DEBUG("保存过滤器状态");
    
    if (m_filterManager) {
        // 保存当前的时间范围
        m_savedFilterState.startTime = m_filterManager->getStartTime();
        m_savedFilterState.endTime = m_filterManager->getEndTime();
        
        // 保存设备和点位信息
        m_savedFilterState.deviceId = m_currentDeviceId;
        m_savedFilterState.deviceIndex = m_currentDeviceIndex;
        m_savedFilterState.pointName = m_currentPointName;
        
        // 保存时间间隔
        m_savedFilterState.interval = m_filterManager->getInterval();
        
        LOG_DEBUG(QString("已保存状态 - 设备ID: %1, 索引: %2, 开始时间: %3")
                 .arg(m_savedFilterState.deviceId)
                 .arg(m_savedFilterState.deviceIndex)
                 .arg(m_savedFilterState.startTime.toString("yyyy-MM-dd hh:mm:ss"))
                 .toStdString().c_str());
    }
}

void SOEHistoryWidget::restoreFilterState()
{
    LOG_DEBUG("恢复过滤器状态");
    
    // 只有在过滤器管理器存在的情况下才恢复
    if (m_filterManager) {
        // 恢复时间范围
        m_filterManager->setTimeRange(m_savedFilterState.startTime, m_savedFilterState.endTime);
        
        // 恢复设备和点位信息
        if (m_savedFilterState.deviceId > 0 && m_savedFilterState.deviceIndex > 0) {
            // 设置当前点位信息
            m_currentDeviceId = m_savedFilterState.deviceId;
            m_currentDeviceIndex = m_savedFilterState.deviceIndex;
            m_currentPointName = m_savedFilterState.pointName;
            
            // 通知过滤器管理器更新设备和点位选择
            m_filterManager->setCurrentDevice(m_currentDeviceId);
            m_filterManager->setCurrentPoint(m_currentDeviceIndex);
            
            // 加载遥测点属性
            if (m_telemetryDataProvider && m_telemetryDataModel) {
                QMap<QString, QVariant> properties = m_telemetryDataProvider->getTelemetryProperties(m_currentDeviceId, m_currentDeviceIndex);
                m_telemetryDataModel->setProperties(properties);
                
                // 如果没有获取到名称，则使用保存的名称
                if (properties.value("Name").toString().isEmpty()) {
                    properties["Name"] = m_currentPointName;
                    m_telemetryDataModel->setProperties(properties);
                }
            }
            
            LOG_DEBUG(QString("已恢复状态 - 设备ID: %1, 索引: %2, 开始时间: %3")
                     .arg(m_currentDeviceId)
                     .arg(m_currentDeviceIndex)
                     .arg(m_savedFilterState.startTime.toString("yyyy-MM-dd hh:mm:ss"))
                     .toStdString().c_str());
        }
    }
}

void SOEHistoryWidget::printResults()
{
    LOG_DEBUG("打印查询结果");
    
    // 获取当前活动模型
    QAbstractItemModel *currentModel = getCurrentModel();
    
    // 使用TableManager打印
    if (currentModel) {
        // 由于TableManager::printResults需要QSqlQueryModel*，
        // 而TelemetryDataModel是QAbstractTableModel，
        // 这里我们只在模型是QSqlQueryModel的情况下打印
        QSqlQueryModel *sqlModel = qobject_cast<QSqlQueryModel*>(currentModel);
        if (sqlModel) {
            m_tableManager->printResults(sqlModel);
        } else {
            QMessageBox::information(this, "打印提示", 
                                   "暂不支持打印当前类型的数据。\n"
                                   "请导出到CSV文件后打印。");
        }
    }
}

void SOEHistoryWidget::exportResults(const QString &filePath)
{
    LOG_DEBUG("导出查询结果");
    
    // 获取文件名
    QString fileName = filePath;
    if (fileName.isEmpty()) {
        fileName = getExportFileName();
    }
    
    if (!fileName.isEmpty()) {
        // 获取当前活动模型
        QAbstractItemModel *currentModel = getCurrentModel();
        
        // 使用TableManager导出
        if (currentModel) {
            // 由于TableManager::exportToCSV需要QSqlQueryModel*，
            // 而TelemetryDataModel是QAbstractTableModel，
            // 这里我们需要自定义导出或将数据转换为QSqlQueryModel
            QSqlQueryModel *sqlModel = qobject_cast<QSqlQueryModel*>(currentModel);
            if (sqlModel) {
                m_tableManager->exportToCSV(fileName, sqlModel);
            } else {
                // 对于TelemetryDataModel，实现自定义导出
                if (m_historyDataType == MINUTE_TELEMETRY || m_historyDataType == SECOND_TELEMETRY) {
                    exportTelemetryDataToCSV(fileName, m_telemetryDataModel);
                } else {
                    QMessageBox::warning(this, "导出错误", "不支持导出当前类型的数据");
                }
            }
        }
    }
}

// 实现通用导出遥测数据到CSV的方法
void SOEHistoryWidget::exportTelemetryDataToCSV(const QString &fileName, TelemetryDataModel *model)
{
    if (!model) {
        return;
    }
    
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "导出错误", 
                           "无法创建文件: " + fileName + "\n" + file.errorString());
        return;
    }
    
    // 直接写入UTF-8编码的内容，包括BOM
    QByteArray content;
    
    // 添加UTF-8 BOM
    content.append(QByteArray::fromHex("EFBBBF"));
    
    // 构建表头
    QString header = "时间,值";
    if (!model->unit().isEmpty()) {
        header += QString(" (%1)").arg(model->unit());
    }
    header += ",质量\n";
    
    // 将字符串转换为UTF-8字节数组
    content.append(header.toUtf8());
    
    // 写入数据行
    for (int row = 0; row < model->rowCount(); ++row) {
        QString dataLine = QString("%1,%2,%3\n")
            .arg(model->data(model->index(row, TelemetryDataModel::TIMESTAMP)).toString())
            .arg(model->data(model->index(row, TelemetryDataModel::VALUE)).toString())
            .arg(model->data(model->index(row, TelemetryDataModel::QUALITY)).toString());
        content.append(dataLine.toUtf8());
    }
    
    // 一次性写入所有内容
    file.write(content);
    file.close();
    
    QMessageBox::information(this, "导出成功", 
                           "数据已成功导出到文件: " + fileName);
}

QAbstractItemModel* SOEHistoryWidget::getCurrentModel() const
{
    if (m_historyDataType == MINUTE_TELEMETRY || m_historyDataType == SECOND_TELEMETRY) {
        return m_telemetryDataModel;
    } else {
        return model();
    }
}

void SOEHistoryWidget::refreshData()
{
    LOG_DEBUG("刷新数据");
    
    // 检查数据库连接
    if (!m_dbManager || !m_dbManager->isConnected()) {
        LOG_WARNING("数据库未连接，无法刷新数据");
        QMessageBox::warning(this, "刷新错误", "数据库未连接，请先连接数据库");
        return;
    }
    
    // 执行查询
    executeQuery();
}

void SOEHistoryWidget::setCurrentTelemetryPoint(int deviceId, int deviceIndex, const QString &name)
{
    m_currentDeviceId = deviceId;
    m_currentDeviceIndex = deviceIndex;
    m_currentPointName = name;
    
    LOG_DEBUG(QString("设置当前遥测点: %1 DeviceID: %2 DeviceIndex: %3")
              .arg(name).arg(deviceId).arg(deviceIndex).toStdString().c_str());
    
    // 加载遥测点属性
    if (m_telemetryDataProvider && m_telemetryDataModel) {
        QMap<QString, QVariant> properties = m_telemetryDataProvider->getTelemetryProperties(deviceId, deviceIndex);
        m_telemetryDataModel->setProperties(properties);
        
        // 如果没有获取到名称，则使用传入的名称
        if (properties.value("Name").toString().isEmpty()) {
            properties["Name"] = name;
            m_telemetryDataModel->setProperties(properties);
        }
    }
    
    // 保存到过滤器状态中，以便切换类型时恢复
    m_savedFilterState.deviceId = deviceId;
    m_savedFilterState.deviceIndex = deviceIndex;
    m_savedFilterState.pointName = name;
}

void SOEHistoryWidget::onTreeItemClicked(QTreeWidgetItem *item)
{
    if (!item) {
        return;
    }
    
    // 检查是否是遥测点节点（具有父节点的子节点）
    if (!item->parent() || !item->parent()->parent()) {
        return;
    }
    
    // 检查是否是遥测历史数据节点
    // 获取祖父节点（根节点）来判断是否是遥测数据
    QTreeWidgetItem *grandParentItem = item->parent()->parent();
    QString grandParentText = grandParentItem->text(0);
    
    // 检查祖父节点是否是分钟级或秒级遥测节点
    if (grandParentText != "分钟级遥测历史数据查询" && grandParentText != "秒级遥测历史数据查询") {
        return;
    }
    
    // 获取DeviceID和DeviceIndex
    QVariant userData = item->data(0, Qt::UserRole);
    if (userData.isValid() && userData.canConvert<QVariantMap>()) {
        QVariantMap dataMap = userData.toMap();
        int deviceId = dataMap.value("DeviceID").toInt();
        int deviceIndex = dataMap.value("DeviceIndex").toInt();
        QString name = item->text(0);
        
        // 设置当前遥测点
        setCurrentTelemetryPoint(deviceId, deviceIndex, name);
        
        // 更新窗口标题，显示当前选中的遥测点
        QString title = QString("%1 - %2").arg(windowTitle()).arg(name);
        setWindowTitle(title);
    }
}

void SOEHistoryWidget::initUI()
{
    LOG_DEBUG("初始化UI");
    
    // 断开父类中建立的连接，重新连接到子类的executeQuery方法
    if (ui && ui->queryButton) {
        // 断开所有连接
        disconnect(ui->queryButton, nullptr, nullptr, nullptr);
        
        // 重新连接到子类的onQueryButtonClicked方法
        connect(ui->queryButton, &QPushButton::clicked, this, &SOEHistoryWidget::onQueryButtonClicked);
    }
    
    // 隐藏标题，为过滤器腾出空间
    if (ui && ui->titleLabel) {
        ui->titleLabel->setVisible(false);
    }
    
    // 创建过滤器布局（如果不存在）
    if (ui) {
        // 删除现有布局
        if (ui->queryConditionGroup->layout()) {
            QLayout *existingLayout = ui->queryConditionGroup->layout();
            QLayoutItem *item;
            while ((item = existingLayout->takeAt(0)) != nullptr) {
                if (item->widget()) {
                    item->widget()->setParent(nullptr); // 将控件从旧布局中移除
                }
                delete item;
            }
            delete existingLayout;
        }
        
        // 创建垂直布局来容纳过滤器和操作按钮
        QVBoxLayout *mainLayout = new QVBoxLayout(ui->queryConditionGroup);
        mainLayout->setContentsMargins(8, 8, 8, 8);
        mainLayout->setSpacing(12);
        
        // 设置过滤器管理器
        m_filterManager->setParentWidget(this);
        
        // 获取过滤器控件并添加到布局
        QWidget *filterWidget = m_filterManager->getFilterWidget();
        if (filterWidget) {
            mainLayout->addWidget(filterWidget);
            m_filterManager->setDataProvider(m_dataProvider);
        }
        
        // 创建底部操作栏
        QWidget *bottomWidget = new QWidget(ui->queryConditionGroup);
        QHBoxLayout *bottomLayout = new QHBoxLayout(bottomWidget);
        bottomLayout->setContentsMargins(0, 8, 0, 0);
        bottomLayout->setSpacing(12);
        
        // 添加结果信息标签和查询按钮
        if (ui->pageInfoLabel) {
            ui->pageInfoLabel->setText("第 0/0 页");
            ui->pageInfoLabel->setStyleSheet("color: #666; font-size: 12px;");
            bottomLayout->addWidget(ui->pageInfoLabel);
        }
        
        bottomLayout->addStretch(); // 添加弹性空间
        
        if (ui->queryButton) {
            ui->queryButton->setVisible(true);
            ui->queryButton->setText("查询");
            ui->queryButton->setMinimumWidth(100);
            ui->queryButton->setMinimumHeight(32);
            ui->queryButton->setStyleSheet(
                "QPushButton {"
                "  background-color: #007acc;"
                "  color: white;"
                "  border: none;"
                "  border-radius: 4px;"
                "  padding: 6px 16px;"
                "  font-weight: 500;"
                "}"
                "QPushButton:hover {"
                "  background-color: #005a9e;"
                "}"
                "QPushButton:pressed {"
                "  background-color: #004578;"
                "}"
            );
            bottomLayout->addWidget(ui->queryButton);
        }
        
        mainLayout->addWidget(bottomWidget);
        
        // 设置查询条件组的最小高度，确保有足够空间显示所有控件
        ui->queryConditionGroup->setMinimumHeight(180);
        ui->queryConditionGroup->setStyleSheet(
            "QGroupBox {"
            "  font-weight: 500;"
            "  border: 1px solid #ddd;"
            "  border-radius: 4px;"
            "  margin-top: 8px;"
            "  background-color: white;"
            "}"
            "QGroupBox::title {"
            "  subcontrol-origin: margin;"
            "  left: 8px;"
            "  padding: 0 4px 0 4px;"
            "  color: #333;"
            "}"
        );
    }
    
    // 设置表格管理器
    if (ui && ui->resultTableView) {
        m_tableManager->setTableView(ui->resultTableView);
    }
    
    LOG_DEBUG("UI初始化完成");
}

void SOEHistoryWidget::initConnections()
{
    LOG_DEBUG("初始化连接");
    
    // 连接TableManager信号
    connect(m_tableManager, &TableManager::resultCounted, this, &SOEHistoryWidget::onResultCounted);
    connect(m_tableManager, &TableManager::messageNotification, this, &SOEHistoryWidget::onMessageNotification);
    
    LOG_DEBUG("连接初始化完成");
}

QString SOEHistoryWidget::buildQuerySQL() const
{
    LOG_DEBUG("构建查询SQL");
    
    if (!m_dbManager || !m_dbManager->isConnected()) {
        LOG_WARNING("数据库未连接，无法构建查询");
        return QString();
    }
    
    // 获取当前表名
    QString tableName = m_currentTable;
    
    // 获取过滤条件
    QMap<QString, QString> filters = m_filterManager->getFilters();
    
    // 获取时间范围
    QDateTime startTime = m_filterManager->getStartTime();
    QDateTime endTime = m_filterManager->getEndTime();
    
    // 使用数据提供者构建SQL
    return m_dataProvider->buildQuerySQL(tableName, filters, startTime, endTime);
}

void SOEHistoryWidget::executeQuery()
{
    LOG_DEBUG("执行查询");
    
    // 检查数据库连接
    if (!m_dbManager || !m_dbManager->isConnected()) {
        LOG_WARNING("数据库未连接，无法执行查询");
        QMessageBox::warning(this, "查询错误", "数据库未连接，请先连接数据库");
        return;
    }
    
    // 获取时间范围
    QDateTime startTime, endTime;
    if (m_filterManager) {
        startTime = m_filterManager->getStartTime();
        endTime = m_filterManager->getEndTime();
    }
    
    // 判断数据类型
    if (m_historyDataType == MINUTE_TELEMETRY || m_historyDataType == SECOND_TELEMETRY) {
        // 对于遥测数据，执行特殊查询
        executeTelemetryQuery(m_currentDeviceId, m_currentDeviceIndex, startTime, endTime);
        return;
    }
    
    // 对于标准历史数据，执行标准查询
    // 获取过滤条件
    QMap<QString, QString> filters;
    if (m_filterManager) {
        filters = m_filterManager->getFilters();
    }
    
    // 注释掉过滤条件检查，允许查询所有数据
    // if (filters.isEmpty()) {
    //     QMessageBox::warning(this, "查询提示", "请至少选择一个过滤条件");
    //     return;
    // }
    
    // 执行SQL查询
    QSqlQuery query = m_dataProvider->executeQuery(m_currentTable, filters, startTime, endTime);
    
    // 检查查询是否成功
    if (query.lastError().isValid()) {
        QString errorText = query.lastError().text();
        LOG_WARNING(QString("查询错误: %1").arg(errorText).toStdString().c_str());
        
        // 检查是否是字段不存在的错误
        if (errorText.contains("Unknown column") || errorText.contains("doesn't exist") || 
            errorText.contains("no such column")) {
            QMessageBox::warning(this, "查询错误", 
                               "执行查询时发生错误：\n" + errorText);
            return;
        } else {
            // 其他错误可能是因为没有数据，继续执行并显示"没有找到符合条件的记录"
            LOG_DEBUG("查询可能没有返回数据，继续处理...");
        }
    }
    
    // 清空旧数据
    if (model()) {
        model()->clear();
    }
    
    // 设置新查询
    if (model()) {
        model()->setQuery(query);
        
        if (model()->lastError().isValid()) {
            LOG_WARNING(QString("设置查询模型错误: %1").arg(model()->lastError().text()).toStdString().c_str());
            QMessageBox::warning(this, "查询错误", 
                               "设置查询模型时发生错误：\n" + model()->lastError().text());
            return;
        }
    }
        
    // 检查是否有结果
    int rowCount = model() ? model()->rowCount() : 0;
    if (rowCount == 0) {
        // 如果没有结果，清空模型
        if (model()) {
            model()->clear();
        }
        
        // 清空表格
        if (m_tableManager) {
            m_tableManager->clearTable();
        }
        
        // 更新行数信息
        if (ui && ui->pageInfoLabel) {
            ui->pageInfoLabel->setText("查询结果：0 条记录");
        }
        
        // 显示提示信息
        QMessageBox::information(this, "查询结果", "没有找到符合条件的记录");
        
        LOG_DEBUG("查询完成，没有找到符合条件的记录");
        return;
    }
    
    // 设置表头
    if (m_tableManager) {
        m_tableManager->setTableHeaders(m_currentTable, model());
        m_tableManager->updateTableDisplay(model(), rowCount);
    }
    
    // 更新行数信息
    if (ui && ui->pageInfoLabel) {
        ui->pageInfoLabel->setText(QString("查询结果：%1 条记录").arg(rowCount));
    }
    
    LOG_DEBUG(QString("查询完成，共找到 %1 条记录").arg(rowCount).toStdString().c_str());
}

void SOEHistoryWidget::executeTelemetryQuery(int deviceId, int deviceIndex, 
                                           const QDateTime &startTime, 
                                           const QDateTime &endTime)
{
    LOG_DEBUG(QString("执行遥测历史数据查询: DeviceID=%1 DeviceIndex=%2 StartTime=%3 EndTime=%4")
              .arg(deviceId).arg(deviceIndex)
              .arg(startTime.toString("yyyy-MM-dd HH:mm:ss"))
              .arg(endTime.toString("yyyy-MM-dd HH:mm:ss")).toStdString().c_str());
    
    // 检查是否选择了遥测点
    if (deviceId == 0 && deviceIndex == 0) {
        QMessageBox::warning(this, "查询提示", "请先选择一个遥测点");
            return;
        }
        
            // 检查时间范围是否有效
    if (!startTime.isValid() || !endTime.isValid() || startTime > endTime) {
        QMessageBox::warning(this, "查询提示", "请设置有效的时间范围");
        return;
    }
    
    // 检查查询日期是否大于当前时间
    QDateTime currentTime = QDateTime::currentDateTime();
    if (startTime > currentTime) {
        QMessageBox::warning(this, "查询提示", "查询开始时间不能大于当前时间");
        return;
    }
    
    if (endTime > currentTime) {
        QMessageBox::warning(this, "查询提示", "查询结束时间不能大于当前时间");
        return;
    }
            
    // 根据数据类型执行不同的查询
    QSqlQuery query;
    if (m_historyDataType == MINUTE_TELEMETRY) {
        // 执行分钟级遥测查询
        query = m_telemetryDataProvider->executeMinuteTelemetryQuery(
            deviceId, deviceIndex, startTime, endTime);
    } else if (m_historyDataType == SECOND_TELEMETRY) {
        // 执行秒级遥测查询
        query = m_telemetryDataProvider->executeSecondTelemetryQuery(
            deviceId, deviceIndex, startTime, endTime);
    } else {
        LOG_WARNING("非遥测数据类型，无法执行遥测查询");
        return;
    }
    
    // 检查查询是否成功
    if (query.lastError().isValid()) {
        LOG_WARNING(QString("遥测查询错误: %1").arg(query.lastError().text()).toStdString().c_str());
        
        // 检查是否是表不存在的错误
        QString errorText = query.lastError().text();
        if (errorText.contains("TABLE_NOT_EXISTS") || errorText.contains("doesn't exist") || 
            errorText.contains("当日没有数据")) {
            QMessageBox::information(this, "查询提示", "当日没有数据");
        } else {
            QMessageBox::warning(this, "查询错误", 
                               "执行遥测查询时发生错误：\n" + errorText);
        }
        return;
    }
            
    // 检查字段结构
    if (query.record().isEmpty()) {
        LOG_WARNING("查询结果不包含任何字段");
        QMessageBox::warning(this, "查询错误", "查询结果不包含任何字段");
        return;
    }
    
    // 输出查询返回的字段列表，用于调试
    LOG_DEBUG("查询结果包含以下字段:");
    for (int i = 0; i < query.record().count(); i++) {
        LOG_DEBUG(QString(" - %1").arg(query.record().fieldName(i)).toStdString().c_str());
            }
            
    // 清空遥测数据模型
    if (m_telemetryDataModel) {
        m_telemetryDataModel->clear();
    }
    
    // 解析查询结果到遥测数据模型
    parseTelemetryQueryResult(query, m_telemetryDataModel);
    
    // 检查是否有结果
    int rowCount = m_telemetryDataModel ? m_telemetryDataModel->rowCount() : 0;
    if (rowCount == 0) {
        // 更新行数信息
        if (ui && ui->pageInfoLabel) {
            ui->pageInfoLabel->setText("查询结果：0 条记录");
        }
        
        // 显示提示信息
        QMessageBox::information(this, "查询结果", "没有找到符合条件的记录");
        
        LOG_DEBUG("遥测查询完成，没有找到符合条件的记录");
        return;
    }
            
            // 更新表格显示
    if (ui && ui->resultTableView) {
        ui->resultTableView->reset();
        ui->resultTableView->setModel(m_telemetryDataModel);
        
        // 设置表格属性
        ui->resultTableView->setAlternatingRowColors(true);
        ui->resultTableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        ui->resultTableView->setSortingEnabled(true);
        ui->resultTableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
        
        // 设置表格列宽可以调整
        ui->resultTableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
        
        // 优化遥测表格列宽
        optimizeTelemetryTableColumnWidths();
    }
    
    // 更新行数信息
            if (ui && ui->pageInfoLabel) {
                ui->pageInfoLabel->setText(QString("查询结果：%1 条记录").arg(rowCount));
            }
    
    LOG_DEBUG(QString("遥测查询完成，共找到 %1 条记录").arg(rowCount).toStdString().c_str());
}

void SOEHistoryWidget::parseTelemetryQueryResult(QSqlQuery &query, TelemetryDataModel *model)
{
    if (!model) {
        LOG_WARNING("遥测数据模型为空，无法解析查询结果");
        return;
    }
    
    // 检查查询是否有效且有结果
    if (!query.isActive()) {
        LOG_WARNING("查询不活跃或无效");
        // 如果查询有错误，输出错误信息
        if (query.lastError().isValid()) {
            LOG_WARNING(QString("查询错误: %1").arg(query.lastError().text()).toStdString().c_str());
        }
        return;
    }
    
    // 输出查询字段信息
    LOG_DEBUG("解析遥测查询结果，字段列表:");
    for (int i = 0; i < query.record().count(); i++) {
        LOG_DEBUG(QString(" - %1").arg(query.record().fieldName(i)).toStdString().c_str());
    }
    
    // 检查是否存在必要的insertDate和curvevalue字段
    int insertDateIndex = query.record().indexOf("insertDate");
    int curveValueIndex = query.record().indexOf("curvevalue");
    
    if (insertDateIndex == -1 || curveValueIndex == -1) {
        LOG_WARNING("查询结果缺少必要的insertDate或curvevalue字段，无法解析");
        return;
    }
    
    bool isMinuteData = (m_historyDataType == MINUTE_TELEMETRY);
        
    // 使用TelemetryDataProvider来转换查询结果
    QVector<TelemetryDataPoint> dataPoints = m_telemetryDataProvider->convertQueryToDataPoints(query, isMinuteData);
        
    // 获取时间间隔过滤选项
    int interval = 1; // 默认间隔为1（不过滤）
    if (m_filterManager) {
        interval = m_filterManager->getInterval();
    }
    
    // 修复日志输出：interval是秒数，需要正确显示
    QString intervalDesc;
    if (interval == 0) {
        intervalDesc = "原始数据";
    } else if (interval < 60) {
        intervalDesc = QString("%1秒").arg(interval);
    } else if (interval % 60 == 0) {
        intervalDesc = QString("%1分钟").arg(interval / 60);
    } else if (interval % 3600 == 0) {
        intervalDesc = QString("%1小时").arg(interval / 3600);
    } else {
        intervalDesc = QString("%1秒").arg(interval);
    }
    
    LOG_DEBUG(QString("应用时间间隔过滤: %1 (原始值: %2秒, 数据类型: %3)")
              .arg(intervalDesc)
              .arg(interval)
              .arg(isMinuteData ? "分钟级" : "秒级")
              .toStdString().c_str());
    
    // 如果有设置间隔并且大于1，则使用TelemetryDataProvider进行过滤
    if (interval > 1 && m_telemetryDataProvider && !dataPoints.isEmpty()) {
        LOG_DEBUG(QString("开始过滤数据: 原始数据点数=%1, 时间范围=%2 到 %3")
                  .arg(dataPoints.size())
                  .arg(dataPoints.first().timestamp.toString("yyyy-MM-dd HH:mm:ss"))
                  .arg(dataPoints.last().timestamp.toString("yyyy-MM-dd HH:mm:ss"))
                  .toStdString().c_str());
                  
        QVector<TelemetryDataPoint> filteredPoints = 
            m_telemetryDataProvider->filterDataByInterval(dataPoints, interval, isMinuteData);
        
        // 设置过滤后的数据到模型
        model->setData(filteredPoints);
        LOG_DEBUG(QString("过滤后数据点数量: %1 (过滤前: %2)")
                  .arg(filteredPoints.size()).arg(dataPoints.size()).toStdString().c_str());
                  
        if (!filteredPoints.isEmpty()) {
            LOG_DEBUG(QString("过滤后时间范围: %1 到 %2")
                      .arg(filteredPoints.first().timestamp.toString("yyyy-MM-dd HH:mm:ss"))
                      .arg(filteredPoints.last().timestamp.toString("yyyy-MM-dd HH:mm:ss"))
                      .toStdString().c_str());
        }
    } else {
        // 不需要过滤，直接设置数据到模型
        model->setData(dataPoints);
        LOG_DEBUG(QString("未应用过滤，数据点数量: %1").arg(dataPoints.size()).toStdString().c_str());
    }
}

void SOEHistoryWidget::onQueryButtonClicked()
{
    LOG_DEBUG("查询按钮被点击");
    
    // 执行查询
        executeQuery();
}

void SOEHistoryWidget::onPrintButtonClicked()
{
    LOG_DEBUG("打印按钮被点击");
    
    // 打印查询结果
    printResults();
}

void SOEHistoryWidget::onExportButtonClicked()
{
    LOG_DEBUG("导出按钮被点击");
    
    // 导出查询结果
    exportResults();
}

void SOEHistoryWidget::onResultCounted(int count)
{
    // 更新结果计数
    if (ui && ui->pageInfoLabel) {
        ui->pageInfoLabel->setText(QString("查询结果：%1 条记录").arg(count));
    }
}

void SOEHistoryWidget::onMessageNotification(const QString &title, const QString &message)
{
    // 显示消息通知
    QMessageBox::information(this, title, message);
}

QString SOEHistoryWidget::getExportFileName()
{
    return QFileDialog::getSaveFileName(this, tr("导出到CSV"), 
                                                   QDir::homePath(), 
                                                   tr("CSV文件 (*.csv)"));
}

void SOEHistoryWidget::setupTableHeaders()
{
    // 使用数据提供者获取表字段定义
    if (!m_dataProvider || !model()) {
        return;
    }
    
    // 获取当前表的字段定义
    QStringList fields = m_dataProvider->getTableFields(m_currentTable);
    
    // 先输出调试信息
    LOG_DEBUG(QString("setupTableHeaders - 表: %1 字段数: %2 模型列数: %3")
              .arg(m_currentTable).arg(fields.size()).arg(model()->columnCount()).toStdString().c_str());
    LOG_DEBUG(QString("setupTableHeaders - 字段列表: %1").arg(fields.join(", ")).toStdString().c_str());
    
    // 根据字段名称设置表头
    int columnCount = qMin(fields.size(), model()->columnCount());
    
    // 根据当前历史数据类型设置表头
    switch (m_historyDataType) {
    case SOE_EVENT:
        // SOE事件表头 (HTSOELOG)
        for (int i = 0; i < columnCount; ++i) {
            QString fieldName = fields.at(i);
            QString displayName;
            
            // 根据字段名映射到显示名称
            if (fieldName == "ID") displayName = tr("记录ID");
            else if (fieldName == "HOSTNAME") displayName = tr("主机名称");
            else if (fieldName == "EQUIPID") {
                displayName = tr("设备ID");
                // 确保字段名正确存储为EQUIPID
                model()->setHeaderData(i, Qt::Horizontal, "EQUIPID");
                LOG_DEBUG(QString("*** 设置EQUIPID字段 *** 列: %1 表头: %2 底层字段: %3")
                          .arg(i).arg(displayName).arg(fieldName).toStdString().c_str());
            }
            else if (fieldName == "YXID") displayName = tr("遥信ID");
            else if (fieldName == "YXNAME") displayName = tr("遥信名称");
            else if (fieldName == "VALUETYPE") {
                displayName = tr("事件类型");
                // 确保字段名正确存储为VALUETYPE
                model()->setHeaderData(i, Qt::Horizontal, "VALUETYPE");
                LOG_DEBUG(QString("*** 设置VALUETYPE字段 *** 列: %1 表头: %2 底层字段: %3")
                          .arg(i).arg(displayName).arg(fieldName).toStdString().c_str());
            }
            else if (fieldName == "STARTTIME") displayName = tr("事件发生时间");
            else if (fieldName == "STARTMS") displayName = tr("毫秒值");
            else if (fieldName == "FLAGACK") displayName = tr("确认标志");
            else if (fieldName == "TIMEIN") displayName = tr("入库时间");
            else if (fieldName == "ACKPERSON") displayName = tr("确认人");
            else if (fieldName == "OLDVALUE") displayName = tr("原状态值");
            else if (fieldName == "NEWVALUE") displayName = tr("新状态值");
            else if (fieldName == "CTRLSIGID") displayName = tr("控制信号ID");
            else if (fieldName == "CTRLSIGNAME") displayName = tr("控制信号名称");
            else if (fieldName == "CTRLTYPE") displayName = tr("控制类型");
            else if (fieldName == "OPERTYPE") {
                displayName = tr("操作类型");
                // 确保字段名正确存储为OPERTYPE
                model()->setHeaderData(i, Qt::Horizontal, "OPERTYPE");
                LOG_DEBUG(QString("*** 设置OPERTYPE字段 *** 列: %1 表头: %2 底层字段: %3")
                          .arg(i).arg(displayName).arg(fieldName).toStdString().c_str());
            }
            else displayName = fieldName;
            
            model()->setHeaderData(i, Qt::Horizontal, displayName);
            LOG_DEBUG(QString("表头设置 - 列: %1 字段名: %2 显示名称: %3")
                      .arg(i).arg(fieldName).arg(displayName).toStdString().c_str());
        }
        break;
    case YX_CHANGE:
        // 遥信变位表头 (HTCOSLOG)
        for (int i = 0; i < columnCount; ++i) {
            QString fieldName = fields.at(i);
            QString displayName;
            
            // 根据字段名映射到显示名称
            if (fieldName == "ID") displayName = tr("记录ID");
            else if (fieldName == "HOSTNAME") displayName = tr("主机名称");
            else if (fieldName == "EQUIPID") displayName = tr("设备ID");
            else if (fieldName == "YXID") displayName = tr("遥信ID");
            else if (fieldName == "YXNAME") displayName = tr("遥信信号名称");
            else if (fieldName == "OLDVALUE") displayName = tr("原状态值");
            else if (fieldName == "NEWVALUE") displayName = tr("新状态值");
            else if (fieldName == "STARTTIME") displayName = tr("状态变化时间");
            else if (fieldName == "MSEC") displayName = tr("毫秒值");
            else if (fieldName == "FLAGACK") displayName = tr("确认状态");
            else if (fieldName == "ACKPERSON") displayName = tr("确认人");
            else displayName = fieldName;
            
            model()->setHeaderData(i, Qt::Horizontal, displayName);
        }
        break;
    case SYSTEM_EVENT:
        // 系统事件表头 (SYSERRORINFO)
        for (int i = 0; i < columnCount; ++i) {
            QString fieldName = fields.at(i);
            QString displayName;
            
            // 根据字段名映射到显示名称
            if (fieldName == "ID") displayName = tr("记录ID");
            else if (fieldName == "HOSTNAME") displayName = tr("主机名称");
            else if (fieldName == "SOURCENAME") displayName = tr("错误来源");
            else if (fieldName == "ERRORINFO") displayName = tr("错误信息");
            else if (fieldName == "STARTTIME") displayName = tr("错误发生时间");
            else if (fieldName == "FLAGACK") displayName = tr("确认状态");
            else if (fieldName == "ERROBJID") displayName = tr("错误对象ID");
            else if (fieldName == "ERRSTATE") displayName = tr("错误处理状态");
            else if (fieldName == "ERRTYPE") {
                displayName = tr("错误类型");
                // 确保字段名正确存储为ERRTYPE
                model()->setHeaderData(i, Qt::Horizontal, "ERRTYPE");
                LOG_DEBUG(QString("*** 设置ERRTYPE字段 *** 列: %1 表头: %2 底层字段: %3")
                          .arg(i).arg(displayName).arg(fieldName).toStdString().c_str());
            }
            else if (fieldName == "ACKPERSON") displayName = tr("确认人");
            else displayName = fieldName;
            
            model()->setHeaderData(i, Qt::Horizontal, displayName);
        }
        break;
    case USER_LOGIN:
        // 用户登录表头 (HTUSERLOG)
        for (int i = 0; i < columnCount; ++i) {
            QString fieldName = fields.at(i);
            QString displayName;
            
            // 根据字段名映射到显示名称
            if (fieldName == "ID") displayName = tr("日志记录ID");
            else if (fieldName == "USERNAME") displayName = tr("用户名");
            else if (fieldName == "STARTTIME") displayName = tr("操作时间");
            else if (fieldName == "LOGTYPE") displayName = tr("日志状态");
            else if (fieldName == "HOSTNAME") displayName = tr("主机名");
            else if (fieldName == "APPNAME") displayName = tr("应用名称");
            else displayName = fieldName;
            
            model()->setHeaderData(i, Qt::Horizontal, displayName);
        }
        break;
    case LIMIT_ALARM:
        // 越限告警表头 (HTBREAKLIMITLOG)
        for (int i = 0; i < columnCount; ++i) {
            QString fieldName = fields.at(i);
            QString displayName;
            
            // 根据字段名映射到显示名称
            if (fieldName == "ID") displayName = tr("记录ID");
            else if (fieldName == "HOSTNAME") displayName = tr("主机名称");
            else if (fieldName == "EQUIPID") displayName = tr("设备ID");
            else if (fieldName == "YCID") displayName = tr("遥测ID");
            else if (fieldName == "BREAKLIMITTYPE") displayName = tr("越限类型");
            else if (fieldName == "ycname") displayName = tr("遥测名称");
            else if (fieldName == "STARTTIME") displayName = tr("越限开始时间");
            else if (fieldName == "ENDTIME") displayName = tr("越限结束时间");
            else if (fieldName == "LIMITVALUE") displayName = tr("限值阈值");
            else if (fieldName == "BREAKVALUE") displayName = tr("越限值");
            else if (fieldName == "RECOVERD") displayName = tr("恢复状态");
            else if (fieldName == "FLAGACK") displayName = tr("确认状态");
            else if (fieldName == "ACKPERSON") displayName = tr("确认人");
            else displayName = fieldName;
            
            model()->setHeaderData(i, Qt::Horizontal, displayName);
        }
        break;
    default:
        // 默认表头处理，直接使用字段名作为表头
        for (int i = 0; i < columnCount; ++i) {
            if (i < fields.size()) {
                model()->setHeaderData(i, Qt::Horizontal, fields.at(i));
            } else {
                model()->setHeaderData(i, Qt::Horizontal, model()->headerData(i, Qt::Horizontal));
            }
        }
        break;
    }
}

void SOEHistoryWidget::initializeFilters()
{
    LOG_DEBUG("主动初始化筛选器");
    
    if (!m_dbManager || !m_dbManager->isConnected()) {
        LOG_WARNING("数据库未连接，无法初始化筛选器");
        return;
    }
    
    if (!m_filterManager) {
        LOG_WARNING("过滤器管理器未初始化");
        return;
    }
    
    // 主动缓存站点、间隔和设备数据
    if (m_stationModel) {
        // 清空缓存确保获取最新数据
        m_stationModel->clearCache();
        // 预加载当前表的数据
        m_stationModel->preloadCache(m_currentTable);
    }
    
    // 初始化筛选器控件
    m_filterManager->initializeFilters();
    
    LOG_DEBUG("筛选器初始化完成");
}

void SOEHistoryWidget::onTelemetryPointSelected(int deviceId, int deviceIndex, const QString &name)
{
    LOG_DEBUG(QString("遥测点被选中: DeviceID=%1 DeviceIndex=%2 Name=%3")
              .arg(deviceId).arg(deviceIndex).arg(name).toStdString().c_str());
    
    // 设置当前遥测点信息
    m_currentDeviceId = deviceId;
    m_currentDeviceIndex = deviceIndex;
    m_currentPointName = name;
    
    // 通知过滤器管理器
    if (m_filterManager) {
        m_filterManager->setCurrentDevice(deviceId);
        m_filterManager->setCurrentPoint(deviceIndex);
    }
    
    // 加载遥测点属性
    if (m_telemetryDataProvider && m_telemetryDataModel) {
        QMap<QString, QVariant> properties = m_telemetryDataProvider->getTelemetryProperties(deviceId, deviceIndex);
        m_telemetryDataModel->setProperties(properties);
        
        // 如果没有获取到名称，则使用传入的名称
        if (properties.value("Name").toString().isEmpty()) {
            properties["Name"] = name;
            m_telemetryDataModel->setProperties(properties);
        }
    }
    
    // 保存到过滤器状态中，以便切换类型时恢复
    m_savedFilterState.deviceId = deviceId;
    m_savedFilterState.deviceIndex = deviceIndex;
    m_savedFilterState.pointName = name;
}

void SOEHistoryWidget::optimizeTelemetryTableColumnWidths()
{
    if (!ui || !ui->resultTableView || !m_telemetryDataModel) {
        return;
    }
    
    // 首先调用基础的自动调整
    ui->resultTableView->resizeColumnsToContents();
    
    // 获取表格视图和字体信息
    QTableView *tableView = ui->resultTableView;
    QFontMetrics fontMetrics = tableView->fontMetrics();
    
    // 为每列设置优化的宽度
    for (int column = 0; column < m_telemetryDataModel->columnCount(); ++column) {
        int optimalWidth = 120; // 默认宽度
        
        switch (column) {
            case TelemetryDataModel::TIMESTAMP: {
                // 时间列：确保能完整显示时间格式 "yyyy-MM-dd HH:mm:ss"
                QString sampleTime = "2025-12-31 23:59:59";
                int timeWidth = fontMetrics.width(sampleTime) + 30; // 额外边距
                optimalWidth = qMax(timeWidth, 180); // 最小180像素
                break;
            }
            case TelemetryDataModel::VALUE: {
                // 数值列：根据数据内容和单位调整宽度
                QString headerText = m_telemetryDataModel->headerData(column, Qt::Horizontal).toString();
                int headerWidth = fontMetrics.width(headerText) + 20;
                
                // 检查实际数据的宽度
                int maxDataWidth = headerWidth;
                int sampleRows = qMin(10, m_telemetryDataModel->rowCount());
                for (int row = 0; row < sampleRows; ++row) {
                    QString value = m_telemetryDataModel->data(
                        m_telemetryDataModel->index(row, column)).toString();
                    int valueWidth = fontMetrics.width(value) + 20;
                    maxDataWidth = qMax(maxDataWidth, valueWidth);
                }
                
                // 数值列宽度：考虑数据宽度，但设置合理的最小和最大值
                optimalWidth = qBound(100, maxDataWidth, 200);
                break;
            }
            case TelemetryDataModel::QUALITY: {
                // 质量列：固定宽度，足够显示"正常"、"异常"等状态
                QString sampleQuality = "质量码: 255";
                int qualityWidth = fontMetrics.width(sampleQuality) + 20;
                optimalWidth = qMax(qualityWidth, 80); // 最小80像素
                break;
            }
            default:
                optimalWidth = 120;
                break;
        }
        
        // 设置列宽
        tableView->setColumnWidth(column, optimalWidth);
    }
    
    // 确保表格头部可见
    tableView->horizontalHeader()->setVisible(true);
    
    // 设置表格的整体显示策略
    tableView->horizontalHeader()->setStretchLastSection(false);
    
    LOG_DEBUG(QString("遥测表格列宽优化完成: 时间列=%1px, 数值列=%2px, 质量列=%3px")
              .arg(tableView->columnWidth(TelemetryDataModel::TIMESTAMP))
              .arg(tableView->columnWidth(TelemetryDataModel::VALUE))
              .arg(tableView->columnWidth(TelemetryDataModel::QUALITY))
              .toStdString().c_str());
} 