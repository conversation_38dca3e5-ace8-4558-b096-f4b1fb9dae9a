#include "mainwindow.h"
#include <QApplication>
#include <QTextCodec>
#include <QTranslator>
#include <QLibraryInfo>
#include "database/dbmanager.h"
#include <QSettings>
#include <QDebug>
#include <QSqlDatabase>
#include <QLoggingCategory>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include "utils/xmlconfigreader.h"
#include "utils/devicenamemapper.h"
#include <QDir>
#include <QTimer>
#include <QMessageBox>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include "models/station/stationmodel.h"
#include "common/logger.h"

// 启用所有调试日志
Q_DECLARE_LOGGING_CATEGORY(datetimeLog)
Q_LOGGING_CATEGORY(datetimeLog, "datetime")

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("HistoryQueryTool");
    app.setApplicationDisplayName("历史数据查询工具");
    app.setOrganizationName("YourCompany");
    
    // 创建命令行解析器
    QCommandLineParser parser;
    parser.setApplicationDescription("历史数据查询工具 - 用于查询各类历史数据");
    parser.addHelpOption();
    parser.addVersionOption();
    
    // 添加调试日志选项
    QCommandLineOption debugOption(QStringList() << "d" << "debug",
                                  "启用调试日志输出");
    parser.addOption(debugOption);
    
    // 解析命令行参数
    parser.process(app);
    
    // 根据命令行参数设置日志级别
    bool enableDebug = parser.isSet(debugOption);
    Logger::setDebugEnabled(enableDebug);
    
    if (enableDebug) {
        LOG_INFO("调试日志已启用");
    } else {
        LOG_INFO("调试日志已禁用（使用 -d 或 --debug 参数启用）");
    }
    
    // 设置编码
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    
    LOG_INFO("应用程序启动");
    
    // 加载XML配置
    XMLConfigReader *configReader = XMLConfigReader::instance();
    QString configPath = QDir::currentPath() + "/AppConfig.xml";
    
    LOG_INFO(QString("加载配置文件: %1").arg(configPath));
    
    if (!configReader->loadConfig(configPath)) {
        LOG_ERROR("无法加载配置文件");
        QMessageBox::critical(nullptr, "配置错误", 
                            "无法加载配置文件 AppConfig.xml\n"
                            "请确保配置文件存在并且格式正确。");
        return -1;
    }
    
    // 获取历史数据库配置
    DBConfig hisDBConfig = configReader->getHisDBConfig();
    
    // 创建数据库管理器
    DBManager *dbManager = DBManager::instance();
    
    // 尝试连接数据库
    LOG_INFO(QString("连接数据库: %1:%2/%3").arg(hisDBConfig.server).arg(hisDBConfig.port).arg(hisDBConfig.name));
    
    bool connected = dbManager->connectToDatabase(
        hisDBConfig.server,
        hisDBConfig.port,
        hisDBConfig.user,
        hisDBConfig.pwd,
        hisDBConfig.name
    );
    
    if (!connected) {
        LOG_ERROR("数据库连接失败: " + dbManager->lastError());
        QMessageBox::warning(nullptr, "数据库连接", 
                           "无法自动连接到历史数据库。\n"
                           "错误信息：" + dbManager->lastError() + "\n\n"
                           "请检查：\n"
                           "1. AppConfig.xml配置文件中的数据库参数是否正确\n"
                           "2. 数据库服务器是否正常运行\n"
                           "3. 网络连接是否正常\n\n"
                           "您可以在程序启动后手动连接数据库。");
    } else {
        LOG_INFO("数据库连接成功");
    }
    
    // 初始化设备名称映射器
    DeviceNameMapper *deviceMapper = DeviceNameMapper::instance();
    if (!deviceMapper->initialize()) {
        LOG_WARNING("设备名称映射器初始化失败: " + deviceMapper->lastError());
    } else {
        LOG_INFO("设备名称映射器初始化成功");
    }
    
    // 获取站点模型单例（不需要手动创建）
    StationModel::instance();
    
    // 创建并显示主窗口
    MainWindow window;
    window.show();
    
    LOG_INFO("主窗口已显示");
    
    int result = app.exec();
    
    LOG_INFO("应用程序退出");
    
    return result;
} 
