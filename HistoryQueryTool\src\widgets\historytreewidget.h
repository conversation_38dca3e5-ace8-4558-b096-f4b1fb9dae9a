#ifndef HISTORYTREEWIDGET_H
#define HISTORYTREEWIDGET_H

#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QSqlDatabase>
#include "../common/enums.h"
#include "../database/dbmanager.h"

class HistoryTreeWidget : public QTreeWidget
{
    Q_OBJECT
public:
    explicit HistoryTreeWidget(QWidget *parent = nullptr);
    
    enum HistoryType {
        SOE = 0,
        Telemetry,
        Alarm,
        SystemEvent,
        Operation,
        Login
    };

    // 加载遥测参数
    void loadTelemetryParameters();

    // 设置当前类型
    void setCurrentType(int type);

signals:
    void historyTypeSelected(int type);
    void telemetryPointSelected(int deviceId, int deviceIndex, const QString &name);

private slots:
    void onItemClicked(QTreeWidgetItem *item, int column);

private:
    void setupUI();
    
    // 连接参数库并加载遥测数据
    bool connectParaDB();
    
    // 加载分钟级遥测数据
    void loadMinuteTelemetry(QSqlDatabase &db);
    
    // 加载秒级遥测数据
    void loadSecondTelemetry(QSqlDatabase &db);
    
    // 清除子节点
    void clearChildItems(QTreeWidgetItem *parentItem);
    
    QTreeWidgetItem *rootItem;
    QTreeWidgetItem *soeItem;
    QTreeWidgetItem *telemetryItem;
    QTreeWidgetItem *alarmItem;
    QTreeWidgetItem *systemEventItem;
    QTreeWidgetItem *operationItem;
    QTreeWidgetItem *loginItem;
    QTreeWidgetItem *minuteTelemetryItem; // 分钟级遥测历史
    QTreeWidgetItem *secondTelemetryItem; // 秒级遥测历史
};

#endif // HISTORYTREEWIDGET_H 