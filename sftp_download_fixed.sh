#!/bin/bash

# SFTP自动下载脚本 - 修复版本
# 功能：每分钟自动从远程SFTP服务器下载指定类型的最新文件

# 配置信息
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="root"
REMOTE_PASS="zkfr_2019"

# 目录映射配置（远程目录:本地目录）
declare -A DIR_MAPPINGS=(
    ["/home/<USER>/zhuanJiKong-Ewenjian/"]="/home/<USER>/qsjglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian2/"]="/home/<USER>/djglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian3/"]="/home/<USER>/jcglyc/"
)

# 文件类型配置 - 需要下载的文件后缀
FILE_EXTENSIONS=("DQ.WPD" "QXZ.WPD" "NBQ.WPD" "THEROY.WPD" "CDQ.WPD" "NWP.WPD")

# 日志配置
DOWNLOAD_LOG_DIR="/home/<USER>/sftplog"
DOWNLOAD_LOG_FILE="$DOWNLOAD_LOG_DIR/download_history.log"
LOCK_FILE="/var/lock/sftp_auto_download.lock"

# 日志函数
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message"
}

# 记录下载日志
log_download() {
    local remote_path="$1"
    local local_path="$2"
    local filename="$3"
    local filesize="$4"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    mkdir -p "$DOWNLOAD_LOG_DIR"
    echo "$timestamp|$remote_path|$local_path|$filename|$filesize" >> "$DOWNLOAD_LOG_FILE"
    log "INFO" "记录下载: $filename ($filesize bytes)"
}

# 检查文件是否已下载
is_file_downloaded() {
    local remote_dir="$1"
    local filename="$2"
    
    if [ -f "$DOWNLOAD_LOG_FILE" ]; then
        grep -q "|$remote_dir|.*|$filename|" "$DOWNLOAD_LOG_FILE"
        return $?
    fi
    return 1
}

# 获取目录中的最新文件（按类型）
get_latest_files_by_type() {
    local remote_dir="$1"
    local found_files=()
    
    log "INFO" "获取目录文件列表: $remote_dir"
    
    # 获取远程文件列表
    local file_list=$(sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "ls -la '$remote_dir'" 2>/dev/null)
    
    if [ $? -ne 0 ] || [ -z "$file_list" ]; then
        log "ERROR" "无法获取文件列表: $remote_dir"
        return 1
    fi
    
    # 为每种文件类型找最新的文件
    for ext in "${FILE_EXTENSIONS[@]}"; do
        local latest_file=""
        local latest_time=""
        
        log "DEBUG" "查找类型: $ext"
        
        # 查找该类型的文件
        while IFS= read -r line; do
            # 跳过目录行和特殊行
            if [[ "$line" =~ ^d ]] || [[ "$line" =~ ^total ]] || [[ "$line" =~ ^\. ]]; then
                continue
            fi
            
            # 提取文件名
            local filename=$(echo "$line" | awk '{print $NF}')
            
            # 检查是否是目标文件类型
            if [[ "$filename" == *"$ext" ]]; then
                log "DEBUG" "找到文件: $filename"
                
                # 检查是否已下载
                if ! is_file_downloaded "$remote_dir" "$filename"; then
                    log "DEBUG" "文件未下载: $filename"
                    
                    # 提取时间信息（月 日 时间）
                    local month=$(echo "$line" | awk '{print $6}')
                    local day=$(echo "$line" | awk '{print $7}')
                    local time_or_year=$(echo "$line" | awk '{print $8}')
                    
                    # 构建比较用的时间字符串
                    local file_time="$month $day $time_or_year"
                    
                    # 如果没有记录或者文件更新，则更新最新文件
                    if [ -z "$latest_file" ] || [[ "$file_time" > "$latest_time" ]]; then
                        latest_file="$filename"
                        latest_time="$file_time"
                        log "DEBUG" "更新最新文件: $latest_file ($latest_time)"
                    fi
                else
                    log "DEBUG" "文件已下载，跳过: $filename"
                fi
            fi
        done <<< "$file_list"
        
        # 如果找到新的最新文件，添加到列表
        if [ -n "$latest_file" ]; then
            found_files+=("$latest_file")
            log "INFO" "发现新文件: $latest_file (类型: $ext)"
        else
            log "DEBUG" "未找到类型 $ext 的新文件"
        fi
    done
    
    printf '%s\n' "${found_files[@]}"
}

# 下载文件
download_file() {
    local remote_dir="$1"
    local local_dir="$2"
    local filename="$3"
    
    # 确保本地目录存在
    mkdir -p "$local_dir"
    
    local remote_file="$remote_dir/$filename"
    local local_file="$local_dir/$filename"
    
    log "INFO" "下载文件: $filename"
    
    # 使用SCP下载
    if sshpass -p "$REMOTE_PASS" scp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST:$remote_file" "$local_file" 2>/dev/null; then
        if [ -f "$local_file" ]; then
            local filesize=$(stat -c%s "$local_file" 2>/dev/null || echo "0")
            log "INFO" "下载成功: $filename ($filesize bytes)"
            log_download "$remote_dir" "$local_dir" "$filename" "$filesize"
            return 0
        fi
    fi
    
    log "ERROR" "下载失败: $filename"
    return 1
}

# 处理单个目录
process_directory() {
    local remote_dir="$1"
    local local_dir="$2"
    
    log "INFO" "处理目录: $remote_dir -> $local_dir"
    
    # 获取需要下载的文件
    local files_to_download=$(get_latest_files_by_type "$remote_dir")
    
    if [ -z "$files_to_download" ]; then
        log "INFO" "目录 $remote_dir 没有新文件需要下载"
        return 0
    fi
    
    # 下载文件
    local download_count=0
    local total_files=$(echo "$files_to_download" | wc -l)
    
    while IFS= read -r filename; do
        if [ -n "$filename" ]; then
            if download_file "$remote_dir" "$local_dir" "$filename"; then
                ((download_count++))
            fi
        fi
    done <<< "$files_to_download"
    
    log "INFO" "目录处理完成: $remote_dir (下载了 $download_count/$total_files 个文件)"
    return 0
}

# 检查并创建锁文件（防止重复执行）
acquire_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            log "WARN" "另一个实例正在运行 (PID: $pid)"
            exit 1
        else
            log "INFO" "清理过期的锁文件"
            rm -f "$LOCK_FILE"
        fi
    fi
    
    echo $$ > "$LOCK_FILE"
    trap 'rm -f "$LOCK_FILE"' EXIT INT TERM
}

# 显示下载历史
show_history() {
    if [ ! -f "$DOWNLOAD_LOG_FILE" ]; then
        log "INFO" "暂无下载历史记录"
        return 0
    fi
    
    echo "=== 下载历史记录 (最近20条) ==="
    echo "时间                 | 远程路径                                    | 本地路径              | 文件名                | 大小(bytes)"
    echo "-------------------- | ------------------------------------------- | -------------------- | -------------------- | -----------"
    
    tail -20 "$DOWNLOAD_LOG_FILE" | while IFS='|' read -r timestamp remote_path local_path filename filesize; do
        printf "%-20s | %-43s | %-20s | %-20s | %s\n" "$timestamp" "$remote_path" "$(basename "$local_path")" "$filename" "$filesize"
    done
}

# 主函数
main() {
    local action="${1:-run}"
    
    case "$action" in
        "run"|"--run")
            acquire_lock
            log "INFO" "========== 开始SFTP下载任务 =========="
            
            # 测试连接
            if ! sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo '连接测试'" >/dev/null 2>&1; then
                log "ERROR" "连接测试失败"
                exit 1
            fi
            log "INFO" "连接测试成功"
            
            # 处理每个目录
            local success_count=0
            local total_count=${#DIR_MAPPINGS[@]}
            
            for remote_dir in "${!DIR_MAPPINGS[@]}"; do
                local local_dir="${DIR_MAPPINGS[$remote_dir]}"
                if process_directory "$remote_dir" "$local_dir"; then
                    ((success_count++))
                fi
            done
            
            log "INFO" "下载任务完成: $success_count/$total_count 个目录处理成功"
            
            if [ $success_count -eq $total_count ]; then
                log "INFO" "========== 下载任务成功 =========="
                exit 0
            else
                log "ERROR" "========== 下载任务部分失败 =========="
                exit 1
            fi
            ;;
        "history"|"--history")
            show_history
            ;;
        "help"|"--help"|"-h")
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  run, --run      执行下载任务 (默认)"
            echo "  history, --history  显示下载历史"
            echo "  help, --help    显示帮助信息"
            ;;
        *)
            log "ERROR" "未知选项: $action"
            echo "使用 '$0 --help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"