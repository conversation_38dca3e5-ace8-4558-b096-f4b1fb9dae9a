#include "dbmanager.h"
#include <QSqlDatabase>
#include <QSqlError>
#include <QSqlQuery>
#include <QDateTime>
#include <QTimer>

DBManager* DBManager::m_instance = nullptr;

DBManager* DBManager::instance()
{
    if (!m_instance) {
        m_instance = new DBManager();
    }
    return m_instance;
}

DBManager::DBManager(QObject *parent) : QObject(parent), 
    m_connected(false),
    m_autoReconnect(true),
    m_reconnectAttempts(0),
    m_maxReconnectAttempts(5)
{
    // 初始化重连定时器
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setInterval(5000); // 5秒尝试重连一次
    connect(m_reconnectTimer, &QTimer::timeout, this, &DBManager::tryReconnect);
}

DBManager::~DBManager()
{
    if (m_connected) {
        disconnectFromDatabase();
    }
    
    if (m_reconnectTimer && m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }
}

bool DBManager::connectToDatabase(const QString &host, int port, const QString &username, 
                                 const QString &password, const QString &dbName)
{
    // 保存连接信息，用于自动重连
    m_host = host;
    m_port = port;
    m_username = username;
    m_password = password;
    m_dbName = dbName;
    
    // 如果已经连接，先断开
    if (m_connected) {
        disconnectFromDatabase();
    }
    
    // 创建并打开数据库连接
    bool ok = createAndOpenConnection();
    
    if (ok) {
        m_connected = true;
        m_lastError = "";
        m_reconnectAttempts = 0; // 重置重连计数
        if (m_reconnectTimer->isActive()) {
            m_reconnectTimer->stop(); // 连接成功，停止重连定时器
        }
        emit connectionStatusChanged(true);
    } else {
        m_connected = false;
        emit connectionStatusChanged(false);
        
        // 如果启用了自动重连，启动重连定时器
        if (m_autoReconnect && !m_reconnectTimer->isActive()) {
            m_reconnectTimer->start();
        }
    }
    
    return ok;
}

bool DBManager::connectToDatabase(const QString &host, const QString &username, 
                                 const QString &password, const QString &dbName)
{
    // 使用默认端口3306
    return connectToDatabase(host, 3306, username, password, dbName);
}

bool DBManager::testConnection(const QString &host, int port, const QString &username, 
                             const QString &password, const QString &dbName)
{
    // 创建临时连接名称
    QString connectionName = "test_connection_" + QString::number(QDateTime::currentMSecsSinceEpoch());
    bool ok = false;
    
    {
        // 创建临时数据库连接
        {
            QSqlDatabase testDb = QSqlDatabase::addDatabase("QMYSQL", connectionName);
            testDb.setHostName(host);
            testDb.setPort(port);
            testDb.setUserName(username);
            testDb.setPassword(password);
            testDb.setDatabaseName(dbName);
            
            // 尝试连接
            ok = testDb.open();
            
            if (ok) {
                m_lastError = "";
            } else {
                m_lastError = testDb.lastError().text();
                qDebug() << "测试连接失败:" << m_lastError;
            }
            
            // 确保关闭连接
            testDb.close();
        }
        
        // 确保在所有返回路径上都移除临时连接
        QSqlDatabase::removeDatabase(connectionName);
    }
    
    return ok;
}

void DBManager::disconnectFromDatabase()
{
    if (m_connected) {
        // 获取默认连接
        if (QSqlDatabase::contains("qt_sql_default_connection")) {
            QSqlDatabase db = QSqlDatabase::database("qt_sql_default_connection");
            // 关闭连接
            if (db.isOpen()) {
                db.close();
            }
            
            // 移除连接前确保没有活动的查询
            QSqlDatabase::removeDatabase("qt_sql_default_connection");
        }
        
        m_connected = false;
        emit connectionStatusChanged(false);
    }
}

bool DBManager::isConnected() const
{
    return m_connected;
}

QString DBManager::lastError() const
{
    return m_lastError;
}

QSqlQuery DBManager::executeQuery(const QString &queryStr)
{
    QSqlQuery query;
    
    try {
        if (m_connected) {
            // 检查查询字符串是否为空
            if (queryStr.trimmed().isEmpty()) {
                m_lastError = "查询语句为空";
                emit queryError(m_lastError);
                return query;
            }
            
            // 检查数据库连接是否有效
            QSqlDatabase db = QSqlDatabase::database();
            if (!db.isValid() || !db.isOpen()) {
                m_lastError = "数据库连接无效或已关闭";
                qDebug() << "数据库连接无效或已关闭";
                emit queryError(m_lastError);
                m_connected = false;  // 更新连接状态
                emit connectionStatusChanged(false);  // 通知连接状态变化
                return query;
            }
            
            // 尝试重新连接数据库
            if (!db.isOpen()) {
                if (!db.open()) {
                    m_lastError = "无法重新打开数据库连接: " + db.lastError().text();
                    qDebug() << "无法重新打开数据库连接:" << m_lastError;
                    emit queryError(m_lastError);
                    m_connected = false;
                    emit connectionStatusChanged(false);
                    return query;
                }
            }
            
            // 创建新的查询对象
            query = QSqlQuery(db);
            
            // 执行查询
            if (!query.exec(queryStr)) {
                m_lastError = query.lastError().text();
                qDebug() << "查询失败:" << m_lastError;
                emit queryError(m_lastError);
            } else {
                // 准确计算查询结果的行数
                if (queryStr.trimmed().toUpper().startsWith("SELECT")) {
                    // 对于DISTINCT查询，我们不使用COUNT(*)，因为可能非常慢
                    // 而是在后续处理中计数
                    if (!queryStr.trimmed().toUpper().contains("DISTINCT")) {
                        // 执行额外的COUNT查询获取精确行数
                        QString countQueryStr = queryStr;
                        
                        // 提取原始查询中的WHERE子句和FROM子句
                        int fromIndex = countQueryStr.toUpper().indexOf(" FROM ");
                        if (fromIndex > 0) {
                            // 提取FROM及之后的部分
                            QString fromPart = countQueryStr.mid(fromIndex);
                            
                            // 移除ORDER BY、LIMIT等子句，它们不影响结果数量
                            int orderByIndex = fromPart.toUpper().indexOf(" ORDER BY ");
                            int limitIndex = fromPart.toUpper().indexOf(" LIMIT ");
                            int groupByIndex = fromPart.toUpper().indexOf(" GROUP BY ");
                            
                            if (orderByIndex > 0) {
                                fromPart = fromPart.left(orderByIndex);
                            } else if (limitIndex > 0) {
                                fromPart = fromPart.left(limitIndex);
                            } else if (groupByIndex > 0 && orderByIndex < 0 && limitIndex < 0) {
                                // 如果有GROUP BY但没有ORDER BY和LIMIT，需要保留GROUP BY
                            }
                            
                            // 创建新的COUNT查询
                            QString countSQL = "SELECT COUNT(*) " + fromPart;
                            QSqlQuery countQuery(db);
                            if (!countQuery.exec(countSQL) || !countQuery.next()) {
                                // 如果COUNT查询失败，退回到近似计数
                                // 这种方法不遍历结果，不会影响原始查询
                                query.last();
                                // 重置查询位置
                                query.seek(-1); // 回到第一行前
                            }
                        }
                    }
                }
            }
        } else {
            m_lastError = "数据库未连接";
            qDebug() << "数据库未连接";
            emit queryError(m_lastError);
        }
    } catch (const std::exception &e) {
        m_lastError = QString("执行查询时发生异常: %1").arg(e.what());
        qDebug() << "执行查询异常:" << m_lastError;
        emit queryError(m_lastError);
    } catch (...) {
        m_lastError = "执行查询时发生未知异常";
        qDebug() << "执行查询时发生未知异常";
        emit queryError(m_lastError);
    }
    
    return query;
}

bool DBManager::isAutoReconnectEnabled() const
{
    return m_autoReconnect;
}

void DBManager::setAutoReconnect(bool enable)
{
    m_autoReconnect = enable;
    
    if (enable && !m_connected && !m_reconnectTimer->isActive() && 
        !m_host.isEmpty() && m_reconnectAttempts < m_maxReconnectAttempts) {
        // 如果启用自动重连，且当前未连接，且定时器未启动，则启动重连定时器
        m_reconnectTimer->start();
    } else if (!enable && m_reconnectTimer->isActive()) {
        // 如果禁用自动重连，且定时器正在运行，则停止定时器
        m_reconnectTimer->stop();
    }
}

void DBManager::tryReconnect()
{
    // 超过最大重试次数，停止重试
    if (m_reconnectAttempts >= m_maxReconnectAttempts) {
        qDebug() << "达到最大重连尝试次数，停止重连";
        m_reconnectTimer->stop();
        return;
    }
    
    // 增加重试计数
    m_reconnectAttempts++;
    
    emit reconnectAttempt(m_reconnectAttempts);
    
    // 尝试重新连接
    bool ok = createAndOpenConnection();
    
    if (ok) {
        m_connected = true;
        m_lastError = "";
        m_reconnectAttempts = 0; // 重置重连计数
        m_reconnectTimer->stop(); // 连接成功，停止重连定时器
        qDebug() << "数据库重连成功";
        emit connectionStatusChanged(true);
    } else {
        m_connected = false;
        qDebug() << "数据库重连失败:" << m_lastError;
    }
}

bool DBManager::createAndOpenConnection()
{
    // 确保没有活动的默认连接
    if (QSqlDatabase::contains("qt_sql_default_connection")) {
        QSqlDatabase oldDb = QSqlDatabase::database("qt_sql_default_connection");
        if (oldDb.isOpen()) {
            oldDb.close();
        }
        QSqlDatabase::removeDatabase("qt_sql_default_connection");
    }
    
    // 创建新的数据库连接
    QSqlDatabase db = QSqlDatabase::addDatabase("QMYSQL");
    
    // 设置连接参数
    db.setHostName(m_host);
    db.setPort(m_port);
    db.setUserName(m_username);
    db.setPassword(m_password);
    db.setDatabaseName(m_dbName);
    
    // 设置连接选项，启用自动重连和更长的超时时间
    db.setConnectOptions("MYSQL_OPT_RECONNECT=1;MYSQL_OPT_CONNECT_TIMEOUT=30");
    
    // 尝试连接
    bool ok = db.open();
    
    if (!ok) {
        m_lastError = db.lastError().text();
        qDebug() << "数据库连接失败:" << m_lastError;
    }
    
    return ok;
} 