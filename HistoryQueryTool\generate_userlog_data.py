#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import random
import datetime
import time
import sys

# 数据库连接信息
DB_CONFIG = {
    "host": "************",
    "port": 3306,
    "user": "root",
    "password": "root",
    "database": "his_mysql",
    "charset": "utf8mb4"
}

# 默认生成的记录数
DEFAULT_RECORDS = 50000

# 登录类型定义
LOGIN_TYPES = {
    0: "登录",
    1: "登出",
    2: "登录失败",
    3: "会话超时"
}

# 主机列表
HOSTNAMES = ["SCADA01", "SCADA02", "HMI01", "HMI02", "HISTORIAN", "EMS01", "CLIENT01", "CLIENT02", "CLIENT03"]

# 应用名称列表
APP_NAMES = ["HistoryQueryTool", "AlarmViewer", "ScadaClient", "ControlPanel", "ConfigManager", "DiagnosticTool"]

# 用户名列表
USERNAMES = ["admin", "operator", "engineer", "supervisor", "user1", "user2", "guest", "system", "maintenance"]

def print_progress(current, total, prefix='', suffix='', decimals=1, bar_length=50):
    """打印进度条"""
    percent = ("{0:." + str(decimals) + "f}").format(100 * (current / float(total)))
    filled_length = int(bar_length * current // total)
    bar = '#' * filled_length + '-' * (bar_length - filled_length)
    sys.stdout.write('\r%s |%s| %s%% %s' % (prefix, bar, percent, suffix))
    sys.stdout.flush()
    if current == total:
        print()

def get_random_datetime(start_days=30, end_days=0):
    """生成随机日期时间
    
    Args:
        start_days: 开始日期距今的天数
        end_days: 结束日期距今的天数
    
    Returns:
        随机生成的datetime对象
    """
    # 获取当前时间
    end_date = datetime.datetime.now() - datetime.timedelta(days=end_days)
    
    # 生成指定范围内的记录
    start_date = end_date - datetime.timedelta(days=start_days)
    
    # 在时间范围内随机选择一个时间点
    time_delta = end_date - start_date
    random_seconds = random.randint(0, int(time_delta.total_seconds()))
    random_date = start_date + datetime.timedelta(seconds=random_seconds)
    
    return random_date

def connect_to_db():
    """连接到MySQL数据库"""
    try:
        print(f"正在连接到 {DB_CONFIG['host']} 的数据库 {DB_CONFIG['database']}...")
        connection = pymysql.connect(**DB_CONFIG)
        print("数据库连接成功!")
        return connection
    except Exception as e:
        print(f"连接数据库失败: {e}")
        return None

def create_table_if_not_exists(connection):
    """创建htuserlog表（如果不存在）"""
    print("检查并创建htuserlog表...")
    with connection.cursor() as cursor:
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS htuserlog (
            id INT(11) NOT NULL AUTO_INCREMENT,
            username VARCHAR(50) NOT NULL,
            starttime DATETIME NOT NULL,
            logtype SMALLINT(6) NOT NULL,
            hostname VARCHAR(50) NOT NULL,
            appname VARCHAR(50) NOT NULL,
            PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        """
        cursor.execute(create_table_sql)
        connection.commit()
        print("表创建完成!")

def generate_user_login_data(cursor, count):
    """生成用户登录记录数据"""
    print("正在生成用户登录记录数据...")
    
    records = []
    batch_size = 10000  # 每批处理的记录数
    
    for i in range(count):
        # 随机生成数据
        username = random.choice(USERNAMES)
        starttime = get_random_datetime()
        logtype = random.randint(0, 3)  # 0-3对应不同的登录类型
        hostname = random.choice(HOSTNAMES)
        appname = random.choice(APP_NAMES)
        
        # 创建记录元组
        record = (username, starttime, logtype, hostname, appname)
        records.append(record)
        
        # 批量提交，提高性能
        if len(records) >= batch_size:
            insert_batch(cursor, records)
            records = []
            connection.commit()
            print_progress(i + 1, count, prefix='进度:', suffix='完成', bar_length=50)
    
    # 插入剩余记录
    if records:
        insert_batch(cursor, records)
        connection.commit()
    
    print_progress(count, count, prefix='进度:', suffix='完成!', bar_length=50)
    print("\n用户登录记录数据生成完成!")

def insert_batch(cursor, records):
    """批量插入数据"""
    insert_sql = """
    INSERT INTO htuserlog (username, starttime, logtype, hostname, appname)
    VALUES (%s, %s, %s, %s, %s)
    """
    
    try:
        cursor.executemany(insert_sql, records)
    except Exception as e:
        print(f"\n插入数据失败: {e}")
        connection.rollback()
        raise

def get_record_count():
    """获取用户希望生成的记录数量"""
    try:
        print(f"请输入要生成的登录记录数量 [默认: {DEFAULT_RECORDS}]: ", end='')
        user_input = input().strip()
        
        if not user_input:
            return DEFAULT_RECORDS
            
        count = int(user_input)
        if count <= 0:
            print(f"输入无效，使用默认值 {DEFAULT_RECORDS}")
            return DEFAULT_RECORDS
            
        return count
    except ValueError:
        print(f"输入无效，使用默认值 {DEFAULT_RECORDS}")
        return DEFAULT_RECORDS

if __name__ == "__main__":
    try:
        # 连接数据库
        connection = connect_to_db()
        if not connection:
            sys.exit(1)
        
        cursor = connection.cursor()
        
        # 创建表（如果不存在）
        create_table_if_not_exists(connection)
        
        # 获取记录数量
        num_records = get_record_count()
        
        # 生成数据
        generate_user_login_data(cursor, num_records)
        
        # 关闭连接
        cursor.close()
        connection.close()
        print("数据生成完成，数据库连接已关闭!")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1) 