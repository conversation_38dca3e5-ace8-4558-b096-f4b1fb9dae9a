#include "telemetrydataprovider.h"
#include "../../database/dbmanager.h"
#include "../../common/constants.h"
#include "../../models/telemetrydatamodel.h"
#include "../../utils/xmlconfigreader.h"
#include "../../database/dbconnectionfactory.h"
#include "../../common/tablenamemanager.h"
#include <QSqlError>
#include <QDebug>
#include <QSqlDatabase>
#include <QSqlRecord>
#include <QSet>
#include <QDateTime>
#include <QMap>
#include <QMapIterator>
#include <QSortFilterProxyModel>
#include <algorithm>

TelemetryDataProvider::TelemetryDataProvider(QObject *parent)
    : QObject(parent), m_dbManager(nullptr)
{
    // 设置表名前缀
    m_minuteTablePrefix = "htdaystore_min_"; // 分钟级遥测历史表前缀
    m_secondTablePrefix = "htdaystore_sec_"; // 秒级遥测历史表前缀
    m_telemetryParamTable = Constants::Tables::PARA_YC;  // 遥测参数表
}

void TelemetryDataProvider::setDBManager(DBManager *dbManager)
{
    m_dbManager = dbManager;
}

QSqlQuery TelemetryDataProvider::executeMinuteTelemetryQuery(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    QSqlQuery query;
    if (!isDatabaseConnected()) {
        qDebug() << "数据库未连接，无法执行分钟级遥测查询";
        return query;
    }

    // 检查是否是跨日期查询
    if (startTime.date() != endTime.date()) {
        // 跨日期查询需要特殊处理
        return executeCrossDayQuery(deviceId, deviceIndex, startTime, endTime, true);
    }

    // 检查表是否存在
    QString tableName = getMinuteTableName(startTime.date());
    if (!checkTableExists(tableName)) {
        qDebug() << "分钟级遥测表不存在:" << tableName;
        // 执行一个会产生错误的查询，用于传递表不存在的信息
        QString errorSql = QString("SELECT '当日没有数据：表 %1 不存在' AS error_message").arg(tableName);
        query = m_dbManager->executeQuery(errorSql);
        // 手动设置一个错误标识，通过查询一个不存在的表来产生错误
        QString fakeErrorSql = QString("SELECT * FROM `%1_TABLE_NOT_EXISTS`").arg(tableName);
        query = m_dbManager->executeQuery(fakeErrorSql);
        return query;
    }

    // 构建分钟级查询SQL
    QString sql = buildMinuteTelemetrySQL(deviceId, deviceIndex, startTime, endTime);
    qDebug() << "执行分钟级遥测查询SQL:" << sql;

    if (m_dbManager) {
        query = m_dbManager->executeQuery(sql);
        if (query.lastError().isValid()) {
            qDebug() << "分钟级遥测查询失败:" << query.lastError().text();
        } else {
            qDebug() << "分钟级遥测查询成功";
        }
    }

    return query;
}

QSqlQuery TelemetryDataProvider::executeSecondTelemetryQuery(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    QSqlQuery query;
    if (!isDatabaseConnected()) {
        qDebug() << "数据库未连接，无法执行秒级遥测查询";
        return query;
    }

    // 检查是否是跨日期查询
    if (startTime.date() != endTime.date()) {
        // 跨日期查询需要特殊处理
        return executeCrossDayQuery(deviceId, deviceIndex, startTime, endTime, false);
    }

    // 检查表是否存在
    QString tableName = getSecondTableName(startTime.date());
    if (!checkTableExists(tableName)) {
        qDebug() << "秒级遥测表不存在:" << tableName;
        // 执行一个会产生错误的查询，用于传递表不存在的信息
        QString errorSql = QString("SELECT '当日没有数据：表 %1 不存在' AS error_message").arg(tableName);
        query = m_dbManager->executeQuery(errorSql);
        // 手动设置一个错误标识，通过查询一个不存在的表来产生错误
        QString fakeErrorSql = QString("SELECT * FROM `%1_TABLE_NOT_EXISTS`").arg(tableName);
        query = m_dbManager->executeQuery(fakeErrorSql);
        return query;
    }

    // 构建秒级查询SQL
    QString sql = buildSecondTelemetrySQL(deviceId, deviceIndex, startTime, endTime);
    qDebug() << "执行秒级遥测查询SQL:" << sql;

    if (m_dbManager) {
        query = m_dbManager->executeQuery(sql);
        if (query.lastError().isValid()) {
            qDebug() << "秒级遥测查询失败:" << query.lastError().text();
        } else {
            qDebug() << "秒级遥测查询成功";
        }
    }

    return query;
}

QString TelemetryDataProvider::buildMinuteTelemetrySQL(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    // 获取日期对应的表名
    QString tableName = getMinuteTableName(startTime.date());
    
    // 根据实际表结构（equipid, linkid, insertDate, curvevalue）构建查询SQL
    // 只查询必要的字段：insertDate和curvevalue
    QString sql = QString("SELECT insertDate, curvevalue "
                         "FROM %1 WHERE equipid = %2 AND linkid = %3 "
                         "AND insertDate BETWEEN %4 AND %5 ORDER BY insertDate")
                .arg(tableName)
                .arg(deviceId)
                .arg(deviceIndex)
                .arg(startTime.toSecsSinceEpoch()) 
                .arg(endTime.toSecsSinceEpoch());

    return sql;
}

QString TelemetryDataProvider::buildSecondTelemetrySQL(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    // 获取日期对应的表名
    QString tableName = getSecondTableName(startTime.date());
    
    // 根据实际表结构（equipid, linkid, insertDate, curvevalue）构建查询SQL
    // 应用时间偏移修正：insertDate记录的是写入时间，实际采集时间早5分钟
    QString sql = QString("SELECT insertDate, FROM_UNIXTIME(insertDate + %6) as FormattedDateTime, curvevalue "
                         "FROM %1 WHERE equipid = %2 AND linkid = %3 "
                         "AND insertDate BETWEEN %4 AND %5 ORDER BY insertDate")
                .arg(tableName)
                .arg(deviceId)
                .arg(deviceIndex)
                .arg(startTime.toSecsSinceEpoch())
                .arg(endTime.toSecsSinceEpoch())
                .arg(TIME_OFFSET_SECONDS);

    return sql;
}

QMap<QString, QVariant> TelemetryDataProvider::getTelemetryProperties(int deviceId, int deviceIndex)
{
    QMap<QString, QVariant> properties;
    
    // 验证参数数据库配置
    XMLConfigReader *configReader = XMLConfigReader::instance();
    if (!configReader || !configReader->isParaDBConfigValid()) {
        qDebug() << "参数库配置无效或未加载";
        return properties;
    }
    
    // 创建参数数据库连接
    QString connectionName = "para_db_connection_" + QString::number(QDateTime::currentMSecsSinceEpoch());
    QSqlDatabase paraDB = DBConnectionFactory::createParaDBConnection(connectionName);
    
    if (!paraDB.open()) {
        qDebug() << "连接参数数据库失败:" << paraDB.lastError().text();
        QSqlDatabase::removeDatabase(connectionName);
        return properties;
    }

    // 查询遥测参数表，获取遥测点的属性信息
    QString sql = QString("SELECT Name, Unit, Rate, Offset FROM %1 "
                         "WHERE DeviceID = %2 AND DeviceIndex = %3")
                    .arg(m_telemetryParamTable)
                    .arg(deviceId)
                    .arg(deviceIndex);

    QSqlQuery query(paraDB);
    if (query.exec(sql)) {
        if (query.next()) {
            properties["Name"] = query.value("Name");
            properties["Unit"] = query.value("Unit");
            properties["Rate"] = query.value("Rate");
            properties["Offset"] = query.value("Offset");
            qDebug() << "成功获取遥测点属性信息:" << deviceId << deviceIndex << properties["Name"].toString();
        } else {
            qDebug() << "未找到遥测点属性信息:" << deviceId << deviceIndex;
        }
    } else {
        qDebug() << "查询失败:" << query.lastError().text();
    }
    
    // 关闭并移除临时连接
    paraDB.close();
    QSqlDatabase::removeDatabase(connectionName);

    return properties;
}

QVector<TelemetryDataPoint> TelemetryDataProvider::createDataPointsFromBlob(
    qint64 insertDateSecs,
    const QByteArray &blobData,
    bool isMinuteData)
{
    QVector<TelemetryDataPoint> dataPoints;
    
    // 检查参数有效性
    if (blobData.isEmpty()) {
        qWarning() << "createDataPointsFromBlob: BLOB数据为空";
        return dataPoints;
    }
    
    // HIS_DATA_DEVICE_VALUE结构体大小
    const int STRUCT_SIZE = sizeof(unsigned char) + sizeof(float);
    
    // 计算数组中的元素数量
    int totalElements = blobData.size() / STRUCT_SIZE;
    if (totalElements == 0) {
        qWarning() << "createDataPointsFromBlob: BLOB数据大小异常:" << blobData.size() << "字节";
        return dataPoints;
    }
    
    // 计算时间间隔（秒）
    int timeInterval = isMinuteData ? 60 : 1;  // 分钟数据间隔60秒，秒级数据间隔1秒
    
    // 解析每个数据点
    for (int i = 0; i < totalElements; i++) {
        const char* dataPtr = blobData.constData() + (i * STRUCT_SIZE);
        
        // 读取status和value
        unsigned char status = static_cast<unsigned char>(dataPtr[0]);
        float value;
        memcpy(&value, dataPtr + sizeof(unsigned char), sizeof(float));
        
        // 计算当前数据点的时间戳
        // 应用时间偏移修正：insertDate记录的是写入时间，实际采集时间早5分钟
        qint64 pointTimestamp = insertDateSecs + TIME_OFFSET_SECONDS + (i * timeInterval);
        
        // 创建数据点
        TelemetryDataPoint point;
        point.timestamp = QDateTime::fromSecsSinceEpoch(pointTimestamp);
        point.value = value;
        // status=1为正常，=0为异常
        point.quality = (status == 1) ? 1 : 0;  // 1表示正常，0表示异常
        
        dataPoints.append(point);
        
        // 调试输出
        if (i < 5 || i > totalElements - 5) {  // 只输出前5个和后5个数据点
            qDebug() << "解析数据点[" << i << "]:"
                     << "时间=" << point.timestamp.toString("yyyy-MM-dd HH:mm:ss")
                     << "值=" << value
                     << "质量=" << (point.quality == 1 ? "正常" : "异常");
        }
    }
    
    qDebug() << "成功解析" << dataPoints.size() << "个数据点";
    return dataPoints;
}

QVector<TelemetryDataPoint> TelemetryDataProvider::convertQueryToDataPoints(QSqlQuery &query, bool isMinuteData)
{
    QVector<TelemetryDataPoint> allDataPoints;
    
    // 检查查询是否有效
    if (!query.isActive()) {
        qWarning() << "convertQueryToDataPoints: 查询不活跃或无效";
        return allDataPoints;
    }
    
    // 检查查询是否有错误
    if (query.lastError().isValid()) {
        qWarning() << "convertQueryToDataPoints: 查询存在错误:" << query.lastError().text();
        return allDataPoints;
    }
    
    // 获取字段索引
    int insertDateIndex = query.record().indexOf("insertDate");
    int formattedDateTimeIndex = query.record().indexOf("FormattedDateTime");
    int curveValueIndex = query.record().indexOf("curvevalue");

    if (insertDateIndex == -1 || curveValueIndex == -1) {
        qWarning() << "convertQueryToDataPoints: 查询不包含insertDate或curvevalue列。";
        qWarning() << "可用列:";
        for (int i = 0; i < query.record().count(); i++) {
            qWarning() << " - " << query.record().fieldName(i);
        }
        return allDataPoints;
    }

    // 检查游标位置，如果需要，重新将游标移动到第一条记录之前
    if (query.at() != QSql::BeforeFirstRow) {
        qDebug() << "查询游标不在起始位置，尝试重新定位...";
        query.first();
        query.previous(); // 移动到第一条记录之前
    }
    
    int processedCount = 0;
    int errorCount = 0;
    int totalRecords = 0;
    
    // 获取总记录数
    if (query.isSelect()) {
        query.last();
        totalRecords = query.at() + 1;
        query.first();
        query.previous();
    }
    
    qDebug() << "开始处理查询结果，总记录数:" << totalRecords;

    while (query.next()) {
        // 防止无效索引访问
        if (insertDateIndex >= query.record().count() || curveValueIndex >= query.record().count()) {
            qWarning() << "列索引超出范围: insertDate=" << insertDateIndex 
                      << "curveValue=" << curveValueIndex
                      << "记录字段数=" << query.record().count();
            errorCount++;
            continue;
        }
        
        // 安全获取字段值
        QVariant insertDateVar = query.value(insertDateIndex);
        QVariant curveValueVar = query.value(curveValueIndex);
        
        if (insertDateVar.isNull() || curveValueVar.isNull()) {
            qWarning() << "跳过包含空值的记录";
            errorCount++;
            continue;
        }
        
        // 转换字段值
        bool ok;
        qint64 insertDateSecs = insertDateVar.toLongLong(&ok);
        if (!ok) {
            qWarning() << "无法将 insertDate 值转换为整数:" << insertDateVar.toString();
            errorCount++;
            continue;
        }
        
        // 验证时间戳的有效性
        if (insertDateSecs <= 0) {
            qWarning() << "无效的时间戳值:" << insertDateSecs;
            errorCount++;
            continue;
        }
        
        QByteArray blobData = curveValueVar.toByteArray();
        if (blobData.isEmpty()) {
            qWarning() << "跳过空的 curvevalue BLOB 数据";
            errorCount++;
            continue;
        }
        
        // 验证BLOB数据大小
        if (blobData.size() < 4) { // 假设最小有效BLOB大小为4字节
            qWarning() << "BLOB数据大小异常:" << blobData.size() << "字节";
            errorCount++;
            continue;
        }
        
        // 转换时间戳为可读格式并输出调试信息
        QDateTime dateTime = QDateTime::fromSecsSinceEpoch(insertDateSecs);
        QString formattedDateTime = "未格式化";
        
        if (formattedDateTimeIndex != -1 && formattedDateTimeIndex < query.record().count()) {
            QVariant formattedVar = query.value(formattedDateTimeIndex);
            if (!formattedVar.isNull()) {
                formattedDateTime = formattedVar.toString();
            }
        } else {
            formattedDateTime = dateTime.toString("yyyy-MM-dd HH:mm:ss");
        }
            
        qDebug() << "处理数据点: 时间戳=" << insertDateSecs 
                 << "日期时间=" << formattedDateTime
                 << "BLOB大小=" << blobData.size() << "字节";
        
        // 使用新方法解析BLOB数据并创建数据点
        QVector<TelemetryDataPoint> pointsFromBlob = 
            createDataPointsFromBlob(insertDateSecs, blobData, isMinuteData);
            
        if (!pointsFromBlob.isEmpty()) {
        allDataPoints.append(pointsFromBlob);
            processedCount++;
        } else {
            qWarning() << "BLOB数据解析未产生数据点";
            errorCount++;
        }
    }
    
    qDebug() << "转换完成: 成功处理记录数=" << processedCount 
             << ", 错误记录数=" << errorCount
             << ", 总计生成数据点数=" << allDataPoints.size()
             << ", 总记录数=" << totalRecords;

    return allDataPoints;
}

QVector<TelemetryDataPoint> TelemetryDataProvider::filterDataByInterval(
    const QVector<TelemetryDataPoint> &dataPoints,
    int interval,
    bool isMinute)
{
    qDebug() << QString("过滤算法开始: interval=%1, isMinute=%2, 数据点数=%3")
                .arg(interval).arg(isMinute).arg(dataPoints.size());
    
    // 如果间隔为0或1，或者数据点为空，直接返回原始数据点
    if (interval <= 1 || dataPoints.isEmpty()) {
        qDebug() << "条件1触发: 返回原始数据点";
        return dataPoints;
    }
    
    qDebug() << "继续执行过滤逻辑...";
    
    QVector<TelemetryDataPoint> filteredPoints;
    
    // interval参数已经是秒数，不需要再次转换
    int adjustedInterval = interval;
    qDebug() << QString("调整后的间隔: %1秒").arg(adjustedInterval);
    
    // 使用更智能的方法：检测数据的实际间隔
    if (dataPoints.size() < 2) {
        qDebug() << "数据点少于2个，返回原始数据";
        return dataPoints;
    }
    
    // 计算前几个数据点的平均间隔来判断数据的实际密度
    QVector<qint64> intervals;
    for (int i = 1; i < qMin(10, dataPoints.size()); ++i) {
        qint64 timeDiff = dataPoints[i-1].timestamp.secsTo(dataPoints[i].timestamp);
        qDebug() << QString("数据点%1到%2的时间差: %3秒").arg(i-1).arg(i).arg(timeDiff);
        if (timeDiff > 0 && timeDiff < 3600) { // 忽略异常大的时间跳跃（超过1小时）
            intervals.append(timeDiff);
        }
    }
    
    // 如果没有有效的间隔数据，返回原始数据
    if (intervals.isEmpty()) {
        qDebug() << "没有有效的间隔数据，返回原始数据";
        return dataPoints;
    }
    
    // 计算平均间隔
    qint64 avgInterval = 0;
    for (qint64 interval : intervals) {
        avgInterval += interval;
    }
    avgInterval /= intervals.size();
    qDebug() << QString("计算出的平均间隔: %1秒, 目标间隔: %2秒").arg(avgInterval).arg(adjustedInterval);
    
    // 如果原始数据的平均间隔已经大于等于目标间隔，返回所有数据点
    if (avgInterval >= adjustedInterval) {
        qDebug() << "原始数据间隔已满足要求，返回所有数据点";
        return dataPoints;
    }
    
    qDebug() << "开始执行实际过滤...";
    
    // 否则进行过滤
    filteredPoints.append(dataPoints.first());
    QDateTime lastSelectedTime = dataPoints.first().timestamp;
    
    // 遍历剩余的数据点
    for (int i = 1; i < dataPoints.size(); ++i) {
        const TelemetryDataPoint &currentPoint = dataPoints[i];
        
        // 计算与上一个选中点的时间差（秒）
        qint64 timeDiff = lastSelectedTime.secsTo(currentPoint.timestamp);
        
        // 如果时间差大于等于指定间隔，则选择这个点
        if (timeDiff >= adjustedInterval) {
            filteredPoints.append(currentPoint);
            lastSelectedTime = currentPoint.timestamp;
            qDebug() << QString("选择数据点%1, 时间差: %2秒").arg(i).arg(timeDiff);
        }
    }
    
    qDebug() << QString("过滤完成: 原始%1个点 -> 过滤后%2个点").arg(dataPoints.size()).arg(filteredPoints.size());
    
    // 返回过滤后的数据点
    return filteredPoints;
}

bool TelemetryDataProvider::isDatabaseConnected() const
{
    return m_dbManager && m_dbManager->isConnected();
}

QString TelemetryDataProvider::getMinuteTableName(const QDate &date) const
{
    return TableNameManager::getMinuteTelemetryTable(date);
}

QString TelemetryDataProvider::getSecondTableName(const QDate &date) const
{
    return TableNameManager::getSecondTelemetryTable(date);
}

QSqlQuery TelemetryDataProvider::executeCrossDayQuery(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime, bool isMinute)
{
    qDebug() << "执行跨日期查询:" 
             << "DeviceID=" << deviceId 
             << "DeviceIndex=" << deviceIndex
             << "StartTime=" << startTime.toString("yyyy-MM-dd HH:mm:ss")
             << "EndTime=" << endTime.toString("yyyy-MM-dd HH:mm:ss")
             << "Type=" << (isMinute ? "分钟级" : "秒级");
    
    // 创建临时表存放结果
    QString tempTableName = QString("temp_telemetry_blob_%1").arg(QDateTime::currentMSecsSinceEpoch());
    
    // 创建临时表SQL - 结构与原表一致: id, equipid, linkid, insertDate, curvevalue
    QString createTempSQL = QString(
        "CREATE TEMPORARY TABLE %1 ("
        "id INT NOT NULL AUTO_INCREMENT, "
        "equipid INT NOT NULL, "
        "linkid INT NOT NULL, "
        "insertDate INT NOT NULL, "
        "curvevalue LONGBLOB, "
        "PRIMARY KEY (id)"
        ")")
        .arg(tempTableName);
    
    QSqlQuery query; // 用于最终返回的查询对象
    
    if (m_dbManager) {
        // 创建临时表
        QSqlQuery tempQuery = m_dbManager->executeQuery(createTempSQL);
        if (tempQuery.lastError().isValid()) {
            qDebug() << "创建临时表失败:" << tempQuery.lastError().text();
            return tempQuery; // 返回带有错误的查询
        }
        
        // 计算日期范围
        QDate currentDate = startTime.date();
        QDate endDate = endTime.date();
        
        // 遍历日期范围，查询每一天的数据并插入临时表
        while (currentDate <= endDate) {
            QDateTime dayStart = (currentDate == startTime.date()) ? 
                                startTime : 
                                QDateTime(currentDate, QTime(0, 0, 0));
            
            QDateTime dayEnd = (currentDate == endDate) ? 
                              endTime : 
                              QDateTime(currentDate, QTime(23, 59, 59, 999));
            
            QString tableName = isMinute ? 
                               getMinuteTableName(currentDate) : 
                               getSecondTableName(currentDate);
            
            QString checkTableSql = QString(
                "SELECT COUNT(*) FROM information_schema.tables "
                "WHERE table_schema = DATABASE() AND table_name = '%1'")
                .arg(tableName);
            
            QSqlQuery checkQuery = m_dbManager->executeQuery(checkTableSql);
            if (checkQuery.next() && checkQuery.value(0).toInt() > 0) {
                // 直接插入符合条件的所有字段，而不是只插入insertDate和curvevalue
                    QString insertSql = QString(
                    "INSERT INTO %1 (equipid, linkid, insertDate, curvevalue) "
                    "SELECT equipid, linkid, insertDate, curvevalue "
                    "FROM %2 "
                    "WHERE equipid = %3 AND linkid = %4 "
                    "AND insertDate BETWEEN %5 AND %6")
                        .arg(tempTableName)
                    .arg(tableName)
                    .arg(deviceId)
                    .arg(deviceIndex)
                    .arg(dayStart.toSecsSinceEpoch())
                    .arg(dayEnd.toSecsSinceEpoch());
                    
                    QSqlQuery insertQuery = m_dbManager->executeQuery(insertSql);
                    if (insertQuery.lastError().isValid()) {
                        qWarning() << "插入临时表失败:" << insertQuery.lastError().text();
                } else {
                    qDebug() << "成功从表" << tableName << "插入数据到临时表";
                }
            } else {
                qDebug() << "表不存在或无法访问:" << tableName;
            }
            
            currentDate = currentDate.addDays(1);
        }
        
        // 查询临时表，获取最终结果，添加时间格式化
        // 应用时间偏移修正：insertDate记录的是写入时间，实际采集时间早5分钟
        QString finalSql = QString(
            "SELECT insertDate, FROM_UNIXTIME(insertDate + %2) as FormattedDateTime, curvevalue "
            "FROM %1 ORDER BY insertDate")
            .arg(tempTableName)
            .arg(TIME_OFFSET_SECONDS);
            
        query = m_dbManager->executeQuery(finalSql);
        if (query.lastError().isValid()) {
            qDebug() << "查询临时表失败:" << query.lastError().text();
        } else {
            qDebug() << "成功从临时表查询数据，行数:" << query.size();
        }

        // 删除临时表
        QString dropTempSql = QString("DROP TEMPORARY TABLE IF EXISTS %1").arg(tempTableName);
        m_dbManager->executeQuery(dropTempSql);
    }
    
    return query;
}

bool TelemetryDataProvider::checkTableExists(const QString &tableName) const
{
    if (!m_dbManager || !m_dbManager->isConnected()) {
        qWarning() << "数据库未连接，无法检查表是否存在";
        return false;
    }
    
    QString checkSql = QString(
        "SELECT COUNT(*) FROM information_schema.tables "
        "WHERE table_schema = DATABASE() AND table_name = '%1'")
        .arg(tableName);
    
    QSqlQuery checkQuery = m_dbManager->executeQuery(checkSql);
    if (checkQuery.lastError().isValid()) {
        qWarning() << "检查表存在性时发生错误:" << checkQuery.lastError().text();
        return false;
    }
    
    if (checkQuery.next()) {
        int count = checkQuery.value(0).toInt();
        bool exists = (count > 0);
        qDebug() << "表" << tableName << (exists ? "存在" : "不存在");
        return exists;
    }
    
    return false;
} 