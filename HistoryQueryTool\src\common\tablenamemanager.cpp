#include "tablenamemanager.h"

// 定义表名前缀常量
const QString TableNameManager::MINUTE_TABLE_PREFIX = "htdaystore_min_";
const QString TableNameManager::SECOND_TABLE_PREFIX = "htdaystore_sec_";

QString TableNameManager::getMinuteTelemetryTable(const QDate &date)
{
    return MINUTE_TABLE_PREFIX + date.toString("yyyyMMdd");
}

QString TableNameManager::getSecondTelemetryTable(const QDate &date)
{
    return SECOND_TABLE_PREFIX + date.toString("yyyyMMdd");
}

QString TableNameManager::getSOEEventTable()
{
    return "htsoelog";
}

QString TableNameManager::getYXChangeTable()
{
    return "htyxchangelog";
}

QString TableNameManager::getLimitAlarmTable()
{
    return "htlimitalarmlog";
}

QString TableNameManager::getSystemEventTable()
{
    return "htsystemeventlog";
}

QString TableNameManager::getOperationRecordTable()
{
    return "htoperationlog";
}

QString TableNameManager::getUserLoginTable()
{
    return "htuserloginlog";
} 