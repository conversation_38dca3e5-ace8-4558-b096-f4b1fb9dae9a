TABLE_NAME	COLUMN_NAME	COLUMN_TYPE	IS_NULLABLE	COLUMN_KEY	COLUMN_DEFAULT	COLUMN_COMMENT
htbreaklimitlog	id	int(11)	NO	PRI	NULL	
htbreaklimitlog	hostname	varchar(50)	YES		NULL	
htbreaklimitlog	equipid	int(11)	YES		NULL	
htbreaklimitlog	ycid	int(11)	YES		NULL	
htbreaklimitlog	breaklimittype	tinyint(4)	YES		NULL	
htbreaklimitlog	yctname	varchar(50)	YES		NULL	
htbreaklimitlog	starttime	datetime	YES		NULL	
htbreaklimitlog	endtime	datetime	YES		NULL	
htbreaklimitlog	limitvalue	decimal(12,4)	YES		NULL	
htbreaklimitlog	breakvalue	decimal(12,4)	YES		NULL	
htbreaklimitlog	recoverd	tinyint(4)	YES		0	
htbreaklimitlog	flagack	tinyint(4)	YES		0	
htbreaklimitlog	ackperson	varchar(80)	YES		NULL	
htcoslog	id	int(11)	NO	PRI	NULL	
htcoslog	equipid	int(11)	YES		NULL	
htcoslog	yxid	int(11)	YES		NULL	
htcoslog	yxname	varchar(200)	YES		NULL	
htcoslog	oldvalue	smallint(6)	YES		NULL	
htcoslog	newvalue	smallint(6)	YES		NULL	
htcoslog	starttime	datetime	YES		NULL	
htcoslog	msec	smallint(6)	NO		0	
htcoslog	flagack	tinyint(4)	NO		0	
htcoslog	ackperson	varchar(80)	YES		NULL	
htcoslog	hostname	varchar(50)	YES		NULL	
htdaystore_min_20250421	id	int(11)	NO	PRI	NULL	
htdaystore_min_20250421	equipid	int(11)	YES		NULL	
htdaystore_min_20250421	linkid	int(11)	YES		NULL	
htdaystore_min_20250421	insertDate	int(11)	YES		NULL	
htdaystore_min_20250421	curvevalue	blob	YES		NULL	
htdaystore_sec_20250421	id	int(11)	NO	PRI	NULL	
htdaystore_sec_20250421	equipid	int(11)	YES		NULL	
htdaystore_sec_20250421	linkid	int(11)	YES		NULL	
htdaystore_sec_20250421	insertDate	int(11)	YES		NULL	
htdaystore_sec_20250421	curvevalue	blob	YES		NULL	
htoperationlog	id	int(11)	NO	PRI	NULL	
htoperationlog	equipid	int(11)	NO		NULL	
htoperationlog	equipname	varchar(300)	NO		NULL	
htoperationlog	ctrlsigid	int(11)	NO		NULL	
htoperationlog	ctrlsigname	varchar(300)	NO		NULL	
htoperationlog	ctrltype	tinyint(4)	NO		NULL	
htoperationlog	opertype	tinyint(4)	NO		NULL	
htoperationlog	opervalue	decimal(20,4)	NO		NULL	
htoperationlog	operresult	tinyint(4)	NO		NULL	
htoperationlog	starttime	datetime	NO		NULL	
htoperationlog	operatorname	varchar(300)	NO		NULL	
htoperationlog	supervisorname	varchar(300)	NO		NULL	
htoperationlog	hostname	varchar(50)	NO		NULL	
htsoelog	id	int(11)	NO	PRI	NULL	
htsoelog	hostname	varchar(50)	YES		NULL	
htsoelog	equipid	int(11)	YES		NULL	
htsoelog	yxid	int(11)	YES		NULL	
htsoelog	yxname	varchar(200)	YES		NULL	
htsoelog	valuetype	tinyint(4)	YES		NULL	
htsoelog	starttime	datetime	YES		NULL	
htsoelog	startms	smallint(6)	YES		NULL	
htsoelog	flagack	tinyint(4)	YES		0	
htsoelog	timein	datetime	YES		NULL	
htsoelog	ackperson	varchar(80)	YES		NULL	
htsoelog	oldvalue	smallint(6)	YES		NULL	
htsoelog	newvalue	smallint(6)	YES		NULL	
htuserlog	id	int(11)	NO	PRI	NULL	
htuserlog	username	varchar(50)	NO		NULL	
htuserlog	starttime	datetime	NO		NULL	
htuserlog	logtype	smallint(6)	NO		NULL	
htuserlog	hostname	varchar(50)	NO		NULL	
htuserlog	appname	varchar(50)	NO		NULL	
syserrorinfo	id	int(11)	NO	PRI	NULL	
syserrorinfo	hostname	varchar(50)	YES		NULL	
syserrorinfo	sourcename	varchar(50)	YES		NULL	
syserrorinfo	errorinfo	varchar(2048)	YES		NULL	
syserrorinfo	starttime	datetime	YES		NULL	
syserrorinfo	flagack	tinyint(4)	YES		0	
syserrorinfo	errobjid	int(11)	YES		NULL	
syserrorinfo	errstate	smallint(6)	YES		NULL	
syserrorinfo	errtype	tinyint(4)	YES		NULL	
syserrorinfo	ackperson	varchar(80)	YES		NULL	
