#!/bin/bash

# SFTP目录测试脚本
# 用于验证远程目录是否存在和可访问

# 配置信息（请根据实际情况修改）
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_username"        # 请替换为实际用户名
REMOTE_PASS="your_password"        # 请替换为实际密码

# 测试目录列表
TEST_DIRS=(
    "/home/<USER>/zhuanJiKong-Ewenjian/"
    "/home/<USER>/zhuanJiKong-Ewenjian2/"
    "/home/<USER>/zhuanJiKong-Ewenjian3/"
)

# 日志函数
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message"
}

# 测试SFTP连接
test_connection() {
    log "INFO" "测试SFTP连接到 $REMOTE_HOST:$REMOTE_PORT..."
    
    local test_cmd="echo 'pwd' | sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=30 '$REMOTE_USER@$REMOTE_HOST'"
    
    if eval "$test_cmd" >/dev/null 2>&1; then
        log "INFO" "连接测试成功"
        return 0
    else
        log "ERROR" "连接测试失败"
        return 1
    fi
}

# 列出用户主目录
list_home_directory() {
    log "INFO" "列出用户主目录内容..."
    
    local temp_file="/tmp/sftp_home_test.txt"
    local batch_file="/tmp/sftp_batch_home.txt"
    
    cat > "$batch_file" << EOF
pwd
ls -la
quit
EOF
    
    local sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=30 -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
    
    if eval "$sftp_cmd" > "$temp_file" 2>&1; then
        log "INFO" "用户主目录内容："
        cat "$temp_file"
    else
        log "ERROR" "无法列出主目录内容"
        cat "$temp_file"
    fi
    
    rm -f "$batch_file" "$temp_file"
}

# 测试特定目录
test_directory() {
    local test_dir="$1"
    log "INFO" "测试目录: $test_dir"
    
    local temp_file="/tmp/sftp_dir_test_$(date +%s).txt"
    local batch_file="/tmp/sftp_batch_$(date +%s).txt"
    
    cat > "$batch_file" << EOF
cd $test_dir
pwd
ls -la
quit
EOF
    
    local sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=30 -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
    
    if eval "$sftp_cmd" > "$temp_file" 2>&1; then
        if grep -q "Couldn't" "$temp_file" || grep -q "No such file" "$temp_file"; then
            log "ERROR" "目录不存在或无权访问: $test_dir"
            cat "$temp_file"
        else
            log "INFO" "目录访问成功: $test_dir"
            log "INFO" "目录内容："
            cat "$temp_file"
        fi
    else
        log "ERROR" "无法访问目录: $test_dir"
        cat "$temp_file"
    fi
    
    rm -f "$batch_file" "$temp_file"
    echo "----------------------------------------"
}

# 查找可能的目录
find_possible_directories() {
    log "INFO" "查找可能的目录结构..."
    
    local temp_file="/tmp/sftp_find_test.txt"
    local batch_file="/tmp/sftp_batch_find.txt"
    
    cat > "$batch_file" << EOF
pwd
ls -la
cd /home
pwd
ls -la
quit
EOF
    
    local sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=30 -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
    
    if eval "$sftp_cmd" > "$temp_file" 2>&1; then
        log "INFO" "目录结构探索结果："
        cat "$temp_file"
    else
        log "ERROR" "无法探索目录结构"
        cat "$temp_file"
    fi
    
    rm -f "$batch_file" "$temp_file"
}

# 主函数
main() {
    echo "=========================================="
    echo "SFTP目录测试脚本"
    echo "=========================================="
    
    # 检查依赖
    if ! command -v sshpass >/dev/null 2>&1; then
        log "ERROR" "缺少sshpass命令，请安装："
        log "INFO" "  sudo apt-get install sshpass  # Ubuntu/Debian"
        log "INFO" "  sudo yum install sshpass      # CentOS/RHEL"
        exit 1
    fi
    
    # 测试连接
    if ! test_connection; then
        log "ERROR" "连接失败，请检查服务器配置"
        exit 1
    fi
    
    echo ""
    
    # 列出主目录
    list_home_directory
    
    echo ""
    echo "=========================================="
    
    # 查找目录结构
    find_possible_directories
    
    echo ""
    echo "=========================================="
    
    # 测试每个目录
    for dir in "${TEST_DIRS[@]}"; do
        test_directory "$dir"
    done
    
    echo "测试完成！"
    echo ""
    echo "如果目录不存在，请："
    echo "1. 检查目录路径是否正确"
    echo "2. 确认用户是否有访问权限"
    echo "3. 联系服务器管理员确认目录结构"
}

# 执行主函数
main "$@"
