#include "filtermanager.h"
#include "../../models/station/stationmodel.h"
#include "../../database/dbmanager.h"
#include "../../utils/typemappingmanager.h"
#include "../../utils/devicenamemapper.h"
#include "../../common/logger.h"
#include <QDebug>
#include <QSqlQuery>
#include <QSqlError>
#include "../providers/historydataprovider.h"

//---------- FilterManager 实现 ----------//

FilterManager::FilterManager(QObject *parent)
    : QObject(parent),
      m_parentWidget(nullptr),
      m_filterWidget(nullptr),
      m_mainLayout(nullptr),
      m_dbManager(nullptr),
      m_stationModel(nullptr),
      m_dataProvider(nullptr),
      m_currentType(SOE_EVENT),
      m_tableName(""),
      // 时间控件
      m_timeLayout(nullptr),
      m_startTimeLabel(nullptr),
      m_startDateTimeEdit(nullptr),
      m_endTimeLabel(nullptr),
      m_endDateTimeEdit(nullptr),
      // 第一行控件
      m_firstRowLayout(nullptr),
      m_stationLabel(nullptr),
      m_stationComboBox(nullptr),
      m_typeLabel(nullptr),
      m_typeComboBox(nullptr),
      // 第二行控件
      m_secondRowLayout(nullptr),
      m_deviceLabel(nullptr),
      m_deviceComboBox(nullptr),
      m_signalLabel(nullptr),
      m_signalEdit(nullptr),
      // 第三行控件
      m_thirdRowLayout(nullptr),
      m_intervalLabel(nullptr),
      m_intervalComboBox(nullptr),
      // 其他
      m_separatorLine(nullptr),
      m_initialized(false)
{
}

FilterManager::~FilterManager()
{
    // Qt的父子关系会自动处理控件的删除
}

void FilterManager::setParentWidget(QWidget *parent)
{
    m_parentWidget = parent;
    createFilterUI();
}

void FilterManager::setDBManager(DBManager *dbManager)
{
    m_dbManager = dbManager;
}

void FilterManager::setStationModel(StationModel *stationModel)
{
    m_stationModel = stationModel;
}

void FilterManager::setDataProvider(HistoryDataProvider *provider)
{
    m_dataProvider = provider;
}

void FilterManager::setFilterType(HistoryDataType type)
{
    Logger::debug(QString("设置过滤器类型: %1").arg(static_cast<int>(type)));
    
    m_currentType = type;
    
    // 根据类型设置表名 - 使用正确的数据库表名
    switch (type) {
        case SOE_EVENT:
            m_tableName = "htsoelog";        // 正确的SOE事件表名
            break;
        case ANALOG_EVENT: 
            m_tableName = "htanalog";        // 模拟量事件表名（如果存在）
            break;
        case LIMIT_ALARM:
            m_tableName = "htbreaklimitlog"; // 正确的越限告警表名
            break;
        case YX_CHANGE:
            m_tableName = "htcoslog";        // 正确的遥信变位表名
            break;
        case USER_LOGIN:
            m_tableName = "htuserlog";       // 正确的用户登录表名
            break;
        case SYSTEM_EVENT:
            m_tableName = "syserrorinfo";    // 正确的系统事件表名
            break;
        case OPERATION_RECORD:
            m_tableName = "htoperationlog";  // 正确的操作记录表名
            break;
        case MINUTE_TELEMETRY:
        case SECOND_TELEMETRY:
            m_tableName = "telemetry";       // 遥测表名会动态生成
            break;
    }
    
    Logger::debug(QString("表名设置为: %1").arg(m_tableName));
    
    updateControlsVisibility();
    
    // 确保过滤器可见
    setVisible(true);
    
    if (m_dbManager && m_dbManager->isConnected()) {
        loadControlData();
    }
}

QWidget* FilterManager::getFilterWidget()
{
    return m_filterWidget;
}

void FilterManager::setVisible(bool visible)
{
    if (m_filterWidget) {
        m_filterWidget->setVisible(visible);
    }
}

QMap<QString, QString> FilterManager::getFilters()
{
    QMap<QString, QString> filters;
    
    // 获取类型映射管理器
    TypeMappingManager *typeMappingManager = TypeMappingManager::instance();
    
    // 根据当前类型返回相应的过滤条件
    switch (m_currentType) {
        case SOE_EVENT:
        case ANALOG_EVENT: {
            // 标准过滤器 - 使用动态字段名
            QString stationName = m_stationComboBox->currentText();
            if (stationName != "所有" && m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    filters[stationField.toUpper()] = stationName;
                }
            }
            
            QString eventType = m_typeComboBox->currentText();
            if (eventType != "所有") {
                // 将显示名称转换为数据库值
                QString eventValue = typeMappingManager->getValueByName("VALUETYPE", eventType);
                filters["VALUETYPE"] = eventValue;
            }
            
            if (m_deviceComboBox) {
                QVariant deviceData = m_deviceComboBox->currentData();
                if (deviceData.isValid()) {
                    QString deviceField = m_stationModel ? m_stationModel->getDeviceFieldForTable(m_tableName) : "equipid";
                    filters[deviceField.toUpper()] = deviceData.toString();
                }
            }
            
            QString signalFilter = m_signalEdit->text().trimmed();
            if (!signalFilter.isEmpty()) {
                filters["SIGNALNAME"] = signalFilter;
            }
            break;
        }
        
        case LIMIT_ALARM: {
            // 越限告警过滤器 - 使用动态字段名
            QString stationName = m_stationComboBox->currentText();
            if (stationName != "所有" && m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    filters[stationField.toUpper()] = stationName;
                }
            }
            
            QString limitType = m_typeComboBox->currentText();
            if (limitType != "所有") {
                // 将显示名称转换为数据库值
                QString limitValue = typeMappingManager->getValueByName("BREAKLIMITTYPE", limitType);
                filters["BREAKLIMITTYPE"] = limitValue;
            }
            
            if (m_deviceComboBox) {
                QVariant deviceData = m_deviceComboBox->currentData();
                if (deviceData.isValid()) {
                    QString deviceField = m_stationModel ? m_stationModel->getDeviceFieldForTable(m_tableName) : "equipid";
                    filters[deviceField.toUpper()] = deviceData.toString();
                }
            }
            
            QString signalFilter = m_signalEdit->text().trimmed();
            if (!signalFilter.isEmpty()) {
                filters["SIGNALNAME"] = signalFilter;
            }
            break;
        }
        
        case OPERATION_RECORD: {
            // 操作记录过滤器 - 使用动态字段名
            QString stationName = m_stationComboBox->currentText();
            if (stationName != "所有" && m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    filters[stationField.toUpper()] = stationName;
                }
            }
            
            QString operType = m_typeComboBox->currentText();
            if (operType != "所有") {
                // 将显示名称转换为数据库值
                QString operValue = typeMappingManager->getValueByName("OPERTYPE", operType);
                filters["OPERTYPE"] = operValue;
            }
            
            if (m_deviceComboBox) {
                QVariant deviceData = m_deviceComboBox->currentData();
                if (deviceData.isValid()) {
                    QString deviceField = m_stationModel ? m_stationModel->getDeviceFieldForTable(m_tableName) : "equipid";
                    filters[deviceField.toUpper()] = deviceData.toString();
                }
            }
            
            QString signalFilter = m_signalEdit->text().trimmed();
            if (!signalFilter.isEmpty()) {
                filters["SIGNALNAME"] = signalFilter;
            }
            break;
        }
        
        case YX_CHANGE: {
            // 遥信变位过滤器 - 使用动态字段名
            QString stationName = m_stationComboBox->currentText();
            if (stationName != "所有" && m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    filters[stationField.toUpper()] = stationName;
                }
            }
            
            // 添加设备过滤支持
            if (m_deviceComboBox) {
                QVariant deviceData = m_deviceComboBox->currentData();
                if (deviceData.isValid()) {
                    QString deviceField = m_stationModel ? m_stationModel->getDeviceFieldForTable(m_tableName) : "equipid";
                    filters[deviceField.toUpper()] = deviceData.toString();
                }
            }
            
            QString signalFilter = m_signalEdit->text().trimmed();
            if (!signalFilter.isEmpty()) {
                filters["SIGNALNAME"] = signalFilter;
            }
            break;
        }
        
        case USER_LOGIN: {
            QString username = m_stationComboBox->currentText();
            if (username != "所有") {
                filters["USERNAME"] = username;
            }
            
            QString logType = m_typeComboBox->currentText();
            if (logType != "所有") {
                QString logTypeValue = typeMappingManager->getValueByName("LOGTYPE", logType);
                filters["LOGTYPE"] = logTypeValue;
            }
            break;
        }
        
        case SYSTEM_EVENT: {
            QString stationName = m_stationComboBox->currentText();
            if (stationName != "所有" && m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    filters[stationField.toUpper()] = stationName;
                }
            }
            
            QString errorType = m_typeComboBox->currentText();
            if (errorType != "所有") {
                // 先尝试ERRTYPE映射，如果没有则使用SOURCENAME
                QString errorValue = typeMappingManager->getValueByName("ERRTYPE", errorType);
                if (errorValue != errorType) {
                    // 映射成功，使用ERRTYPE字段
                    filters["ERRTYPE"] = errorValue;
                } else {
                    // 映射失败，使用SOURCENAME字段
                    filters["SOURCENAME"] = errorType;
                }
            }
            break;
        }
        
        case MINUTE_TELEMETRY:
        case SECOND_TELEMETRY: {
            filters["start_time"] = m_startDateTimeEdit->dateTime().toString("yyyy-MM-dd HH:mm:ss");
            filters["end_time"] = m_endDateTimeEdit->dateTime().toString("yyyy-MM-dd HH:mm:ss");
            filters["memory_filter_interval"] = QString::number(getInterval());
            filters["is_minute_interval"] = (m_currentType == MINUTE_TELEMETRY) ? "true" : "false";
            break;
        }
    }
    
    return filters;
}

QDateTime FilterManager::getStartTime() const
{
    if (m_startDateTimeEdit) {
        return m_startDateTimeEdit->dateTime();
    }
    return QDateTime(QDateTime::currentDateTime().date(), QTime(0, 0, 0));
}

QDateTime FilterManager::getEndTime() const
{
    if (m_endDateTimeEdit) {
        return m_endDateTimeEdit->dateTime();
    }
    return QDateTime::currentDateTime();
}

void FilterManager::setTimeRange(const QDateTime &startTime, const QDateTime &endTime)
{
    if (m_startDateTimeEdit && m_endDateTimeEdit) {
        m_startDateTimeEdit->setDateTime(startTime);
        m_endDateTimeEdit->setDateTime(endTime);
    }
}

int FilterManager::getInterval() const
{
    if (!m_intervalComboBox) {
        return 0;
    }
    
    QString intervalText = m_intervalComboBox->currentText();
    if (intervalText.contains("原始")) {
        return 0; // 原始数据
    }
    
    // 提取数字部分
    QRegExp rx("(\\d+)");
    if (rx.indexIn(intervalText) != -1) {
        int value = rx.cap(1).toInt();
        if (intervalText.contains("分钟")) {
            return value * 60;
        } else if (intervalText.contains("小时")) {
            return value * 3600;
        } else if (intervalText.contains("秒")) {
            return value;
        }
    }
    
    return 0;
}

void FilterManager::initializeFilters()
{
    Logger::debug("开始初始化过滤器数据");
    
    if (!m_dbManager || !m_dbManager->isConnected()) {
        Logger::warning("数据库未连接，无法初始化过滤器");
        return;
    }
    
    if (m_tableName.isEmpty()) {
        Logger::warning("表名为空，无法初始化过滤器");
        return;
    }
    
    m_initialized = true;
    loadControlData();
    
    Logger::debug("过滤器数据初始化完成");
}

void FilterManager::createFilterUI()
{
    if (!m_parentWidget) {
        return;
    }
    
    // 创建主过滤器容器
    m_filterWidget = new QWidget(m_parentWidget);
    m_mainLayout = new QVBoxLayout(m_filterWidget);
    m_mainLayout->setContentsMargins(STANDARD_MARGIN, STANDARD_MARGIN, STANDARD_MARGIN, STANDARD_MARGIN);
    m_mainLayout->setSpacing(STANDARD_SPACING);
    
    // 创建各行控件
    createTimeRow();
    createFirstFilterRow();
    createSecondFilterRow();
    createThirdFilterRow();
    
    // 添加分隔线
    m_separatorLine = new QFrame(m_filterWidget);
    m_separatorLine->setFrameStyle(QFrame::HLine | QFrame::Sunken);
    m_separatorLine->setVisible(false); // 默认隐藏
    m_mainLayout->addWidget(m_separatorLine);
    
    // 应用统一样式
    applyStandardStyles();
    
    // 默认显示过滤器控件
    setVisible(true);
}

void FilterManager::createTimeRow()
{
    // 创建时间行容器
    QWidget *timeWidget = new QWidget(m_filterWidget);
    m_timeLayout = new QHBoxLayout(timeWidget);
    m_timeLayout->setContentsMargins(0, 0, 0, 0);
    m_timeLayout->setSpacing(STANDARD_SPACING);
    
    // 创建时间控件
    m_startTimeLabel = createStandardLabel("开始时间:", timeWidget);
    m_startDateTimeEdit = createStandardDateTimeEdit(timeWidget);
    m_endTimeLabel = createStandardLabel("结束时间:", timeWidget);
    m_endDateTimeEdit = createStandardDateTimeEdit(timeWidget);
    
    // 设置默认时间
    QDateTime currentTime = QDateTime::currentDateTime();
    QDateTime startTime = QDateTime(currentTime.date(), QTime(0, 0, 0));
    m_startDateTimeEdit->setDateTime(startTime);
    m_endDateTimeEdit->setDateTime(currentTime);
    
    // 添加到布局
    m_timeLayout->addWidget(m_startTimeLabel);
    m_timeLayout->addWidget(m_startDateTimeEdit, 1);
    m_timeLayout->addSpacing(STANDARD_SPACING);
    m_timeLayout->addWidget(m_endTimeLabel);
    m_timeLayout->addWidget(m_endDateTimeEdit, 1);
    m_timeLayout->addStretch();
    
    m_mainLayout->addWidget(timeWidget);
}

void FilterManager::createFirstFilterRow()
{
    // 创建第一行容器
    QWidget *firstRowWidget = new QWidget(m_filterWidget);
    m_firstRowLayout = new QHBoxLayout(firstRowWidget);
    m_firstRowLayout->setContentsMargins(0, 0, 0, 0);
    m_firstRowLayout->setSpacing(STANDARD_SPACING);
    
    // 创建控件
    m_stationLabel = createStandardLabel("主机名:", firstRowWidget);
    m_stationComboBox = createStandardComboBox(firstRowWidget);
    m_typeLabel = createStandardLabel("事件类型:", firstRowWidget);
    m_typeComboBox = createStandardComboBox(firstRowWidget);
    
    // 添加默认选项
    m_stationComboBox->addItem("所有");
    m_typeComboBox->addItem("所有");
    
    // 连接信号
    connect(m_stationComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &FilterManager::onStationChanged);
    connect(m_typeComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &FilterManager::onBayChanged);
    
    // 添加到布局
    m_firstRowLayout->addWidget(m_stationLabel);
    m_firstRowLayout->addWidget(m_stationComboBox, 1);
    m_firstRowLayout->addSpacing(STANDARD_SPACING);
    m_firstRowLayout->addWidget(m_typeLabel);
    m_firstRowLayout->addWidget(m_typeComboBox, 1);
    m_firstRowLayout->addStretch();
    
    m_mainLayout->addWidget(firstRowWidget);
}

void FilterManager::createSecondFilterRow()
{
    // 创建第二行容器
    QWidget *secondRowWidget = new QWidget(m_filterWidget);
    m_secondRowLayout = new QHBoxLayout(secondRowWidget);
    m_secondRowLayout->setContentsMargins(0, 0, 0, 0);
    m_secondRowLayout->setSpacing(STANDARD_SPACING);
    
    // 创建控件
    m_deviceLabel = createStandardLabel("设备:", secondRowWidget);
    m_deviceComboBox = createStandardComboBox(secondRowWidget);
    m_signalLabel = createStandardLabel("信号过滤:", secondRowWidget);
    m_signalEdit = createStandardLineEdit(secondRowWidget);
    
    // 添加默认选项
    m_deviceComboBox->addItem("所有设备");
    
    // 添加到布局
    m_secondRowLayout->addWidget(m_deviceLabel);
    m_secondRowLayout->addWidget(m_deviceComboBox, 1);
    m_secondRowLayout->addSpacing(STANDARD_SPACING);
    m_secondRowLayout->addWidget(m_signalLabel);
    m_secondRowLayout->addWidget(m_signalEdit, 2); // 信号过滤框占更多空间
    
    m_mainLayout->addWidget(secondRowWidget);
}

void FilterManager::createThirdFilterRow()
{
    // 创建第三行容器（遥测专用）
    QWidget *thirdRowWidget = new QWidget(m_filterWidget);
    m_thirdRowLayout = new QHBoxLayout(thirdRowWidget);
    m_thirdRowLayout->setContentsMargins(0, 0, 0, 0);
    m_thirdRowLayout->setSpacing(STANDARD_SPACING);
    
    // 创建控件
    m_intervalLabel = createStandardLabel("时间间隔:", thirdRowWidget);
    m_intervalComboBox = createStandardComboBox(thirdRowWidget);
    
    // 添加间隔选项
    m_intervalComboBox->addItem("原始数据");
    m_intervalComboBox->addItem("1分钟");
    m_intervalComboBox->addItem("5分钟");
    m_intervalComboBox->addItem("15分钟");
    m_intervalComboBox->addItem("30分钟");
    m_intervalComboBox->addItem("1小时");
    m_intervalComboBox->addItem("2小时");
    m_intervalComboBox->addItem("6小时");
    
    // 添加到布局
    m_thirdRowLayout->addWidget(m_intervalLabel);
    m_thirdRowLayout->addWidget(m_intervalComboBox, 1);
    m_thirdRowLayout->addStretch(3); // 添加更多空白
    
    m_mainLayout->addWidget(thirdRowWidget);
    
    // 默认隐藏遥测控件
    thirdRowWidget->setVisible(false);
}

QLabel* FilterManager::createStandardLabel(const QString &text, QWidget *parent)
{
    QLabel *label = new QLabel(text, parent);
    label->setMinimumWidth(LABEL_MIN_WIDTH);
    label->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    return label;
}

QComboBox* FilterManager::createStandardComboBox(QWidget *parent)
{
    QComboBox *comboBox = new QComboBox(parent);
    comboBox->setMinimumHeight(STANDARD_CONTROL_HEIGHT);
    comboBox->setMinimumWidth(CONTROL_MIN_WIDTH);
    comboBox->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    return comboBox;
}

QLineEdit* FilterManager::createStandardLineEdit(QWidget *parent)
{
    QLineEdit *lineEdit = new QLineEdit(parent);
    lineEdit->setMinimumHeight(STANDARD_CONTROL_HEIGHT);
    lineEdit->setMinimumWidth(CONTROL_MIN_WIDTH);
    lineEdit->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    return lineEdit;
}

QDateTimeEdit* FilterManager::createStandardDateTimeEdit(QWidget *parent)
{
    QDateTimeEdit *dateTimeEdit = new QDateTimeEdit(parent);
    dateTimeEdit->setMinimumHeight(STANDARD_CONTROL_HEIGHT);
    dateTimeEdit->setMinimumWidth(CONTROL_MIN_WIDTH + 20); // 稍微宽一点
    dateTimeEdit->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Fixed);
    dateTimeEdit->setCalendarPopup(true);
    dateTimeEdit->setDisplayFormat("yyyy-MM-dd HH:mm:ss");
    return dateTimeEdit;
}

void FilterManager::applyStandardStyles()
{
    if (!m_filterWidget) {
        return;
    }
    
    // 设置整体样式
    m_filterWidget->setStyleSheet(QString(
        "QWidget { "
        "  background-color: #f8f9fa; "
        "  border-radius: 4px; "
        "} "
        "QLabel { "
        "  color: #333; "
        "  font-weight: 500; "
        "  padding-right: 4px; "
        "} "
        "QComboBox, QLineEdit, QDateTimeEdit { "
        "  background-color: white; "
        "  border: 1px solid #ddd; "
        "  border-radius: 3px; "
        "  padding: 4px 8px; "
        "  font-size: 12px; "
        "  color: #333; "
        "} "
        "QComboBox:hover, QLineEdit:hover, QDateTimeEdit:hover { "
        "  border-color: #007acc; "
        "} "
        "QComboBox:focus, QLineEdit:focus, QDateTimeEdit:focus { "
        "  border-color: #007acc; "
        "  outline: none; "
        "} "
        "QComboBox::drop-down { "
        "  subcontrol-origin: padding; "
        "  subcontrol-position: top right; "
        "  width: 16px; "
        "  border-left-width: 1px; "
        "  border-left-color: #ddd; "
        "  border-left-style: solid; "
        "  border-top-right-radius: 3px; "
        "  border-bottom-right-radius: 3px; "
        "} "
        "QComboBox::down-arrow { "
        "  image: none; "
        "  border-left: 4px solid transparent; "
        "  border-right: 4px solid transparent; "
        "  border-top: 6px solid #666; "
        "  width: 0; "
        "  height: 0; "
        "} "
        "QComboBox QAbstractItemView { "
        "  background-color: white; "
        "  border: 1px solid #ddd; "
        "  selection-background-color: #007acc; "
        "  selection-color: white; "
        "  outline: 0; "
        "} "
        "QComboBox QAbstractItemView::item { "
        "  background-color: white; "
        "  color: #333; "
        "  padding: 4px 8px; "
        "  border: none; "
        "} "
        "QComboBox QAbstractItemView::item:selected { "
        "  background-color: #007acc; "
        "  color: white; "
        "} "
        "QComboBox QAbstractItemView::item:hover { "
        "  background-color: #e6f3ff; "
        "  color: #333; "
        "} "
        "QFrame[frameStyle=\"5\"] { "
        "  color: #ddd; "
        "  margin: 4px 0; "
        "}"
    ));
}

void FilterManager::updateControlsVisibility()
{
    if (!m_filterWidget) {
        return;
    }
    
    // 默认显示时间控件
    m_timeLayout->parentWidget()->setVisible(true);
    
    // 根据类型显示不同的控件组合
    switch (m_currentType) {
        case SOE_EVENT:
        case ANALOG_EVENT:
        case LIMIT_ALARM:
        case OPERATION_RECORD: {
            // 显示标准过滤控件（包括主机名、事件类型、设备、信号过滤）
            m_firstRowLayout->parentWidget()->setVisible(true);
            m_secondRowLayout->parentWidget()->setVisible(true);
            m_thirdRowLayout->parentWidget()->setVisible(false);
            
            // 确保所有控件可见并可点击
            m_stationLabel->setVisible(true);
            m_stationComboBox->setVisible(true);
            m_stationComboBox->setEnabled(true);
            m_typeLabel->setVisible(true);
            m_typeComboBox->setVisible(true);
            m_typeComboBox->setEnabled(true);
            m_deviceLabel->setVisible(true);
            m_deviceComboBox->setVisible(true);
            m_deviceComboBox->setEnabled(true);
            m_signalLabel->setVisible(true);
            m_signalEdit->setVisible(true);
            m_signalEdit->setEnabled(true);
            
            // 更新标签文本
            m_stationLabel->setText("主机名:");
            if (m_currentType == LIMIT_ALARM) {
                m_typeLabel->setText("越限类型:");
            } else if (m_currentType == OPERATION_RECORD) {
                m_typeLabel->setText("操作类型:");
            } else {
                m_typeLabel->setText("事件类型:");
            }
            m_deviceLabel->setText("设备:");
            m_signalLabel->setText("信号过滤:");
            break;
        }
        
        case YX_CHANGE: {
            // 遥信变位显示时间、主机名、设备和信号过滤（不显示事件类型）
            m_firstRowLayout->parentWidget()->setVisible(true);
            m_secondRowLayout->parentWidget()->setVisible(true);
            m_thirdRowLayout->parentWidget()->setVisible(false);
            
            // 显示主机名控件
            m_stationLabel->setVisible(true);
            m_stationComboBox->setVisible(true);
            m_stationComboBox->setEnabled(true);
            m_stationLabel->setText("主机名:");
            
            // 隐藏事件类型控件（遥信变位没有事件类型）
            m_typeLabel->setVisible(false);
            m_typeComboBox->setVisible(false);
            
            // 显示设备控件
            m_deviceLabel->setVisible(true);
            m_deviceComboBox->setVisible(true);
            m_deviceComboBox->setEnabled(true);
            m_deviceLabel->setText("设备:");
            
            // 显示信号过滤控件
            m_signalLabel->setVisible(true);
            m_signalEdit->setVisible(true);
            m_signalEdit->setEnabled(true);
            m_signalLabel->setText("信号过滤:");
            break;
        }
        
        case USER_LOGIN: {
            // 用户登录显示用户名和日志类型
            m_firstRowLayout->parentWidget()->setVisible(true);
            m_secondRowLayout->parentWidget()->setVisible(false);
            m_thirdRowLayout->parentWidget()->setVisible(false);
            
            // 显示用户名和日志类型
            m_stationLabel->setVisible(true);
            m_stationComboBox->setVisible(true);
            m_stationComboBox->setEnabled(true);
            m_typeLabel->setVisible(true);
            m_typeComboBox->setVisible(true);
            m_typeComboBox->setEnabled(true);
            
            // 重新设置标签文本
            m_stationLabel->setText("用户名:");
            m_typeLabel->setText("日志类型:");
            break;
        }
        
        case SYSTEM_EVENT: {
            // 系统事件显示主机名和错误来源
            m_firstRowLayout->parentWidget()->setVisible(true);
            m_secondRowLayout->parentWidget()->setVisible(false);
            m_thirdRowLayout->parentWidget()->setVisible(false);
            
            // 显示主机名和错误来源
            m_stationLabel->setVisible(true);
            m_stationComboBox->setVisible(true);
            m_stationComboBox->setEnabled(true);
            m_typeLabel->setVisible(true);
            m_typeComboBox->setVisible(true);
            m_typeComboBox->setEnabled(true);
            
            // 重新设置标签文本
            m_stationLabel->setText("主机名:");
            m_typeLabel->setText("错误来源:");
            break;
        }
        
        case MINUTE_TELEMETRY:
        case SECOND_TELEMETRY: {
            // 遥测数据只显示时间和间隔
            m_firstRowLayout->parentWidget()->setVisible(false);
            m_secondRowLayout->parentWidget()->setVisible(false);
            m_thirdRowLayout->parentWidget()->setVisible(true);
            
            // 显示间隔控件
            m_intervalLabel->setVisible(true);
            m_intervalComboBox->setVisible(true);
            m_intervalComboBox->setEnabled(true);
            break;
        }
    }
}

void FilterManager::loadControlData()
{
    if (!m_dataProvider || !m_dbManager || !m_dbManager->isConnected()) {
        Logger::warning("无法加载控件数据：数据提供者或数据库无效");
        return;
    }
    
    Logger::debug(QString("开始加载控件数据，表名: %1").arg(m_tableName));
    
    // 阻塞信号
    m_stationComboBox->blockSignals(true);
    m_typeComboBox->blockSignals(true);
    if (m_deviceComboBox) m_deviceComboBox->blockSignals(true);
    
    // 清空并重新加载数据
    m_stationComboBox->clear();
    m_typeComboBox->clear();
    if (m_deviceComboBox) m_deviceComboBox->clear();
    
    // 添加默认选项
    m_stationComboBox->addItem("所有");
    m_typeComboBox->addItem("所有");
    if (m_deviceComboBox) m_deviceComboBox->addItem("所有设备");
    
    // 获取类型映射管理器
    TypeMappingManager *typeMappingManager = TypeMappingManager::instance();
    
    // 根据类型加载不同的数据
    switch (m_currentType) {
        case SOE_EVENT:
        case ANALOG_EVENT: {
            // 使用StationModel加载主机名（确保使用正确的字段名）
            if (m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    QList<Station> stations = m_stationModel->getStationsFromTable(m_tableName, stationField);
                    for (const Station &station : stations) {
                        if (station.name != "所有") { // 避免重复添加"所有"选项
                            m_stationComboBox->addItem(station.name);
                        }
                    }
                }
            }
            
            // 加载事件类型 - 使用映射
            QString typeField = "VALUETYPE";
            QStringList mappedTypes = typeMappingManager->getAllNames(typeField);
            if (!mappedTypes.isEmpty()) {
                m_typeComboBox->addItems(mappedTypes);
            } else {
                // 如果映射为空，直接从数据库查询
                QStringList types = m_dataProvider->getDistinctValuesFromTable(m_tableName, typeField);
                m_typeComboBox->addItems(types);
            }
            break;
        }
        
        case LIMIT_ALARM: {
            // 使用StationModel加载主机名
            if (m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    QList<Station> stations = m_stationModel->getStationsFromTable(m_tableName, stationField);
                    for (const Station &station : stations) {
                        if (station.name != "所有") { // 避免重复添加"所有"选项
                            m_stationComboBox->addItem(station.name);
                        }
                    }
                }
            }
            
            // 加载越限类型 - 使用映射
            QString typeField = "BREAKLIMITTYPE";
            QStringList mappedTypes = typeMappingManager->getAllNames(typeField);
            if (!mappedTypes.isEmpty()) {
                m_typeComboBox->addItems(mappedTypes);
            } else {
                // 如果映射为空，直接从数据库查询
                QStringList types = m_dataProvider->getDistinctValuesFromTable(m_tableName, typeField);
                m_typeComboBox->addItems(types);
            }
            break;
        }
        
        case OPERATION_RECORD: {
            // 使用StationModel加载主机名
            if (m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    QList<Station> stations = m_stationModel->getStationsFromTable(m_tableName, stationField);
                    for (const Station &station : stations) {
                        if (station.name != "所有") { // 避免重复添加"所有"选项
                            m_stationComboBox->addItem(station.name);
                        }
                    }
                }
            }
            
            // 加载操作类型 - 使用映射
            QString typeField = "OPERTYPE";
            QStringList mappedTypes = typeMappingManager->getAllNames(typeField);
            if (!mappedTypes.isEmpty()) {
                m_typeComboBox->addItems(mappedTypes);
            } else {
                // 如果映射为空，直接从数据库查询
                QStringList types = m_dataProvider->getDistinctValuesFromTable(m_tableName, typeField);
                m_typeComboBox->addItems(types);
            }
            break;
        }
        
        case YX_CHANGE: {
            // 使用StationModel加载主机名
            if (m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    QList<Station> stations = m_stationModel->getStationsFromTable(m_tableName, stationField);
                    for (const Station &station : stations) {
                        if (station.name != "所有") { // 避免重复添加"所有"选项
                            m_stationComboBox->addItem(station.name);
                        }
                    }
                }
            }
            
            // 遥信变位没有类型字段
            break;
        }
        
        case USER_LOGIN: {
            // 加载用户名
            QStringList users = m_dataProvider->getDistinctValuesFromTable(m_tableName, "USERNAME");
            m_stationComboBox->addItems(users);
            
            // 加载日志类型（使用映射）
            QStringList logTypes = typeMappingManager->getAllNames("LOGTYPE");
            m_typeComboBox->addItems(logTypes);
            break;
        }
        
        case SYSTEM_EVENT: {
            // 使用StationModel加载主机名
            if (m_stationModel) {
                QString stationField = m_stationModel->getStationFieldForTable(m_tableName);
                if (!stationField.isEmpty()) {
                    QList<Station> stations = m_stationModel->getStationsFromTable(m_tableName, stationField);
                    for (const Station &station : stations) {
                        if (station.name != "所有") { // 避名重复添加"所有"选项
                            m_stationComboBox->addItem(station.name);
                        }
                    }
                }
            }
            
            // 加载错误类型 - 使用映射
            QString typeField = "ERRTYPE";
            QStringList mappedTypes = typeMappingManager->getAllNames(typeField);
            if (!mappedTypes.isEmpty()) {
                m_typeComboBox->addItems(mappedTypes);
            } else {
                // 如果映射为空，直接从数据库查询
                QStringList sources = m_dataProvider->getDistinctValuesFromTable(m_tableName, "SOURCENAME");
                m_typeComboBox->addItems(sources);
            }
            break;
        }
        
        case MINUTE_TELEMETRY:
        case SECOND_TELEMETRY:
            // 遥测数据不需要加载下拉框数据
            break;
    }
    
    // 恢复信号
    m_stationComboBox->blockSignals(false);
    m_typeComboBox->blockSignals(false);
    if (m_deviceComboBox) m_deviceComboBox->blockSignals(false);
    
    // 设备下拉框：从 para_device 表获取所有设备
    if (m_deviceComboBox) {
        m_deviceComboBox->clear();
        m_deviceComboBox->addItem("所有设备", QVariant());
        DeviceNameMapper *deviceMapper = DeviceNameMapper::instance();
        if (deviceMapper->initialize()) {
            QMap<int, QString> deviceMap = deviceMapper->getAllDeviceNames();
            for (auto it = deviceMap.constBegin(); it != deviceMap.constEnd(); ++it) {
                int deviceId = it.key();
                QString deviceName = it.value();
                QString displayName = QString("%1 [%2]").arg(deviceName).arg(deviceId);
                m_deviceComboBox->addItem(displayName, deviceId);
            }
        }
    }

    Logger::debug("控件数据加载完成");
}

void FilterManager::onStationChanged(int /*index*/)
{
    // 只有在设备下拉框存在且当前类型支持设备过滤的情况下才执行
    if (!m_deviceComboBox || m_currentType == USER_LOGIN || m_currentType == SYSTEM_EVENT ||
        m_currentType == MINUTE_TELEMETRY || m_currentType == SECOND_TELEMETRY) {
        return;
    }

    // 设备下拉框：从 para_device 表获取所有设备
    m_deviceComboBox->blockSignals(true);
    m_deviceComboBox->clear();
    m_deviceComboBox->addItem("所有设备", QVariant());
    DeviceNameMapper *deviceMapper = DeviceNameMapper::instance();
    if (deviceMapper->initialize()) {
        QMap<int, QString> deviceMap = deviceMapper->getAllDeviceNames();
        for (auto it = deviceMap.constBegin(); it != deviceMap.constEnd(); ++it) {
            int deviceId = it.key();
            QString deviceName = it.value();
            QString displayName = QString("%1 [%2]").arg(deviceName).arg(deviceId);
            m_deviceComboBox->addItem(displayName, deviceId);
        }
    }
    m_deviceComboBox->blockSignals(false);
}

void FilterManager::onBayChanged(int /*index*/)
{
    // 当事件类型变更时，重新加载设备列表
    onStationChanged(m_stationComboBox->currentIndex());
}

void FilterManager::setCurrentDevice(int deviceId)
{
    if (m_deviceComboBox) {
        int idx = m_deviceComboBox->findData(deviceId);
        if (idx >= 0) {
            m_deviceComboBox->setCurrentIndex(idx);
        }
    }
}

void FilterManager::setCurrentPoint(int deviceIndex)
{
    // 如果有点位下拉框或输入框，自动设置。此处预留实现。
    // 例如：if (m_pointComboBox) { int idx = m_pointComboBox->findData(deviceIndex); if (idx >= 0) m_pointComboBox->setCurrentIndex(idx); }
    // 或者：if (m_pointEdit) m_pointEdit->setText(QString::number(deviceIndex));
} 


