/****************************************************************************
** Meta object code from reading C++ file 'soehistorywidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "src/widgets/soehistorywidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'soehistorywidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_SOEHistoryWidget_t {
    QByteArrayData data[17];
    char stringdata0[231];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_SOEHistoryWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_SOEHistoryWidget_t qt_meta_stringdata_SOEHistoryWidget = {
    {
QT_MOC_LITERAL(0, 0, 16), // "SOEHistoryWidget"
QT_MOC_LITERAL(1, 17, 24), // "onTelemetryPointSelected"
QT_MOC_LITERAL(2, 42, 0), // ""
QT_MOC_LITERAL(3, 43, 8), // "deviceId"
QT_MOC_LITERAL(4, 52, 11), // "deviceIndex"
QT_MOC_LITERAL(5, 64, 4), // "name"
QT_MOC_LITERAL(6, 69, 20), // "onQueryButtonClicked"
QT_MOC_LITERAL(7, 90, 20), // "onPrintButtonClicked"
QT_MOC_LITERAL(8, 111, 21), // "onExportButtonClicked"
QT_MOC_LITERAL(9, 133, 15), // "onResultCounted"
QT_MOC_LITERAL(10, 149, 5), // "count"
QT_MOC_LITERAL(11, 155, 21), // "onMessageNotification"
QT_MOC_LITERAL(12, 177, 5), // "title"
QT_MOC_LITERAL(13, 183, 7), // "message"
QT_MOC_LITERAL(14, 191, 17), // "onTreeItemClicked"
QT_MOC_LITERAL(15, 209, 16), // "QTreeWidgetItem*"
QT_MOC_LITERAL(16, 226, 4) // "item"

    },
    "SOEHistoryWidget\0onTelemetryPointSelected\0"
    "\0deviceId\0deviceIndex\0name\0"
    "onQueryButtonClicked\0onPrintButtonClicked\0"
    "onExportButtonClicked\0onResultCounted\0"
    "count\0onMessageNotification\0title\0"
    "message\0onTreeItemClicked\0QTreeWidgetItem*\0"
    "item"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_SOEHistoryWidget[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    3,   49,    2, 0x0a /* Public */,
       6,    0,   56,    2, 0x08 /* Private */,
       7,    0,   57,    2, 0x08 /* Private */,
       8,    0,   58,    2, 0x08 /* Private */,
       9,    1,   59,    2, 0x08 /* Private */,
      11,    2,   62,    2, 0x08 /* Private */,
      14,    1,   67,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::Int, QMetaType::QString,    3,    4,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   10,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   12,   13,
    QMetaType::Void, 0x80000000 | 15,   16,

       0        // eod
};

void SOEHistoryWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        SOEHistoryWidget *_t = static_cast<SOEHistoryWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->onTelemetryPointSelected((*reinterpret_cast< int(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 1: _t->onQueryButtonClicked(); break;
        case 2: _t->onPrintButtonClicked(); break;
        case 3: _t->onExportButtonClicked(); break;
        case 4: _t->onResultCounted((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 5: _t->onMessageNotification((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 6: _t->onTreeItemClicked((*reinterpret_cast< QTreeWidgetItem*(*)>(_a[1]))); break;
        default: ;
        }
    }
}

const QMetaObject SOEHistoryWidget::staticMetaObject = {
    { &QueryWidget::staticMetaObject, qt_meta_stringdata_SOEHistoryWidget.data,
      qt_meta_data_SOEHistoryWidget,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *SOEHistoryWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *SOEHistoryWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_SOEHistoryWidget.stringdata0))
        return static_cast<void*>(this);
    return QueryWidget::qt_metacast(_clname);
}

int SOEHistoryWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QueryWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 7;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
