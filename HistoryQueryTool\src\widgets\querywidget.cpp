#include "querywidget.h"
#include "ui_querywidget.h"
#include "../database/dbmanager.h"
#include "../utils/devicenamemapper.h"
#include <QMessageBox>
#include <QAbstractItemModel>
#include <QDateTime>
#include <QDebug>
#include <QSqlQuery>
#include <QSqlError>
#include <QMap>
#include <QFile>
#include <QTextStream>

#if defined(QT_PRINTSUPPORT_LIB)
#include <QPainter>
#include <QPrinter>
#include <QPrintDialog>
#endif

// 静态常量定义
const QString QueryWidget::HOSTNAME_FIELD = "HOSTNAME";
const QString QueryWidget::DEVICE_FIELD = "EQUIPNAME";

QueryWidget::QueryWidget(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::QueryWidget),
    queryModel(new CustomQueryModel(this)),
    autoQueryEnabled(false)
{
    ui->setupUi(this);
    
    // 设置默认时间范围（当天0:00到当前时间）
    ui->startDateEdit->setDate(QDate::currentDate());
    ui->startTimeEdit->setTime(QTime(0, 0, 0));
    ui->endDateEdit->setDate(QDate::currentDate());
    ui->endTimeEdit->setTime(QTime::currentTime());
    
    // 确保设备ID映射功能启用
    queryModel->setDeviceIdMappingEnabled(true);
    
    // 连接信号和槽
    connect(ui->queryButton, &QPushButton::clicked, this, &QueryWidget::executeQuery);
}

QueryWidget::~QueryWidget()
{
    delete ui;
}

void QueryWidget::executeQuery()
{
    // 获取查询参数
    startTime = QDateTime(ui->startDateEdit->date(), ui->startTimeEdit->time());
    endTime = QDateTime(ui->endDateEdit->date(), ui->endTimeEdit->time());
    signalFilter = ui->signalFilterEdit->text();
    
    // 确保设备名称映射器已初始化
    DeviceNameMapper *deviceMapper = DeviceNameMapper::instance();
    if (!deviceMapper->isInitialized()) {
        if (!deviceMapper->initialize()) {
            qWarning() << "设备名称映射器初始化失败: " << deviceMapper->lastError();
            // 即使初始化失败，也继续查询，只是没有设备名称映射功能
        }
    }
    
    // 检查数据库管理器是否存在
    if (!dbManager) {
        dbManager = DBManager::instance(); // 尝试获取全局实例
        
        if (!dbManager) {
            QMessageBox::warning(this, "查询错误", "数据库管理器未初始化");
            return;
        } else {
            qDebug() << "QueryWidget: 自动获取数据库管理器实例";
        }
    }
    
    // 检查数据库连接状态
    if (!dbManager->isConnected()) {
        QMessageBox::warning(this, "查询错误", "数据库未连接，请先连接数据库");
        return;
    }
    
    try {
        // 确保查询模型存在
        if (!queryModel) {
            queryModel = new CustomQueryModel(this);
        }
        
        // 强制启用设备ID映射功能
        queryModel->setDeviceIdMappingEnabled(true);
        
        // 构建查询SQL
        QString sql = buildQuerySQL();
        
        // 修改SQL查询，只选择表头定义的字段
        sql = modifySelectClause(sql);
        
        // 检查查询的表，如果是htbreaklimitlog、htcoslog、htoperationlog或htsoelog，强制映射equipid字段
        if (sql.contains("htbreaklimitlog") || 
            sql.contains("htcoslog") || 
            sql.contains("htoperationlog") || 
            sql.contains("htsoelog")) {
            // 这些表含有可映射的设备ID
        }
        
        qDebug() << "执行查询SQL:" << sql;
        
        // 使用executeQuery方法而不是database方法
        QSqlQuery query = dbManager->executeQuery(sql);
        
        if (query.lastError().isValid()) {
            QMessageBox::warning(this, "查询错误", 
                               "执行查询时发生错误：\n" + query.lastError().text());
            qWarning() << "查询错误:" << query.lastError().text();
            return;
        }
                
        // 清除旧数据
        queryModel->clear();
        
        // 设置新查询
        queryModel->setQuery(query);
        
        if (queryModel->lastError().isValid()) {
            QMessageBox::warning(this, "查询错误", 
                               "执行查询时发生错误：\n" + queryModel->lastError().text());
            qWarning() << "设置查询模型错误:" << queryModel->lastError().text();
            return;
        }
        
        // 检查是否有结果
        int rowCount = queryModel->rowCount();
        if (rowCount == 0) {
            // 如果没有结果，清空模型
            queryModel->clear();
            
            // 不设置表头，显示空表格
            ui->resultTableView->setModel(nullptr);
            
            // 更新行数信息
            ui->pageInfoLabel->setText("查询结果：0 条记录");
            
            // 显示提示信息
            QMessageBox::information(this, "查询结果", "没有找到符合条件的记录");
            
            qDebug() << "查询完成，没有找到符合条件的记录";
            return;
        }
        
        // 有结果，设置表头
        setupTableHeaders();
        
        // 强制刷新表视图，确保映射结果显示
        ui->resultTableView->reset();
        
        // 确保表格视图使用正确的模型
        ui->resultTableView->setModel(queryModel);
        
        // 设置表格属性
        ui->resultTableView->setAlternatingRowColors(true);
        ui->resultTableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        ui->resultTableView->setSortingEnabled(true);
        ui->resultTableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
        
        // 设置表格列宽可以调整
        ui->resultTableView->horizontalHeader()->setSectionResizeMode(QHeaderView::Interactive);
        
        // 调整表格列宽
        ui->resultTableView->resizeColumnsToContents();
        
        // 为时间列设置更宽的宽度，确保显示完整的日期时间（包括秒）
        for (int i = 0; i < queryModel->columnCount(); ++i) {
            QString headerText = queryModel->headerData(i, Qt::Horizontal).toString();
            if (headerText.contains("时间", Qt::CaseInsensitive) || 
                headerText.toUpper().contains("TIME") || 
                headerText.toUpper().contains("DATE")) {
                int currentWidth = ui->resultTableView->columnWidth(i);
                ui->resultTableView->setColumnWidth(i, currentWidth * 1.5);
            }
        }
        
        // 更新行数信息
        ui->pageInfoLabel->setText(QString("查询结果：%1 条记录").arg(rowCount));
        
        qDebug() << "查询完成，共找到" << rowCount << "条记录";
    }
    catch (std::exception &e) {
        QMessageBox::critical(this, "查询错误", 
                             QString("查询发生异常：%1").arg(e.what()));
        qCritical() << "查询异常:" << e.what();
    }
}

// 确保查询模型有效
bool QueryWidget::ensureModelValid()
{
    if (!queryModel) {
        qDebug() << "创建新的查询模型";
        queryModel = new CustomQueryModel(this);
        return true;
    }
    return true;
}

// 安全地清除模型
void QueryWidget::safeModelClear()
{
    if (!queryModel) {
        return;
    }
    
    try {
        // 使用更安全的方式清除模型
        queryModel->setQuery(QSqlQuery());
    } catch (const std::exception &e) {
        qCritical() << "清除查询模型时异常:" << e.what();
    } catch (...) {
        qCritical() << "清除查询模型时发生未知异常";
    }
}

void QueryWidget::refreshData()
{
    // 检查数据库管理器是否存在
    if (!dbManager) {
        dbManager = DBManager::instance(); // 尝试获取全局实例
        
        if (!dbManager) {
            QMessageBox::warning(this, "查询错误", "数据库管理器未初始化");
            return;
        } else {
            qDebug() << "QueryWidget: 自动获取数据库管理器实例";
        }
    }
    
    // 检查数据库连接状态
    if (!dbManager->isConnected()) {
        QMessageBox::warning(this, "查询错误", "数据库未连接，请先连接数据库");
        return;
    }
    
    // 安全地清除当前查询结果
    clear();
    
    // 确保查询模型存在
    ensureModelValid();
    
    // 重新设置表格视图的模型
    ui->resultTableView->setModel(queryModel);
    
    // 设置表格属性
    ui->resultTableView->setAlternatingRowColors(true);
    ui->resultTableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->resultTableView->setSortingEnabled(true);
    ui->resultTableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    
    // 执行查询
    executeQuery();
}

void QueryWidget::clear()
{
    // 安全地清除查询模型中的数据
    safeModelClear();
    
    // 重置表格视图
    ui->resultTableView->reset();
    
    // 清除信号过滤器
    ui->signalFilterEdit->clear();
    signalFilter = "";
}

#if defined(QT_PRINTSUPPORT_LIB)
void QueryWidget::printResults(QPrinter *printer)
{
    if (!queryModel || queryModel->rowCount() == 0) {
        QMessageBox::warning(this, "打印错误", "没有可打印的数据！");
        return;
    }
    
    QPainter painter;
    if (!painter.begin(printer)) {
        QMessageBox::warning(this, "打印错误", "无法启动打印机！");
        return;
    }
    
    // 设置字体
    QFont font = painter.font();
    font.setPointSize(10);
    painter.setFont(font);
    
    // 计算页面大小和边距
    QRect pageRect(0, 0, painter.device()->width(), painter.device()->height());
    int margin = 20;
    QRect contentRect = pageRect.adjusted(margin, margin, -margin, -margin);
    
    // 绘制标题
    QFont titleFont = font;
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    painter.setFont(titleFont);
    
    QRect titleRect = contentRect;
    titleRect.setHeight(30);
    painter.drawText(titleRect, Qt::AlignCenter, ui->titleLabel->text());
    
    // 绘制查询条件
    painter.setFont(font);
    QRect conditionRect = contentRect.adjusted(0, 40, 0, 0);
    conditionRect.setHeight(20);
    painter.drawText(conditionRect, Qt::AlignLeft, 
                   QString("查询时间范围：%1 至 %2")
                   .arg(startTime.toString("yyyy-MM-dd HH:mm:ss"))
                   .arg(endTime.toString("yyyy-MM-dd HH:mm:ss")));
    
    // 绘制表格
    QRect tableRect = contentRect.adjusted(0, 70, 0, 0);
    
    // 计算列宽
    int columnCount = queryModel->columnCount();
    QVector<int> columnWidths(columnCount);
    int totalWidth = tableRect.width();
    
    for (int col = 0; col < columnCount; ++col) {
        columnWidths[col] = totalWidth / columnCount;
    }
    
    // 绘制表头
    QRect headerRect = tableRect;
    headerRect.setHeight(30);
    
    for (int col = 0; col < columnCount; ++col) {
        QRect cellRect = headerRect;
        cellRect.setLeft(headerRect.left() + col * columnWidths[col]);
        cellRect.setWidth(columnWidths[col]);
        
        // 绘制表头文本
        QString headerText = queryModel->headerData(col, Qt::Horizontal).toString();
        painter.drawText(cellRect, Qt::AlignCenter, headerText);
        
        // 绘制表头边框
        painter.drawRect(cellRect);
    }
    
    // 绘制数据行
    int rowCount = queryModel->rowCount();
    int rowHeight = 25;
    int rowsPerPage = (contentRect.height() - 100) / rowHeight;
    int currentPage = 1;
    
    for (int row = 0; row < rowCount; ++row) {
        // 检查是否需要换页
        if (row > 0 && row % rowsPerPage == 0) {
            printer->newPage();
            currentPage++;
            
            // 重新绘制表头
            for (int col = 0; col < columnCount; ++col) {
                QRect cellRect = headerRect;
                cellRect.setLeft(headerRect.left() + col * columnWidths[col]);
                cellRect.setWidth(columnWidths[col]);
                
                // 绘制表头文本
                QString headerText = queryModel->headerData(col, Qt::Horizontal).toString();
                painter.drawText(cellRect, Qt::AlignCenter, headerText);
                
                // 绘制表头边框
                painter.drawRect(cellRect);
            }
        }
        
        // 计算行的位置
        int rowPosition = (row % rowsPerPage) * rowHeight + 100;
        
        // 绘制数据单元格
        for (int col = 0; col < columnCount; ++col) {
            QRect cellRect = tableRect;
            cellRect.setLeft(tableRect.left() + col * columnWidths[col]);
            cellRect.setTop(tableRect.top() + rowPosition);
            cellRect.setWidth(columnWidths[col]);
            cellRect.setHeight(rowHeight);
            
            // 绘制单元格文本
            QString cellText = queryModel->data(queryModel->index(row, col)).toString();
            painter.drawText(cellRect, Qt::AlignCenter, cellText);
            
            // 绘制单元格边框
            painter.drawRect(cellRect);
        }
    }
    
    // 绘制页码
    QRect pageNumberRect = contentRect;
    pageNumberRect.setTop(contentRect.bottom() - 20);
    pageNumberRect.setHeight(20);
    painter.drawText(pageNumberRect, Qt::AlignRight, 
                   QString("第 %1 页，共 %2 页")
                   .arg(currentPage)
                   .arg((rowCount + rowsPerPage - 1) / rowsPerPage));
    
    painter.end();
}
#endif

CustomQueryModel* QueryWidget::model() const
{
    return queryModel;
}

void QueryWidget::setDBManager(DBManager *dbManager)
{
    if (!dbManager) {
        qWarning() << "警告: 尝试设置空的数据库管理器";
        return;
    }
    
    this->dbManager = dbManager;
    qDebug() << "QueryWidget: 数据库管理器已设置";
    
    // 初始化表结构检查
    if (dbManager->isConnected()) {
        checkTableStructureAndLockUI();
    }
}

void QueryWidget::checkTableStructureAndLockUI()
{
    if (!dbManager || !dbManager->isConnected()) {
        qWarning() << "数据库未连接，无法检查表结构";
        return;
    }

    // 需要检查的表列表
    QStringList tables;
    tables << "htsoelog"       // 保护事件历史数据
           << "htcoslog"       // 遥信变位历史数据
           << "htbreaklimitlog" // 越限告警历史数据
           << "syserrorinfo"   // 系统事件历史数据
           << "htoperationlog" // 操作记录历史数据
           << "htuserlog";     // 用户登录历史数据

    tableFieldInfoMap.clear();

    for (const QString &tableName : tables) {
        QString sql = QString("SHOW COLUMNS FROM %1").arg(tableName);
        QSqlQuery query = dbManager->executeQuery(sql);
        
        if (query.lastError().isValid()) {
            qWarning() << "查询表" << tableName << "结构失败:" << query.lastError().text();
            continue;
        }

        TableFieldInfo tableInfo;
        tableInfo.hasHostnameField = false;
        tableInfo.hasDeviceField = false;

        while (query.next()) {
            QString fieldName = query.value(0).toString().toUpper();
            tableInfo.fields << fieldName;

            if (fieldName == HOSTNAME_FIELD) {
                tableInfo.hasHostnameField = true;
            } else if (fieldName == DEVICE_FIELD) {
                tableInfo.hasDeviceField = true;
            }
        }

        tableFieldInfoMap[tableName] = tableInfo;

        qDebug() << "表" << tableName << "结构检查完成:";
        qDebug() << "  主机名字段:" << (tableInfo.hasHostnameField ? "存在" : "不存在");
        qDebug() << "  设备字段:" << (tableInfo.hasDeviceField ? "存在" : "不存在");
    }

    // 应用初始表的锁定
    if (currentTable.isEmpty() && !tableFieldInfoMap.isEmpty()) {
        // 如果当前表为空，使用第一个表作为默认表
        currentTable = tableFieldInfoMap.keys().first();
    }
    
    if (!currentTable.isEmpty()) {
        applyTableStructureLock(currentTable);
    }
}

bool QueryWidget::tableHasField(const QString &tableName, const QString &fieldName)
{
    if (!tableFieldInfoMap.contains(tableName)) {
        return false;
    }

    return tableFieldInfoMap[tableName].fields.contains(fieldName, Qt::CaseInsensitive);
}

void QueryWidget::applyTableStructureLock(const QString &tableName)
{
    if (!tableFieldInfoMap.contains(tableName)) {
        // 默认全部锁定
        ui->stationComboBox->setEnabled(false);
        ui->deviceComboBox->setEnabled(false);
        return;
    }

    const TableFieldInfo &info = tableFieldInfoMap[tableName];

    // 根据表结构锁定或解锁控件
    ui->stationComboBox->setEnabled(info.hasHostnameField);
    ui->deviceComboBox->setEnabled(info.hasDeviceField);

    // 如果控件被锁定，则清空并添加"所有"选项
    if (!ui->stationComboBox->isEnabled()) {
        ui->stationComboBox->clear();
        if (ui->stationComboBox->findText("所有") == -1) {
            ui->stationComboBox->addItem("所有");
        }
    }

    if (!ui->deviceComboBox->isEnabled()) {
        ui->deviceComboBox->clear();
        if (ui->deviceComboBox->findText("所有设备") == -1) {
            ui->deviceComboBox->addItem("所有");
        }
    }

    // 更新UI状态提示
    QString lockStatus;
    if (!info.hasHostnameField) lockStatus += "主机名 ";
    if (!info.hasDeviceField) lockStatus += "设备 ";

    if (!lockStatus.isEmpty()) {
        qDebug() << "表" << tableName << "锁定了以下选项:" << lockStatus;
    }
}

void QueryWidget::onTableTypeChanged(int index)
{
    // 默认实现，子类可以重写
    Q_UNUSED(index);
}

void QueryWidget::initUI()
{
    // 默认实现，子类可以重写此方法
}

void QueryWidget::initConnections()
{
    // 默认实现，子类可以重写此方法
}

void QueryWidget::initComboBoxes()
{
    // 默认实现，子类可以重写此方法
}

void QueryWidget::initFilters()
{
    // 默认实现，子类可以重写此方法
}

#if defined(QT_PRINTSUPPORT_LIB)
void QueryWidget::printResults()
{
    QPrinter printer;
    printer.setPageOrientation(QPageLayout::Landscape);
    
    QPrintDialog dialog(&printer, this);
    if (dialog.exec() == QDialog::Accepted) {
        printResults(&printer);
    }
}
#endif

// 强制刷新查询结果
void QueryWidget::onForceRefresh()
{
    refreshData();
}

// 构建查询SQL
QString QueryWidget::buildQuerySQL() const
{
    // 默认实现，子类应该重写此方法
    return QString("SELECT * FROM htsoelog LIMIT 10");
}

// 设置表头
void QueryWidget::setupTableHeaders()
{
    // 默认实现，子类应该重写此方法
}

// 修改SQL查询以只选择特定列
QString QueryWidget::modifySelectClause(const QString &sql) const
{
    // 默认实现，子类可以重写此方法
    return sql;
}

void QueryWidget::setAutoQueryEnabled(bool enabled)
{
    autoQueryEnabled = enabled;
    qDebug() << "自动查询设置为:" << (enabled ? "启用" : "禁用");
}

bool QueryWidget::exportToCSV(const QString &fileName)
{
    qDebug() << "导出CSV到:" << fileName;
    
    if (!queryModel || queryModel->rowCount() == 0) {
        QMessageBox::warning(this, "导出错误", "没有可导出的数据！");
        return false;
    }
    
    QFile file(fileName);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "导出错误", "无法打开文件进行写入！");
        return false;
    }
    
    QTextStream out(&file);
    
    // 写入表头
    QStringList headers;
    for (int i = 0; i < queryModel->columnCount(); ++i) {
        headers << queryModel->headerData(i, Qt::Horizontal).toString();
    }
    out << headers.join(",") << "\n";
    
    // 写入数据
    for (int row = 0; row < queryModel->rowCount(); ++row) {
        QStringList rowData;
        for (int col = 0; col < queryModel->columnCount(); ++col) {
            rowData << queryModel->data(queryModel->index(row, col)).toString();
        }
        out << rowData.join(",") << "\n";
    }
    
    file.close();
    QMessageBox::information(this, "导出成功", "数据已成功导出到CSV文件！");
    return true;
}

bool QueryWidget::exportToExcel(const QString &fileName)
{
    qDebug() << "导出Excel到:" << fileName;
    
    // 由于没有Excel导出库，使用CSV替代
    QMessageBox::information(this, "导出信息", "当前版本不支持直接导出Excel格式，将导出CSV格式代替。");
    return exportToCSV(fileName);
}

void QueryWidget::saveTableFilterSettings()
{
    qDebug() << "保存表格过滤设置";
    // 默认实现为空，应由子类实现具体功能
}

void QueryWidget::loadTableFilterSettings()
{
    qDebug() << "加载表格过滤设置";
    // 默认实现为空，应由子类实现具体功能
} 