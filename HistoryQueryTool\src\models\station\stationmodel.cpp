#include "stationmodel.h"
#include "../../database/dbmanager.h"
#include "../../utils/typemappingmanager.h"
#include <QSet>
#include <QDebug>
#include <QSqlError>
#include <QSqlRecord>
#include <QSet>

// 缓存有效期(秒)
const int CACHE_EXPIRATION_SECONDS = 300; // 5分钟

// 定义不同数据类型对应的历史数据表
const QString TABLE_SOE_EVENT = "htsoelog";         // 保护事件
const QString TABLE_YX_CHANGE = "htcoslog";         // 遥信变位
const QString TABLE_LIMIT_ALARM = "htbreaklimitlog"; // 越限告警
const QString TABLE_SYSTEM_EVENT = "syserrorinfo";  // 系统事件
const QString TABLE_OPERATION = "htoperationlog";   // 操作记录
const QString TABLE_USER_LOGIN = "htuserlog";       // 用户登录

StationModel* StationModel::m_instance = nullptr;

StationModel* StationModel::instance()
{
    if (!m_instance) {
        m_instance = new StationModel();
    }
    return m_instance;
}

StationModel::StationModel(QObject *parent) : QObject(parent), cacheExpirationSeconds(CACHE_EXPIRATION_SECONDS)
{
    // 初始化缓存更新时间
    lastCacheUpdateTime = QDateTime::currentDateTime();
    // 初始化预加载表记录
    preloadedTables.clear();
}

StationModel::~StationModel()
{
}


// 清除所有缓存
void StationModel::clearCache()
{
    stationsCache.clear();
    baysCache.clear();
    devicesCache.clear();
    tableFieldsCache.clear();
    preloadedTables.clear();
    lastCacheUpdateTime = QDateTime::currentDateTime();
}

// 检查表是否有指定字段
bool StationModel::tableHasField(const QString &tableName, const QString &fieldName)
{
    // 先检查表名和字段名是否为空
    if (tableName.isEmpty() || fieldName.isEmpty()) {
        return false;
    }
    
    // 先检查缓存
    if (tableFieldsCache.contains(tableName)) {
        return tableFieldsCache[tableName].contains(fieldName, Qt::CaseInsensitive);
    }
    
    DBManager *dbManager = DBManager::instance();
    if (!dbManager->isConnected()) {
        return false;
    }
    
    // 查询表结构
    QString queryStr = QString("DESCRIBE %1").arg(tableName);
    QSqlQuery query = dbManager->executeQuery(queryStr);
    
    if (query.lastError().isValid()) {
        qDebug() << "获取表结构失败:" << query.lastError().text();
        return false;
    }
    
    // 获取所有字段
    QStringList fields;
    while (query.next()) {
        fields << query.value(0).toString();
    }
    
    // 缓存表字段
    tableFieldsCache[tableName] = fields;
    
    // 判断是否包含字段(不区分大小写)
    return fields.contains(fieldName, Qt::CaseInsensitive);
}

// 从指定表获取主机列表
QList<Station> StationModel::getStationsFromTable(const QString &tableName, const QString &stationField)
{
    // 先检查缓存是否有效
    if (stationsCache.contains(tableName) && 
        lastCacheUpdateTime.secsTo(QDateTime::currentDateTime()) < cacheExpirationSeconds) {
        return stationsCache[tableName];
    }
    
    QList<Station> stations;
    
    // 检查参数
    if (tableName.isEmpty() || stationField.isEmpty()) {
        qWarning() << "表名或字段名为空，无法获取主机列表";
        
        // 添加"所有"选项
        Station allStation;
        allStation.id = -1; 
        allStation.name = "所有";
        stations.append(allStation);
        
        return stations;
    }
    
    // 检查表是否存在该字段
    if (!tableHasField(tableName, stationField)) {
        qWarning() << "表" << tableName << "不存在字段" << stationField;
        
        // 添加"所有"选项
        Station allStation;
        allStation.id = -1;
        allStation.name = "所有";
        stations.append(allStation);
        
        return stations;
    }
    
    QSet<QString> uniqueStations; // 用于去重
    
    // 临时存储查询结果
    QList<Station> queryResults;
    int sqlRowCount = 0;
    
    // 构建查询SQL - 从指定表中获取唯一的hostname值
    QString queryStr = QString(
        "SELECT DISTINCT %1 FROM %2 "
        "WHERE %1 IS NOT NULL "
        "ORDER BY LENGTH(%1), %1").arg(stationField.toLower(), tableName.toLower());
    
    // 执行查询
    DBManager *dbManager = DBManager::instance();
    if (dbManager->isConnected()) {
        QSqlQuery query = dbManager->executeQuery(queryStr);
        
        if (query.lastError().isValid()) {
            qWarning() << "获取主机信息失败:" << query.lastError().text();
        } else {
            // 处理查询结果
            int id = 0; // 从0开始为从数据库加载的项目编号
            
            while (query.next()) {
                sqlRowCount++;
                QString name = query.value(0).toString().trimmed();
                
                // 检查是否为空字符串
                if (name.isEmpty()) {
                    continue;
                }
                
                // 去重
                if (!uniqueStations.contains(name)) {
                    Station station;
                    station.id = id++;
                    station.name = name;
                    queryResults.append(station);
                    uniqueStations.insert(name);
                }
            }
        }
    } else {
        qWarning() << "数据库未连接，无法获取主机信息";
    }
    
    // 添加"所有"选项到最前面
    Station allStation;
    allStation.id = -1;
    allStation.name = "所有";
    stations.append(allStation);
    
    // 然后添加其他所有站点
    stations.append(queryResults);

    // 更新缓存
    stationsCache[tableName] = stations;
    lastCacheUpdateTime = QDateTime::currentDateTime();
    
    return stations;
}

// 从指定表获取指定主机的事件类型列表
QList<Bay> StationModel::getBaysFromTable(const QString &tableName, const QString &stationName)
{
    // 获取字段名
    QString stationField = getStationFieldForTable(tableName);
    QString bayField = getBayFieldForTable(tableName);
    
    // 先检查缓存是否有效
    if (baysCache.contains(tableName) && 
        baysCache[tableName].contains(stationName) &&
        lastCacheUpdateTime.secsTo(QDateTime::currentDateTime()) < cacheExpirationSeconds) {
        return baysCache[tableName][stationName];
    }
    
    QList<Bay> bays;
    
    // 检查参数
    if (tableName.isEmpty() || bayField.isEmpty()) {
        qWarning() << "表名或字段名为空，无法获取事件类型列表";
        
        // 添加默认的"所有"选项
        Bay allBay;
        allBay.id = -1;
        allBay.name = "所有";
        bays.append(allBay);
        
        return bays;
    }
    
    QSet<QString> uniqueBays; // 用于去重
    
    // 确保不会添加重复的"所有"选项
    uniqueBays.insert("所有");
    
    // 临时存储查询结果
    QList<Bay> queryResults;
    int sqlRowCount = 0;
    
    // 构建查询SQL - 从指定表中获取唯一的类型名称
    QString queryStr;
    if (stationName == "所有" || stationName.isEmpty() || stationField.isEmpty() || !tableHasField(tableName, stationField)) {
        // 不按主机筛选
        // 如果是数值类型字段，尝试按数值排序
        if (bayField.compare("VALUETYPE", Qt::CaseInsensitive) == 0 ||
            bayField.compare("ERRTYPE", Qt::CaseInsensitive) == 0 ||
            bayField.compare("OPERTYPE", Qt::CaseInsensitive) == 0) {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE %1 IS NOT NULL "
                "ORDER BY CAST(%1 AS SIGNED)").arg(bayField.toLower(), tableName.toLower());
        } else {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE %1 IS NOT NULL "
                "ORDER BY LENGTH(%1), %1").arg(bayField.toLower(), tableName.toLower());
        }
    } else {
        // 按主机筛选
        // 如果是数值类型字段，尝试按数值排序
        if (bayField.compare("VALUETYPE", Qt::CaseInsensitive) == 0 ||
            bayField.compare("ERRTYPE", Qt::CaseInsensitive) == 0 ||
            bayField.compare("OPERTYPE", Qt::CaseInsensitive) == 0) {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE LOWER(%3) = LOWER('%4') AND %1 IS NOT NULL "
                "ORDER BY CAST(%1 AS SIGNED)").arg(bayField.toLower(), tableName.toLower(), 
                                                stationField.toLower(), stationName);
        } else {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE LOWER(%3) = LOWER('%4') AND %1 IS NOT NULL "
                "ORDER BY LENGTH(%1), %1").arg(bayField.toLower(), tableName.toLower(), 
                                            stationField.toLower(), stationName);
        }
    }
    
    // 执行查询
    DBManager *dbManager = DBManager::instance();
    if (dbManager->isConnected()) {
        QSqlQuery query = dbManager->executeQuery(queryStr);
        
        if (query.lastError().isValid()) {
            qDebug() << "获取事件类型信息失败:" << query.lastError().text();
        } else {
            // 处理查询结果，先存储所有类型
            int id = 0; // 修改为从0开始
            
            while (query.next()) {
                sqlRowCount++;
                QString name = query.value(0).toString().trimmed();
                
                // 检查是否为空值
                if (name.isEmpty()) {
                    continue;
                }
                
                // 去重，但保留数值字符串（如"0"、"1"）
                if (!uniqueBays.contains(name)) {
                    Bay bay;
                    bay.id = id++;
                    bay.name = name;
                    queryResults.append(bay);
                    uniqueBays.insert(name);
                }
            }
        }
    } else {
        qWarning() << "数据库未连接，无法获取事件类型信息";
    }
    
    // 添加"所有"选项到最前面
    Bay allBay;
    allBay.id = -1;
    allBay.name = "所有";
    bays.append(allBay);
    
    // 然后添加查询结果
    bays.append(queryResults);
    
    qDebug() << "查询完成，事件类型总数(含'所有'):" << bays.size();

    // 更新缓存
    if (!baysCache.contains(tableName)) {
        baysCache[tableName] = QMap<QString, QList<Bay>>();
    }
    baysCache[tableName][stationName] = bays;
    lastCacheUpdateTime = QDateTime::currentDateTime();
    
    return bays;
}

// 从指定表获取指定主机和事件类型的设备列表
QList<Device> StationModel::getDevicesFromTable(const QString &tableName, const QString &stationField,
     const QString &bayField, const QString &deviceField,
     const QString &stationName, const QString &bayName)
{
    // 先检查缓存是否有效
    if (devicesCache.contains(tableName) && 
        devicesCache[tableName].contains(stationName) &&
        devicesCache[tableName][stationName].contains(bayName) &&
        lastCacheUpdateTime.secsTo(QDateTime::currentDateTime()) < cacheExpirationSeconds) {
        return devicesCache[tableName][stationName][bayName];
    }
    
    QList<Device> devices;
    
    // 检查参数
    if (tableName.isEmpty() || deviceField.isEmpty()) {
        qWarning() << "表名或设备字段名为空，无法获取设备列表";
        return devices;
    }
    
    // 检查表是否存在设备字段
    if (!tableHasField(tableName, deviceField)) {
        qWarning() << "表" << tableName << "不存在字段" << deviceField;
        return devices;
    }
    
    QSet<QString> uniqueDevices; // 用于去重
    
    // 确保"所有设备"选项不会重复添加
    uniqueDevices.insert("所有设备");
    
    // 临时存储查询结果
    QList<Device> queryResults;
    
    // 构建查询SQL
    QString queryStr;
    QStringList conditions;
    
    // 只有当主机名不是"所有"且字段存在时才添加主机名条件
    if (stationName != "所有" && !stationName.isEmpty() && !stationField.isEmpty() && tableHasField(tableName, stationField)) {
        conditions << QString("LOWER(%1) = LOWER('%2')").arg(stationField.toLower(), stationName);
    }
    
    // 只有当事件类型不是"所有"且字段存在时才添加事件类型条件
    if (bayName != "所有" && !bayName.isEmpty() && !bayField.isEmpty() && tableHasField(tableName, bayField)) {
        // 使用TypeMappingManager处理所有类型映射
        TypeMappingManager* typeMappingManager = TypeMappingManager::instance();
        
        if (typeMappingManager->isMappedTypeField(bayField)) {
            // 获取类型名称对应的值进行查询
            QString typeValue = typeMappingManager->getValueByName(bayField, bayName);
            conditions << QString("%1 = %2").arg(bayField.toLower(), typeValue);
        } else {
            // 非映射字段使用直接比较
            conditions << QString("LOWER(%1) = LOWER('%2')").arg(bayField.toLower(), bayName);
        }
    }
    
    // 添加设备字段不为空的条件
    QString deviceNotNullCondition = QString("%1 IS NOT NULL").arg(deviceField.toLower());
    
    // 构建基本查询
    queryStr = QString("SELECT DISTINCT %1 FROM %2").arg(deviceField.toLower(), tableName.toLower());
    
    // 添加条件
    if (!conditions.isEmpty()) {
        queryStr += " WHERE " + conditions.join(" AND ") + " AND " + deviceNotNullCondition;
    } else {
        queryStr += " WHERE " + deviceNotNullCondition;
    }
    
    // 添加排序
    queryStr += QString(" ORDER BY %1").arg(deviceField.toLower());
    
    qDebug() << "设备查询SQL:" << queryStr;
    
    // 执行查询
    DBManager *dbManager = DBManager::instance();
    if (dbManager->isConnected()) {
        QSqlQuery query = dbManager->executeQuery(queryStr);
        
        if (query.lastError().isValid()) {
            qWarning() << "获取设备信息失败:" << query.lastError().text() << "SQL:" << queryStr;
        } else {
            // 处理结果
            int id = 1; // 自动生成ID
            while (query.next()) {
                QString name = query.value(0).toString().trimmed();
                
                // 去重
                if (!uniqueDevices.contains(name)) {
                    Device device;
                    device.id = id++;
                    device.name = name;
                    queryResults.append(device);
                    uniqueDevices.insert(name);
                }
            }
        }
    } else {
        qWarning() << "数据库未连接，无法获取设备信息";
    }
    
    // 添加默认项
    Device allDevice;
    allDevice.id = 0;
    allDevice.name = "所有设备";
    devices.append(allDevice);
    
    // 然后添加查询结果
    devices.append(queryResults);
    
    // 更新缓存
    if (!devicesCache.contains(tableName)) {
        devicesCache[tableName] = QMap<QString, QMap<QString, QList<Device>>>();
    }
    if (!devicesCache[tableName].contains(stationName)) {
        devicesCache[tableName][stationName] = QMap<QString, QList<Device>>();
    }
    devicesCache[tableName][stationName][bayName] = devices;
    lastCacheUpdateTime = QDateTime::currentDateTime();
    
    qDebug() << "获取设备列表完成，设备总数:" << devices.size();
    
    return devices;
}

// 获取历史数据类型对应的表名
QString StationModel::getHistoryTypeTableName(int historyType)
{
    switch (historyType) {
        case 0: // SOE_EVENT
            return TABLE_SOE_EVENT;
        case 1: // YX_CHANGE
            return TABLE_YX_CHANGE;
        case 2: // LIMIT_ALARM
            return TABLE_LIMIT_ALARM;
        case 3: // SYSTEM_EVENT
            return TABLE_SYSTEM_EVENT;
        case 4: // OPERATION_RECORD
            return TABLE_OPERATION;
        case 5: // USER_LOGIN
            return TABLE_USER_LOGIN;
        default:
            return TABLE_SOE_EVENT;
    }
}

// 获取表对应的主机字段名
QString StationModel::getStationFieldForTable(const QString &tableName)
{
    // 根据表名返回对应的主机字段名
    if (tableName.compare(TABLE_SOE_EVENT, Qt::CaseInsensitive) == 0) {
        return "hostname";
    } else if (tableName.compare(TABLE_YX_CHANGE, Qt::CaseInsensitive) == 0) {
        return "hostname";
    } else if (tableName.compare(TABLE_LIMIT_ALARM, Qt::CaseInsensitive) == 0) {
        return "hostname";
    } else if (tableName.compare(TABLE_OPERATION, Qt::CaseInsensitive) == 0) {
        return "hostname";
    } else if (tableName.compare(TABLE_SYSTEM_EVENT, Qt::CaseInsensitive) == 0) {
        return "hostname";
    } else if (tableName.compare(TABLE_USER_LOGIN, Qt::CaseInsensitive) == 0) {
        return "hostname";
    }
    
    // 检查表是否实际存在该字段
    if (tableHasField(tableName, "HOSTNAME")) {
        return "HOSTNAME";
    } else if (tableHasField(tableName, "hostname")) {
        return "hostname";
    }
    
    return ""; // 如果表不存在主机名字段，返回空字符串
}

// 获取表对应的事件类型字段名
QString StationModel::getBayFieldForTable(const QString &tableName)
{
    // 按表名处理特殊情况
    if (tableName.compare(TABLE_SOE_EVENT, Qt::CaseInsensitive) == 0) {
        // 对于保护事件表，应该使用小写的valuetype字段
        return "valuetype";
    } else if (tableName.compare(TABLE_YX_CHANGE, Qt::CaseInsensitive) == 0) {
        // 遥信变位表没有事件类型字段，不应该显示事件类型筛选器
        return "";
    } else if (tableName.compare(TABLE_LIMIT_ALARM, Qt::CaseInsensitive) == 0) {
        // 越限告警表使用breaklimittype字段作为类型标识
        return "breaklimittype";
    } else if (tableName.compare(TABLE_OPERATION, Qt::CaseInsensitive) == 0) {
        // 操作记录表使用OPERTYPE字段
        return "OPERTYPE";
    } else if (tableName.compare(TABLE_SYSTEM_EVENT, Qt::CaseInsensitive) == 0) {
        // 系统事件表使用ERRTYPE字段
        return "ERRTYPE";
    } else if (tableName.compare(TABLE_USER_LOGIN, Qt::CaseInsensitive) == 0) {
        // 用户登录表没有事件类型字段
        return "";
    }
    
    // 如果不是预设的表，按顺序检查表中是否存在相关字段
    if (tableHasField(tableName, "VALUETYPE")) {
        return "VALUETYPE";
    } else if (tableHasField(tableName, "valuetype")) {
        return "valuetype";
    } else if (tableHasField(tableName, "BREAKLIMITTYPE")) {
        return "BREAKLIMITTYPE";
    } else if (tableHasField(tableName, "breaklimittype")) {
        return "breaklimittype";
    } else if (tableHasField(tableName, "OPERTYPE")) {
        return "OPERTYPE";
    } else if (tableHasField(tableName, "ERRTYPE")) {
        return "ERRTYPE";
    } else if (tableHasField(tableName, "BAYNAME")) {
        return "BAYNAME";
    }
    
    return ""; // 如果表不存在相关字段，返回空字符串
}

// 获取表对应的设备字段名
QString StationModel::getDeviceFieldForTable(const QString &tableName)
{
    if (tableName.compare(TABLE_SOE_EVENT, Qt::CaseInsensitive) == 0) {
        // 对于htsoelog表，应该使用equipid字段作为设备标识
        return "equipid";
    } else if (tableName.compare(TABLE_YX_CHANGE, Qt::CaseInsensitive) == 0) {
        return "equipid";  // 修改为使用equipid字段，而不是yxname
    } else if (tableName.compare(TABLE_LIMIT_ALARM, Qt::CaseInsensitive) == 0) {
        return "equipid";  // 修改为使用equipid，因为htbreaklimitlog表没有EQUIPNAME字段
    } else if (tableName.compare(TABLE_OPERATION, Qt::CaseInsensitive) == 0) {
        return "EQUIPNAME";
    } else if (tableName.compare(TABLE_SYSTEM_EVENT, Qt::CaseInsensitive) == 0) {
        return "SOURCENAME"; // 系统事件表使用SOURCENAME代替设备名
    } else if (tableName.compare(TABLE_USER_LOGIN, Qt::CaseInsensitive) == 0) {
        return "USERNAME"; // 用户登录表使用USERNAME字段作为筛选条件
    }
    
    // 检查表是否实际存在该字段
    if (tableHasField(tableName, "EQUIPNAME")) {
        return "EQUIPNAME";
    } else if (tableHasField(tableName, "equipid")) {
        return "equipid";
    } else if (tableHasField(tableName, "yxname")) {
        return "yxname";
    } else if (tableHasField(tableName, "DEVICENAME")) {
        return "DEVICENAME";
    } else if (tableHasField(tableName, "DEVICE")) {
        return "DEVICE";
    }
    
    return ""; // 如果表不存在设备相关字段，返回空字符串
}



// 预加载缓存数据
void StationModel::preloadCache(const QString &tableName)
{
    // 检查是否已经预加载过该表
    if (preloadedTables.contains(tableName) && preloadedTables[tableName]) {
        qDebug() << "表" << tableName << "已预加载过，跳过重复预加载";
        return;
    }
    
    qDebug() << "预加载表" << tableName << "的缓存数据";
    
    if (tableName.isEmpty()) {
        qWarning() << "表名为空，无法预加载缓存";
        return;
    }
    
    DBManager *dbManager = DBManager::instance();
    if (!dbManager->isConnected()) {
        qWarning() << "数据库未连接，无法预加载缓存";
        return;
    }
    
    // 获取表对应的字段名
    QString stationField = getStationFieldForTable(tableName);
    QString bayField = getBayFieldForTable(tableName);
    QString deviceField = getDeviceFieldForTable(tableName);
    
    // 1. 加载主机字段数据
    if (!stationField.isEmpty()) {
        if (tableHasField(tableName, stationField)) {
            // 完整加载主机名列表
            QList<Station> stations = getStationsFromTable(tableName, stationField);
            int actualCount = stations.size();
            int dataCount = actualCount > 1 ? actualCount - 1 : 0;
            qDebug() << "已加载表" << tableName << "的主机名列表，实际数据" << dataCount << "项";
        } else {
            qWarning() << "表" << tableName << "中不存在字段" << stationField << "，无法加载主机名";
        }
    }
    
    // 2. 预加载事件类型数据
    if (!bayField.isEmpty() && tableHasField(tableName, bayField)) {
        // 获取类型映射管理器
        TypeMappingManager* typeMappingManager = TypeMappingManager::instance();
        
        // 检查字段是否为映射类型字段
        if (typeMappingManager->isMappedTypeField(bayField)) {
            // 对于映射类型字段(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)，预加载完整的事件类型列表
            QList<Bay> bays = getEventTypesFromTable(tableName, bayField, "所有");
            int actualCount = bays.size();
            int dataCount = actualCount > 1 ? actualCount - 1 : 0;
            qDebug() << "已加载表" << tableName << "的事件类型列表，实际数据" << dataCount << "项";
        } else {
            // 只验证字段可用性
            QString queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                    "WHERE %1 IS NOT NULL "
                "LIMIT 1").arg(bayField.toLower(), tableName.toLower());
        
            QSqlQuery query = dbManager->executeQuery(queryStr);
            if (!query.lastError().isValid() && query.next()) {
                // 缓存字段信息
                if (!tableFieldsCache.contains(tableName)) {
                    tableFieldsCache[tableName] = QStringList();
                }
                if (!tableFieldsCache[tableName].contains(bayField, Qt::CaseInsensitive)) {
                    tableFieldsCache[tableName] << bayField;
                }
            }
        }
    }
    
    // 3. 检查设备字段
    if (!deviceField.isEmpty() && tableHasField(tableName, deviceField)) {
        // 只验证字段可用性
        QString queryStr = QString(
            "SELECT DISTINCT %1 FROM %2 "
            "WHERE %1 IS NOT NULL "
            "LIMIT 1").arg(deviceField.toLower(), tableName.toLower());
        
        QSqlQuery query = dbManager->executeQuery(queryStr);
        if (!query.lastError().isValid() && query.next()) {
            // 缓存字段信息
            if (!tableFieldsCache.contains(tableName)) {
                tableFieldsCache[tableName] = QStringList();
            }
            if (!tableFieldsCache[tableName].contains(deviceField, Qt::CaseInsensitive)) {
                tableFieldsCache[tableName] << deviceField;
            }
        }
    }
    
    // 标记该表已预加载
    preloadedTables[tableName] = true;
    qDebug() << "表" << tableName << "预加载完成";
}

// 从指定表获取事件类型列表
QList<Bay> StationModel::getEventTypesFromTable(const QString &tableName, const QString &typeField, const QString &stationName)
{
    // 获取站点字段
    QString stationField = getStationFieldForTable(tableName);
    
    // 检查表名是否为空
    if (tableName.isEmpty()) {
        qWarning() << "表名为空，无法获取事件类型列表";
        
        // 添加默认的"所有"选项
        Bay allType;
        allType.id = -1;
        allType.name = "所有";
        QList<Bay> result;
        result.append(allType);
        return result;
    }
    
    // 先检查缓存是否有效
    if (baysCache.contains(tableName) && 
        baysCache[tableName].contains(stationName) &&
        lastCacheUpdateTime.secsTo(QDateTime::currentDateTime()) < cacheExpirationSeconds) {
        return baysCache[tableName][stationName];
    }
    
    QList<Bay> eventTypes;
    QSet<QString> uniqueTypes; // 用于去重
    
    // 将所有选项添加到uniqueTypes以确保不会重复添加
    uniqueTypes.insert("所有");
    
    // 查询前先准备临时存储结果的列表
    QList<Bay> queryResults;
    
    // 获取类型映射管理器
    TypeMappingManager* typeMappingManager = TypeMappingManager::instance();
    
    // 对于映射类型字段的特殊处理：使用TypeMappingManager获取类型列表
    if (typeMappingManager->isMappedTypeField(typeField)) {
        qDebug() << "处理映射类型字段:" << typeField;
        int id = 0;
        
        // 使用TypeMappingManager获取所有映射名称
        QStringList typeNameList = typeMappingManager->getAllNames(typeField);
        
        // 直接从映射中生成类型列表
        for (const QString &typeName : typeNameList) {
            Bay typeItem;
            typeItem.id = id++;
            typeItem.name = typeName;
            queryResults.append(typeItem);
            uniqueTypes.insert(typeName);
        }
        
        // 如果不是BREAKLIMITTYPE字段，还需要从数据库查询额外的类型值
        if (typeField.compare("BREAKLIMITTYPE", Qt::CaseInsensitive) != 0) {
            // 继续执行数据库查询，获取可能的额外类型
            getTypesFromDatabase(tableName, typeField, stationName, stationField, typeMappingManager, queryResults, uniqueTypes);
        }
    } else {
        // 检查参数
        if (typeField.isEmpty()) {
            qWarning() << "字段名为空，无法获取事件类型列表";
        } else if (!tableHasField(tableName, typeField)) {
            qWarning() << "表" << tableName << "不存在字段" << typeField;
        } else {
            // 执行数据库查询获取类型值
            getTypesFromDatabase(tableName, typeField, stationName, stationField, typeMappingManager, queryResults, uniqueTypes);
        }
    }
    
    // 添加"所有"选项到最前面
    Bay allType;
    allType.id = -1;
    allType.name = "所有";
    eventTypes.append(allType);
    
    // 然后添加其他事件类型
    eventTypes.append(queryResults);
    
    // 更新缓存
    if (!baysCache.contains(tableName)) {
        baysCache[tableName] = QMap<QString, QList<Bay>>();
    }
    baysCache[tableName][stationName] = eventTypes;
    lastCacheUpdateTime = QDateTime::currentDateTime();
    
    qDebug() << "查询完成，事件类型总数:" << eventTypes.size();
    
    return eventTypes;
}

// 从数据库获取类型数据的辅助方法
void StationModel::getTypesFromDatabase(const QString &tableName, const QString &typeField, 
                                      const QString &stationName, const QString &stationField,
                                      TypeMappingManager* typeMappingManager, 
                                      QList<Bay> &results, QSet<QString> &uniqueTypes)
{
    // 构建查询SQL - 从指定表中获取唯一的类型值
    QString queryStr;
    
    if (stationName == "所有" || stationName.isEmpty() || stationField.isEmpty() || !tableHasField(tableName, stationField)) {
        // 不按站点筛选
        // 如果是数值类型字段，尝试按数值排序
        if (typeMappingManager->isMappedTypeField(typeField)) {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE %1 IS NOT NULL "
                "ORDER BY CAST(%1 AS SIGNED)").arg(typeField.toLower(), tableName.toLower());
        } else {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE %1 IS NOT NULL "
                "ORDER BY LENGTH(%1), %1").arg(typeField.toLower(), tableName.toLower());
        }
    } else {
        // 按站点筛选
        // 如果是数值类型字段，尝试按数值排序
        if (typeMappingManager->isMappedTypeField(typeField)) {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE LOWER(%3) = LOWER('%4') AND %1 IS NOT NULL "
                "ORDER BY CAST(%1 AS SIGNED)").arg(typeField.toLower(), tableName.toLower(), 
                                                 stationField.toLower(), stationName);
        } else {
            queryStr = QString(
                "SELECT DISTINCT %1 FROM %2 "
                "WHERE LOWER(%3) = LOWER('%4') AND %1 IS NOT NULL "
                "ORDER BY LENGTH(%1), %1").arg(typeField.toLower(), tableName.toLower(), 
                                             stationField.toLower(), stationName);
        }
    }
    
    qDebug() << "查询事件类型: 表名=" << tableName << ", 类型字段=" << typeField;
    
    // 执行查询
    DBManager *dbManager = DBManager::instance();
    if (dbManager->isConnected()) {
        QSqlQuery query = dbManager->executeQuery(queryStr);
        
        if (query.lastError().isValid()) {
            qWarning() << "获取事件类型信息失败:" << query.lastError().text();
        } else {
            // 处理查询结果
            int id = results.size(); // 起始ID应该考虑已有结果
            int rowCount = 0;
            
            while (query.next()) {
                rowCount++;
                QVariant rawValue = query.value(0);
                QString rawValueStr = rawValue.toString().trimmed();
                
                // 检查是否为空值
                if (rawValueStr.isEmpty()) {
                    continue;
                }
                
                // 对于映射类型字段，使用TypeMappingManager获取类型名称
                QString typeName;
                if (typeMappingManager->isMappedTypeField(typeField)) {
                    typeName = typeMappingManager->getNameByValue(typeField, rawValueStr);
                } else {
                    typeName = rawValueStr; // 非映射字段直接使用值
                }
                
                // 去重处理
                if (!uniqueTypes.contains(typeName)) {
                    Bay eventType;
                    eventType.id = id++;
                    eventType.name = typeName;
                    results.append(eventType);
                    uniqueTypes.insert(typeName);
                }
            }
        }
    } else {
        qWarning() << "数据库未连接，无法获取事件类型信息";
    }
}

