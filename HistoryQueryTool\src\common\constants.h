#ifndef CONSTANTS_H
#define CONSTANTS_H

#include <QString>

namespace Constants {

// 数据库名称
namespace Databases {
    const QString HIS_DB = "his_mysql";          // 历史数据库
    const QString STAKE_PARA_DB = "stakeparadb"; // 遥测参数库
}

// 表名常量
namespace Tables {
    const QString SOE_LOG = "htsoelog";         // SOE事件表
    const QString COS_LOG = "htcoslog";         // 遥信变位表
    const QString BREAK_LIMIT_LOG = "htbreaklimitlog"; // 越限告警表
    const QString SYS_ERROR_INFO = "syserrorinfo";     // 系统错误信息表
    const QString OPERATION_LOG = "htoperationlog";    // 操作记录表
    const QString USER_LOG = "htuserlog";             // 用户登录表
    const QString PARA_YC = "para_yc";                // 遥测参数表
}

// 字段名常量
namespace Fields {
    // 通用字段
    namespace Common {
        const QString ID = "ID";
        const QString HOSTNAME = "HOSTNAME";
        const QString START_TIME = "STARTTIME";
        const QString FLAG_ACK = "FLAGACK";
        const QString ACK_PERSON = "ACKPERSON";
        const QString EQUIP_ID = "EQUIPID";
    }
    
    // SOE事件表字段
    namespace SOELog {
        const QString YX_ID = "YXID";
        const QString YX_NAME = "YXNAME";
        const QString VALUE_TYPE = "VALUETYPE";
        const QString START_MS = "STARTMS";
        const QString TIME_IN = "TIMEIN";
        const QString OLD_VALUE = "OLDVALUE";
        const QString NEW_VALUE = "NEWVALUE";
    }
    
    // 遥信变位表字段
    namespace COSLog {
        const QString YX_ID = "YXID";
        const QString YX_NAME = "YXNAME";
        const QString OLD_VALUE = "OLDVALUE";
        const QString NEW_VALUE = "NEWVALUE";
        const QString MSEC = "MSEC";
    }
    
    // 越限告警表字段
    namespace BreakLimitLog {
        const QString YC_ID = "YCID";
        const QString YCT_NAME = "YCTNAME";
        const QString BREAK_LIMIT_TYPE = "BREAKLIMITTYPE";
        const QString END_TIME = "ENDTIME";
        const QString LIMIT_VALUE = "LIMITVALUE";
        const QString BREAK_VALUE = "BREAKVALUE";
        const QString RECOVERD = "RECOVERD";
    }
    
    // 系统错误信息表字段
    namespace SysErrorInfo {
        const QString SOURCE_NAME = "SOURCENAME";
        const QString ERROR_INFO = "ERRORINFO";
        const QString ERR_OBJ_ID = "ERROBJID";
        const QString ERR_STATE = "ERRSTATE";
        const QString ERR_TYPE = "ERRTYPE";
    }
    
    // 操作记录表字段
    namespace OperationLog {
        const QString EQUIP_NAME = "EQUIPNAME";
        const QString CTRL_SIG_ID = "CTRLSIGID";
        const QString CTRL_SIG_NAME = "CTRLSIGNAME";
        const QString CTRL_TYPE = "CTRLTYPE";
        const QString OPER_TYPE = "OPERTYPE";
        const QString OPER_VALUE = "OPERVALUE";
        const QString OPER_RESULT = "OPERRESULT";
        const QString OPERATOR_NAME = "OPERATORNAME";
        const QString SUPERVISOR_NAME = "SUPERVISORNAME";
    }
    
    // 用户登录表字段
    namespace UserLog {
        const QString USERNAME = "USERNAME";
        const QString LOG_TYPE = "LOGTYPE";
        const QString APP_NAME = "APPNAME";
    }
    
    // 遥测参数表字段
    namespace ParaYC {
        const QString ID = "ID";
        const QString DEVICE_ID = "DeviceID";
        const QString DEVICE_INDEX = "DeviceIndex";
        const QString NAME = "Name";
        const QString CHART_TYPE = "ChartType";
        const QString UNIT = "Unit";
        const QString RATE = "Rate";
        const QString OFFSET = "Offset";
        const QString DEAD_RANGE = "DeadRange";
        const QString LOCKED = "Locked";
        const QString MANUAL = "Manual";
        const QString AUTO_CALL = "AutoCall";
    }
}

} // namespace Constants

#endif // CONSTANTS_H 