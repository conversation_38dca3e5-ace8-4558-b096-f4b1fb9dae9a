#include "telemetrydataprovider.h"
#include "../../database/dbmanager.h"
#include "../../common/constants.h"
#include <QSqlError>
#include <QDebug>
#include <QSqlDatabase>
#include <QSqlRecord>

TelemetryDataProvider::TelemetryDataProvider(QObject *parent)
    : QObject(parent), m_dbManager(nullptr)
{
    // 设置表名前缀
    m_minuteTablePrefix = "htdaystore_min_"; // 分钟级遥测历史表前缀
    m_secondTablePrefix = "htdaystore_sec_"; // 秒级遥测历史表前缀
    m_telemetryParamTable = Constants::Tables::PARA_YC;  // 遥测参数表
}

void TelemetryDataProvider::setDBManager(DBManager *dbManager)
{
    m_dbManager = dbManager;
}

QSqlQuery TelemetryDataProvider::executeMinuteTelemetryQuery(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    QSqlQuery query;
    if (!isDatabaseConnected()) {
        qDebug() << "数据库未连接，无法执行分钟级遥测查询";
        return query;
    }

    // 检查是否是跨日期查询
    if (startTime.date() != endTime.date()) {
        // 跨日期查询需要特殊处理
        return executeCrossDayQuery(deviceId, deviceIndex, startTime, endTime, true);
    }

    // 构建分钟级查询SQL
    QString sql = buildMinuteTelemetrySQL(deviceId, deviceIndex, startTime, endTime);
    qDebug() << "执行分钟级遥测查询SQL:" << sql;

    if (m_dbManager) {
        query = m_dbManager->executeQuery(sql);
        if (query.lastError().isValid()) {
            qDebug() << "分钟级遥测查询失败:" << query.lastError().text();
        } else {
            qDebug() << "分钟级遥测查询成功";
        }
    }

    return query;
}

QSqlQuery TelemetryDataProvider::executeSecondTelemetryQuery(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    QSqlQuery query;
    if (!isDatabaseConnected()) {
        qDebug() << "数据库未连接，无法执行秒级遥测查询";
        return query;
    }

    // 检查是否是跨日期查询
    if (startTime.date() != endTime.date()) {
        // 跨日期查询需要特殊处理
        return executeCrossDayQuery(deviceId, deviceIndex, startTime, endTime, false);
    }

    // 构建秒级查询SQL
    QString sql = buildSecondTelemetrySQL(deviceId, deviceIndex, startTime, endTime);
    qDebug() << "执行秒级遥测查询SQL:" << sql;

    if (m_dbManager) {
        query = m_dbManager->executeQuery(sql);
        if (query.lastError().isValid()) {
            qDebug() << "秒级遥测查询失败:" << query.lastError().text();
        } else {
            qDebug() << "秒级遥测查询成功";
        }
    }

    return query;
}

QString TelemetryDataProvider::buildMinuteTelemetrySQL(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    // 获取日期对应的表名
    QString tableName = getMinuteTableName(startTime.date());
    
    // 构建分钟级遥测查询SQL
    QString sql = QString("SELECT Timestamp, Value, Quality FROM %1 WHERE DeviceID = %2 AND DeviceIndex = %3 "
                         "AND Timestamp BETWEEN '%4' AND '%5' ORDER BY Timestamp")
                    .arg(tableName)
                    .arg(deviceId)
                    .arg(deviceIndex)
                    .arg(startTime.toString("yyyy-MM-dd HH:mm:ss"))
                    .arg(endTime.toString("yyyy-MM-dd HH:mm:ss"));

    return sql;
}

QString TelemetryDataProvider::buildSecondTelemetrySQL(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime)
{
    // 获取日期对应的表名
    QString tableName = getSecondTableName(startTime.date());
    
    // 构建秒级遥测查询SQL
    QString sql = QString("SELECT Timestamp, Value, Quality FROM %1 WHERE DeviceID = %2 AND DeviceIndex = %3 "
                         "AND Timestamp BETWEEN '%4' AND '%5' ORDER BY Timestamp")
                    .arg(tableName)
                    .arg(deviceId)
                    .arg(deviceIndex)
                    .arg(startTime.toString("yyyy-MM-dd HH:mm:ss"))
                    .arg(endTime.toString("yyyy-MM-dd HH:mm:ss"));

    return sql;
}

QMap<QString, QVariant> TelemetryDataProvider::getTelemetryProperties(int deviceId, int deviceIndex)
{
    QMap<QString, QVariant> properties;
    if (!isDatabaseConnected()) {
        qDebug() << "数据库未连接，无法获取遥测点属性";
        return properties;
    }

    // 查询遥测参数表，获取遥测点的属性信息
    QString sql = QString("SELECT Name, Unit, Rate, Offset FROM %1 "
                         "WHERE DeviceID = %2 AND DeviceIndex = %3")
                    .arg(m_telemetryParamTable)
                    .arg(deviceId)
                    .arg(deviceIndex);

    if (m_dbManager) {
        QSqlQuery query = m_dbManager->executeQuery(sql);
        if (query.next()) {
            properties["Name"] = query.value("Name");
            properties["Unit"] = query.value("Unit");
            properties["Rate"] = query.value("Rate");
            properties["Offset"] = query.value("Offset");
        } else {
            qDebug() << "未找到遥测点属性信息:" << deviceId << deviceIndex;
        }
    }

    return properties;
}

bool TelemetryDataProvider::isDatabaseConnected() const
{
    return m_dbManager && m_dbManager->isConnected();
}

QString TelemetryDataProvider::getMinuteTableName(const QDate &date) const
{
    // 生成分钟级表名: htdaystore_min_YYYYMMDD
    return m_minuteTablePrefix + date.toString("yyyyMMdd");
}

QString TelemetryDataProvider::getSecondTableName(const QDate &date) const
{
    // 生成秒级表名: htdaystore_sec_YYYYMMDD
    return m_secondTablePrefix + date.toString("yyyyMMdd");
}

QSqlQuery TelemetryDataProvider::executeCrossDayQuery(
    int deviceId, int deviceIndex, const QDateTime &startTime, const QDateTime &endTime, bool isMinute)
{
    qDebug() << "执行跨日期查询:" 
             << "DeviceID=" << deviceId 
             << "DeviceIndex=" << deviceIndex
             << "StartTime=" << startTime.toString("yyyy-MM-dd HH:mm:ss")
             << "EndTime=" << endTime.toString("yyyy-MM-dd HH:mm:ss")
             << "Type=" << (isMinute ? "分钟级" : "秒级");
    
    // 创建临时表存放结果
    QString tempTableName = QString("temp_telemetry_%1").arg(QDateTime::currentMSecsSinceEpoch());
    
    // 创建临时表SQL
    QString createTempSQL = QString(
        "CREATE TEMPORARY TABLE %1 ("
        "Timestamp DATETIME, "
        "Value DOUBLE, "
        "Quality INT"
        ")")
        .arg(tempTableName);
    
    QSqlQuery query;
    if (m_dbManager) {
        // 创建临时表
        query = m_dbManager->executeQuery(createTempSQL);
        if (query.lastError().isValid()) {
            qDebug() << "创建临时表失败:" << query.lastError().text();
            return query;
        }
        
        // 计算日期范围
        QDate currentDate = startTime.date();
        QDate endDate = endTime.date();
        
        // 遍历日期范围，查询每一天的数据并插入临时表
        while (currentDate <= endDate) {
            // 计算当天的查询时间范围
            QDateTime dayStart = (currentDate == startTime.date()) ? 
                                startTime : 
                                QDateTime(currentDate, QTime(0, 0, 0));
            
            QDateTime dayEnd = (currentDate == endDate) ? 
                              endTime : 
                              QDateTime(currentDate, QTime(23, 59, 59, 999));
            
            // 构建当天的查询
            QString daySql;
            if (isMinute) {
                daySql = buildMinuteTelemetrySQL(deviceId, deviceIndex, dayStart, dayEnd);
            } else {
                daySql = buildSecondTelemetrySQL(deviceId, deviceIndex, dayStart, dayEnd);
            }
            
            // 检查表是否存在
            QString tableName = isMinute ? 
                               getMinuteTableName(currentDate) : 
                               getSecondTableName(currentDate);
            
            QString checkTableSql = QString(
                "SELECT COUNT(*) FROM information_schema.tables "
                "WHERE table_schema = DATABASE() AND table_name = '%1'")
                .arg(tableName);
            
            QSqlQuery checkQuery = m_dbManager->executeQuery(checkTableSql);
            if (checkQuery.next() && checkQuery.value(0).toInt() > 0) {
                // 表存在，执行查询
                QSqlQuery dayQuery = m_dbManager->executeQuery(daySql);
                
                // 插入到临时表
                while (dayQuery.next()) {
                    QString insertSql = QString(
                        "INSERT INTO %1 (Timestamp, Value, Quality) VALUES ('%2', %3, %4)")
                        .arg(tempTableName)
                        .arg(dayQuery.value(0).toDateTime().toString("yyyy-MM-dd HH:mm:ss"))
                        .arg(dayQuery.value(1).toDouble())
                        .arg(dayQuery.value(2).toInt());
                    
                    m_dbManager->executeQuery(insertSql);
                }
            } else {
                qDebug() << "表不存在:" << tableName;
            }
            
            // 移动到下一天
            currentDate = currentDate.addDays(1);
        }
        
        // 查询临时表，获取最终结果
        QString finalSql = QString("SELECT * FROM %1 ORDER BY Timestamp").arg(tempTableName);
        query = m_dbManager->executeQuery(finalSql);
        
        // 删除临时表
        QString dropTempSql = QString("DROP TEMPORARY TABLE IF EXISTS %1").arg(tempTableName);
        m_dbManager->executeQuery(dropTempSql);
    }
    
    return query;
} 