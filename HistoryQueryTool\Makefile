#############################################################################
# Makefile for building: HistoryQueryTool
# Generated by qmake (3.1) (Qt 5.9.2)
# Project:  HistoryQueryTool.pro
# Template: app
# Command: /home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/qmake -o Makefile HistoryQueryTool.pro
#############################################################################

MAKEFILE      = Makefile

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DQT_NO_DEBUG -DQT_PRINTSUPPORT_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_SQL_LIB -DQT_XML_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -O2 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -Wno-deprecated-copy -O2 -std=gnu++11 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I. -I../../../Qt5.9.2/5.9.2/gcc_64/include -I../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui -I../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql -I../../../Qt5.9.2/5.9.2/gcc_64/include/QtXml -I../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore -I. -isystem /usr/include/libdrm -I. -I../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++
QMAKE         = /home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/qmake -install qinstall
QINSTALL_PROGRAM = /home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = HistoryQueryTool1.0.0
DISTDIR = /home/<USER>/home/<USER>/HistoryQueryTool/.tmp/HistoryQueryTool1.0.0
LINK          = g++
LFLAGS        = -Wl,-O1 -Wl,-rpath,/home/<USER>/Qt5.9.2/5.9.2/gcc_64/lib
LIBS          = $(SUBLIBS) -L/home/<USER>/Qt5.9.2/5.9.2/gcc_64/lib -lQt5PrintSupport -lQt5Widgets -lQt5Gui -lQt5Sql -lQt5Xml -lQt5Core -lGL -lpthread 
AR            = ar cqs
RANLIB        = 
SED           = sed
STRIP         = strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = src/main.cpp \
		src/mainwindow.cpp \
		src/database/dbmanager.cpp \
		src/database/dbconnectionfactory.cpp \
		src/models/station/stationmodel.cpp \
		src/models/customquerymodel.cpp \
		src/models/telemetrydatamodel.cpp \
		src/widgets/historytreewidget.cpp \
		src/widgets/querywidget.cpp \
		src/widgets/soehistorywidget.cpp \
		src/widgets/filters/filtermanager.cpp \
		src/widgets/providers/historydataprovider.cpp \
		src/widgets/providers/telemetrydataprovider.cpp \
		src/widgets/tables/tablemanager.cpp \
		src/utils/db/tablestructuremanager.cpp \
		src/utils/xmlconfigreader.cpp \
		src/utils/devicenamemapper.cpp \
		src/utils/typemappingmanager.cpp \
		src/common/logger.cpp \
		src/common/tablenamemanager.cpp moc_mainwindow.cpp \
		moc_dbmanager.cpp \
		moc_stationmodel.cpp \
		moc_customquerymodel.cpp \
		moc_telemetrydatamodel.cpp \
		moc_historytreewidget.cpp \
		moc_querywidget.cpp \
		moc_soehistorywidget.cpp \
		moc_filtermanager.cpp \
		moc_historydataprovider.cpp \
		moc_telemetrydataprovider.cpp \
		moc_tablemanager.cpp \
		moc_tablestructuremanager.cpp \
		moc_devicenamemapper.cpp \
		moc_typemappingmanager.cpp
OBJECTS       = main.o \
		mainwindow.o \
		dbmanager.o \
		dbconnectionfactory.o \
		stationmodel.o \
		customquerymodel.o \
		telemetrydatamodel.o \
		historytreewidget.o \
		querywidget.o \
		soehistorywidget.o \
		filtermanager.o \
		historydataprovider.o \
		telemetrydataprovider.o \
		tablemanager.o \
		tablestructuremanager.o \
		xmlconfigreader.o \
		devicenamemapper.o \
		typemappingmanager.o \
		logger.o \
		tablenamemanager.o \
		moc_mainwindow.o \
		moc_dbmanager.o \
		moc_stationmodel.o \
		moc_customquerymodel.o \
		moc_telemetrydatamodel.o \
		moc_historytreewidget.o \
		moc_querywidget.o \
		moc_soehistorywidget.o \
		moc_filtermanager.o \
		moc_historydataprovider.o \
		moc_telemetrydataprovider.o \
		moc_tablemanager.o \
		moc_tablestructuremanager.o \
		moc_devicenamemapper.o \
		moc_typemappingmanager.o
DIST          = ../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/spec_pre.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/unix.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/linux.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/sanitize.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/gcc-base.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/gcc-base-unix.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/g++-base.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/g++-unix.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/qconfig.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_core.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_help.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_location.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_network.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_script.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt_functions.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt_config.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++/qmake.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/spec_post.prf \
		.qmake.stash \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/exclusive_builds.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/toolchain.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/default_pre.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/resolve_config.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/default_post.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/warn_on.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/resources.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/moc.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/unix/opengl.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/uic.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/unix/thread.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qmake_use.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/file_copies.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/testcase_targets.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/exceptions.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/yacc.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/lex.prf \
		HistoryQueryTool.pro src/mainwindow.h \
		src/database/dbmanager.h \
		src/database/dbconnectionfactory.h \
		src/models/station/stationmodel.h \
		src/models/customquerymodel.h \
		src/models/telemetrydatamodel.h \
		src/widgets/historytreewidget.h \
		src/widgets/querywidget.h \
		src/widgets/soehistorywidget.h \
		src/widgets/filters/filtermanager.h \
		src/widgets/providers/historydataprovider.h \
		src/widgets/providers/telemetrydataprovider.h \
		src/widgets/tables/tablemanager.h \
		src/utils/db/tablestructuremanager.h \
		src/common/enums.h \
		src/common/constants.h \
		src/common/datastructures.h \
		src/utils/xmlconfigreader.h \
		src/utils/devicenamemapper.h \
		src/utils/typemappingmanager.h \
		src/common/logger.h \
		src/common/tablenamemanager.h src/main.cpp \
		src/mainwindow.cpp \
		src/database/dbmanager.cpp \
		src/database/dbconnectionfactory.cpp \
		src/models/station/stationmodel.cpp \
		src/models/customquerymodel.cpp \
		src/models/telemetrydatamodel.cpp \
		src/widgets/historytreewidget.cpp \
		src/widgets/querywidget.cpp \
		src/widgets/soehistorywidget.cpp \
		src/widgets/filters/filtermanager.cpp \
		src/widgets/providers/historydataprovider.cpp \
		src/widgets/providers/telemetrydataprovider.cpp \
		src/widgets/tables/tablemanager.cpp \
		src/utils/db/tablestructuremanager.cpp \
		src/utils/xmlconfigreader.cpp \
		src/utils/devicenamemapper.cpp \
		src/utils/typemappingmanager.cpp \
		src/common/logger.cpp \
		src/common/tablenamemanager.cpp
QMAKE_TARGET  = HistoryQueryTool
DESTDIR       = 
TARGET        = HistoryQueryTool


first: all
####### Build rules

$(TARGET): ui_mainwindow.h ui_querywidget.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: HistoryQueryTool.pro ../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++/qmake.conf ../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/spec_pre.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/unix.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/linux.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/sanitize.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/gcc-base.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/gcc-base-unix.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/g++-base.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/g++-unix.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/qconfig.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3danimation.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dcore.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dextras.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dinput.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquick.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3drender.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_charts.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_charts_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_concurrent.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_core.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_core_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_dbus.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designer.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designer_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gamepad.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gui.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gui_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_help.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_help_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_location.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_location_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimedia.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_network.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_network_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_networkauth.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_nfc.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_opengl.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_positioning.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_printsupport.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_purchasing.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qml.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qml_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmltest.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quick.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quick_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_repparser.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_script.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_script_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scripttools.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scxml.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sensors.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialbus.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialport.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sql.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sql_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_svg.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_svg_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_testlib.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uitools.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webchannel.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webengine.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_websockets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webview.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webview_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_widgets.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_x11extras.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xml.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xml_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt_functions.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt_config.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++/qmake.conf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/spec_post.prf \
		.qmake.stash \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/exclusive_builds.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/toolchain.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/default_pre.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/resolve_config.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/default_post.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/warn_on.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/resources.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/moc.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/unix/opengl.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/uic.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/unix/thread.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qmake_use.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/file_copies.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/testcase_targets.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/exceptions.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/yacc.prf \
		../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/lex.prf \
		HistoryQueryTool.pro \
		../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5PrintSupport.prl \
		../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Widgets.prl \
		../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Gui.prl \
		../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Sql.prl \
		../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Xml.prl \
		../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Core.prl
	$(QMAKE) -o Makefile HistoryQueryTool.pro
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/spec_pre.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/unix.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/linux.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/sanitize.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/gcc-base.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/gcc-base-unix.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/g++-base.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/common/g++-unix.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/qconfig.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3danimation.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3danimation_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dcore.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dcore_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dextras.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dextras_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dinput.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dinput_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dlogic.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquick.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquick_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3drender.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_3drender_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bluetooth.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_charts.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_charts_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_concurrent.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_concurrent_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_core.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_core_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_datavisualization.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_datavisualization_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_dbus.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_dbus_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designer.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designer_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_egl_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_fb_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gamepad.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gamepad_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_glx_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gui.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_gui_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_help.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_help_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_input_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_kms_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_location.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_location_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimedia.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimedia_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_network.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_network_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_networkauth.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_networkauth_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_nfc.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_nfc_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_opengl.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_opengl_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_openglextensions.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_positioning.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_positioning_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_printsupport.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_printsupport_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_purchasing.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_purchasing_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qml.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qml_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmltest.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qmltest_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quick.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quick_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_repparser.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_repparser_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_script.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_script_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scripttools.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scripttools_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scxml.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_scxml_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sensors.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sensors_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialbus.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialbus_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialport.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_serialport_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_service_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sql.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_sql_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_svg.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_svg_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_testlib.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_testlib_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_texttospeech.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_theme_support_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uiplugin.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uitools.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_uitools_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webchannel.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webchannel_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webengine.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webengine_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecore.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecore_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginecoreheaders_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginewidgets.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webenginewidgets_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_websockets.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_websockets_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webview.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_webview_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_widgets.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_widgets_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_x11extras.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_x11extras_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xml.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xml_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt_functions.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt_config.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++/qmake.conf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/spec_post.prf:
.qmake.stash:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/exclusive_builds.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/toolchain.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/default_pre.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/resolve_config.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/default_post.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/warn_on.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qt.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/resources.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/moc.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/unix/opengl.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/uic.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/unix/thread.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/qmake_use.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/file_copies.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/testcase_targets.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/exceptions.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/yacc.prf:
../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/lex.prf:
HistoryQueryTool.pro:
../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5PrintSupport.prl:
../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Widgets.prl:
../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Gui.prl:
../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Sql.prl:
../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Xml.prl:
../../../Qt5.9.2/5.9.2/gcc_64/lib/libQt5Core.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile HistoryQueryTool.pro

qmake_all: FORCE


all: Makefile $(TARGET)

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents ../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents src/mainwindow.h src/database/dbmanager.h src/database/dbconnectionfactory.h src/models/station/stationmodel.h src/models/customquerymodel.h src/models/telemetrydatamodel.h src/widgets/historytreewidget.h src/widgets/querywidget.h src/widgets/soehistorywidget.h src/widgets/filters/filtermanager.h src/widgets/providers/historydataprovider.h src/widgets/providers/telemetrydataprovider.h src/widgets/tables/tablemanager.h src/utils/db/tablestructuremanager.h src/common/enums.h src/common/constants.h src/common/datastructures.h src/utils/xmlconfigreader.h src/utils/devicenamemapper.h src/utils/typemappingmanager.h src/common/logger.h src/common/tablenamemanager.h $(DISTDIR)/
	$(COPY_FILE) --parents src/main.cpp src/mainwindow.cpp src/database/dbmanager.cpp src/database/dbconnectionfactory.cpp src/models/station/stationmodel.cpp src/models/customquerymodel.cpp src/models/telemetrydatamodel.cpp src/widgets/historytreewidget.cpp src/widgets/querywidget.cpp src/widgets/soehistorywidget.cpp src/widgets/filters/filtermanager.cpp src/widgets/providers/historydataprovider.cpp src/widgets/providers/telemetrydataprovider.cpp src/widgets/tables/tablemanager.cpp src/utils/db/tablestructuremanager.cpp src/utils/xmlconfigreader.cpp src/utils/devicenamemapper.cpp src/utils/typemappingmanager.cpp src/common/logger.cpp src/common/tablenamemanager.cpp $(DISTDIR)/
	$(COPY_FILE) --parents src/mainwindow.ui src/widgets/querywidget.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: ../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/data/dummy.cpp
	g++ -pipe -Wno-deprecated-copy -O2 -std=gnu++11 -Wall -W -dM -E -o moc_predefs.h ../../../Qt5.9.2/5.9.2/gcc_64/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_mainwindow.cpp moc_dbmanager.cpp moc_stationmodel.cpp moc_customquerymodel.cpp moc_telemetrydatamodel.cpp moc_historytreewidget.cpp moc_querywidget.cpp moc_soehistorywidget.cpp moc_filtermanager.cpp moc_historydataprovider.cpp moc_telemetrydataprovider.cpp moc_tablemanager.cpp moc_tablestructuremanager.cpp moc_devicenamemapper.cpp moc_typemappingmanager.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_mainwindow.cpp moc_dbmanager.cpp moc_stationmodel.cpp moc_customquerymodel.cpp moc_telemetrydatamodel.cpp moc_historytreewidget.cpp moc_querywidget.cpp moc_soehistorywidget.cpp moc_filtermanager.cpp moc_historydataprovider.cpp moc_telemetrydataprovider.cpp moc_tablemanager.cpp moc_tablestructuremanager.cpp moc_devicenamemapper.cpp moc_typemappingmanager.cpp
moc_mainwindow.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMainWindow \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmainwindow.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QToolBar \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtoolbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qaction.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qactiongroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QAction \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QSplitter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsplitter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/models/station/stationmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/widgets/soehistorywidget.h \
		src/widgets/querywidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		src/common/enums.h \
		src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		src/widgets/providers/telemetrydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		src/common/datastructures.h \
		src/models/telemetrydatamodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreeview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidgetitemiterator.h \
		src/widgets/historytreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidgetItem \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QtSql \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QtSqlDepends \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QtCore \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QtCoreDepends \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstracteventdispatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeventloop.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractnativeeventfilter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstracttransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydataops.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydatapointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbitarray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbuffer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraymatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcollator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcommandlineoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcommandlineparser.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreapplication.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcryptographichash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qelapsedtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdiriterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeasingcurve.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qendian.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeventtransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qexception.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfactoryinterface.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileselector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfilesystemwatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfinalstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuture.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfutureinterface.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrunnable.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qresultstore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuturesynchronizer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuturewatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhistorystate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qidentityproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qisenum.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonarray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonvalue.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsondocument.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlibrary.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlibraryinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversionnumber.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlinkedlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlockfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qloggingcategory.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetaobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimedata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimedatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimetype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectcleanuphandler.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qoperatingsystemversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qparallelanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpauseanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qplugin.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpluginloader.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocess.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpropertyanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariantanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qqueue.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qreadwritelock.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qresource.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsavefile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedvaluerollback.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsemaphore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsequentialanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsettings.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedmemory.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsignalmapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsignaltransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsocketnotifier.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsortfilterproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstack.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstandardpaths.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstatemachine.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstorageinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlistmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemsemaphore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtemporarydir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QScopedPointer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtemporaryfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextboundaryfinder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextcodec.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthread.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthreadpool.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthreadstorage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimeline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimezone.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtranslator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypetraits.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/quuid.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qwaitcondition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qwineventnotifier.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qxmlstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcoreversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldriver.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldriverplugin.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlfield.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlindex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrelationaldelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlistview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qcombobox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrelationaltablemodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqltablemodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlresult.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTableView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtableview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QStandardItemModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qstandarditemmodel.h \
		src/mainwindow.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/mainwindow.h -o moc_mainwindow.cpp

moc_dbmanager.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/database/dbmanager.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/database/dbmanager.h -o moc_dbmanager.cpp

moc_stationmodel.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/models/station/stationmodel.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/models/station/stationmodel.h -o moc_stationmodel.cpp

moc_customquerymodel.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		src/models/customquerymodel.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/models/customquerymodel.h -o moc_customquerymodel.cpp

moc_telemetrydatamodel.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		src/common/datastructures.h \
		src/models/telemetrydatamodel.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/models/telemetrydatamodel.h -o moc_telemetrydatamodel.cpp

moc_historytreewidget.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreeview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidgetitemiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidgetItem \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		src/common/enums.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/widgets/historytreewidget.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/widgets/historytreewidget.h -o moc_historytreewidget.cpp

moc_querywidget.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		src/widgets/querywidget.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/widgets/querywidget.h -o moc_querywidget.cpp

moc_soehistorywidget.cpp: src/widgets/querywidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		src/common/enums.h \
		src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		src/widgets/providers/telemetrydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		src/common/datastructures.h \
		src/models/telemetrydatamodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreeview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidgetitemiterator.h \
		src/widgets/soehistorywidget.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/widgets/soehistorywidget.h -o moc_soehistorywidget.cpp

moc_filtermanager.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QGridLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qgridlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qboxlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QHBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QVBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLabel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlabel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QComboBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qcombobox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLineEdit \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlineedit.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpen.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QDateTimeEdit \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdatetimeedit.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QFrame \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QSpacerItem \
		src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		src/common/enums.h \
		src/widgets/filters/filtermanager.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/widgets/filters/filtermanager.h -o moc_filtermanager.cpp

moc_historydataprovider.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		src/common/enums.h \
		src/widgets/providers/historydataprovider.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/widgets/providers/historydataprovider.h -o moc_historydataprovider.cpp

moc_telemetrydataprovider.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		src/common/enums.h \
		src/common/datastructures.h \
		src/widgets/providers/telemetrydataprovider.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/widgets/providers/telemetrydataprovider.h -o moc_telemetrydataprovider.cpp

moc_tablemanager.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTableView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtableview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		src/widgets/tables/tablemanager.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/widgets/tables/tablemanager.h -o moc_tablemanager.cpp

moc_tablestructuremanager.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		src/utils/db/tablestructuremanager.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/utils/db/tablestructuremanager.h -o moc_tablestructuremanager.cpp

moc_devicenamemapper.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/utils/devicenamemapper.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/utils/devicenamemapper.h -o moc_devicenamemapper.cpp

moc_typemappingmanager.cpp: ../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		src/utils/typemappingmanager.h \
		moc_predefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/moc
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/moc $(DEFINES) --include ./moc_predefs.h -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/mkspecs/linux-g++ -I/home/<USER>/home/<USER>/HistoryQueryTool -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtWidgets -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtGui -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtSql -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtXml -I/home/<USER>/Qt5.9.2/5.9.2/gcc_64/include/QtCore -I/usr/include/c++/13 -I/usr/include/x86_64-linux-gnu/c++/13 -I/usr/include/c++/13/backward -I/usr/lib/gcc/x86_64-linux-gnu/13/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include src/utils/typemappingmanager.h -o moc_typemappingmanager.cpp

compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h ui_querywidget.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h ui_querywidget.h
ui_mainwindow.h: src/mainwindow.ui \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/uic
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/uic src/mainwindow.ui -o ui_mainwindow.h

ui_querywidget.h: src/widgets/querywidget.ui \
		../../../Qt5.9.2/5.9.2/gcc_64/bin/uic
	/home/<USER>/Qt5.9.2/5.9.2/gcc_64/bin/uic src/widgets/querywidget.ui -o ui_querywidget.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.o: src/main.cpp src/mainwindow.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMainWindow \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmainwindow.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QToolBar \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtoolbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qaction.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qactiongroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QAction \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QSplitter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsplitter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/models/station/stationmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/widgets/soehistorywidget.h \
		src/widgets/querywidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		src/common/enums.h \
		src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		src/widgets/providers/telemetrydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		src/common/datastructures.h \
		src/models/telemetrydatamodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreeview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidgetitemiterator.h \
		src/widgets/historytreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidgetItem \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QtSql \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QtSqlDepends \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QtCore \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QtCoreDepends \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstracteventdispatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeventloop.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractnativeeventfilter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstracttransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydataops.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydatapointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbitarray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbuffer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraymatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcollator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcommandlineoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcommandlineparser.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreapplication.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcryptographichash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qelapsedtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdiriterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeasingcurve.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qendian.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeventtransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qexception.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfactoryinterface.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileselector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfilesystemwatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfinalstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuture.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfutureinterface.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrunnable.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qresultstore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuturesynchronizer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuturewatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhistorystate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qidentityproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qisenum.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonarray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonvalue.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsondocument.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlibrary.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlibraryinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversionnumber.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlinkedlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlockfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qloggingcategory.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetaobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimedata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimedatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimetype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectcleanuphandler.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qoperatingsystemversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qparallelanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpauseanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qplugin.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpluginloader.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocess.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpropertyanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariantanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qqueue.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qreadwritelock.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qresource.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsavefile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedvaluerollback.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsemaphore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsequentialanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsettings.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedmemory.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsignalmapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsignaltransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsocketnotifier.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsortfilterproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstack.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstandardpaths.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstatemachine.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstorageinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlistmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemsemaphore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtemporarydir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QScopedPointer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtemporaryfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextboundaryfinder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextcodec.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthread.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthreadpool.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthreadstorage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimeline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimezone.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtranslator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypetraits.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/quuid.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qwaitcondition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qwineventnotifier.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qxmlstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcoreversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldriver.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldriverplugin.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlfield.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlindex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrelationaldelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlistview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qcombobox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrelationaltablemodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqltablemodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlresult.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTableView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtableview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QStandardItemModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qstandarditemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QApplication \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qapplication.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qguiapplication.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qinputmethod.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTextCodec \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTranslator \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QLibraryInfo \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QSettings \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QLoggingCategory \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QFile \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTextStream \
		src/utils/xmlconfigreader.h \
		src/utils/devicenamemapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDir \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMessageBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmessagebox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QCommandLineParser \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QCommandLineOption \
		src/common/logger.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o src/main.cpp

mainwindow.o: src/mainwindow.cpp src/mainwindow.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMainWindow \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmainwindow.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QToolBar \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtoolbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qaction.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qactiongroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QAction \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QSplitter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsplitter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/models/station/stationmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/widgets/soehistorywidget.h \
		src/widgets/querywidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		src/common/enums.h \
		src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		src/widgets/providers/telemetrydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		src/common/datastructures.h \
		src/models/telemetrydatamodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreeview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidgetitemiterator.h \
		src/widgets/historytreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidgetItem \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QtSql \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QtSqlDepends \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QtCore \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QtCoreDepends \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstracteventdispatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeventloop.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractnativeeventfilter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstracttransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydataops.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydatapointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbitarray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbuffer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraymatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcollator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcommandlineoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcommandlineparser.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreapplication.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcryptographichash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdeadlinetimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qelapsedtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdiriterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeasingcurve.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qendian.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qeventtransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qexception.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfactoryinterface.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileselector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfilesystemwatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfinalstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuture.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfutureinterface.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrunnable.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qresultstore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuturesynchronizer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfuturewatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhistorystate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qidentityproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qisenum.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonarray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonvalue.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsondocument.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qjsonobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlibrary.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlibraryinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversionnumber.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlinkedlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlockfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qloggingcategory.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmessageauthenticationcode.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetaobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimedata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimedatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmimetype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectcleanuphandler.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qoperatingsystemversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qparallelanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpauseanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qplugin.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpluginloader.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocess.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpropertyanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariantanimation.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qqueue.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qreadwritelock.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qresource.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsavefile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedvaluerollback.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsemaphore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsequentialanimationgroup.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsettings.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedmemory.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsignalmapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsignaltransition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsocketnotifier.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsortfilterproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstack.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstandardpaths.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstatemachine.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstorageinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlistmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemsemaphore.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtemporarydir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QScopedPointer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtemporaryfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextboundaryfinder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextcodec.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthread.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthreadpool.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qthreadstorage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimeline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimezone.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtranslator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypetraits.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/quuid.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qwaitcondition.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qwineventnotifier.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qxmlstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcoreversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldriver.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldriverplugin.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlfield.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlindex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrelationaldelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlistview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qcombobox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrelationaltablemodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqltablemodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlresult.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlversion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTableView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtableview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QStandardItemModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qstandarditemmodel.h \
		ui_mainwindow.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QVBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qboxlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qgridlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QFileDialog \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QFile \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTextStream \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QRadioButton \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qradiobutton.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractbutton.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLabel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlabel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QDialogButtonBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdialogbuttonbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDir \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QFormLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qformlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLineEdit \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlineedit.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpen.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QHBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QPushButton \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qpushbutton.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QResizeEvent \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QDesktopWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdesktopwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QApplication \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qapplication.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qguiapplication.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qinputmethod.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrintDialog \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprintdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qabstractprintdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMessageBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmessagebox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QSettings \
		src/utils/xmlconfigreader.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.o src/mainwindow.cpp

dbmanager.o: src/database/dbmanager.cpp src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o dbmanager.o src/database/dbmanager.cpp

dbconnectionfactory.o: src/database/dbconnectionfactory.cpp src/database/dbconnectionfactory.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		src/utils/xmlconfigreader.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o dbconnectionfactory.o src/database/dbconnectionfactory.cpp

stationmodel.o: src/models/station/stationmodel.cpp src/models/station/stationmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/utils/typemappingmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QSet \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlRecord \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o stationmodel.o src/models/station/stationmodel.cpp

customquerymodel.o: src/models/customquerymodel.cpp src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		src/utils/devicenamemapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		src/utils/typemappingmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlField \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlfield.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlRecord \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o customquerymodel.o src/models/customquerymodel.cpp

telemetrydatamodel.o: src/models/telemetrydatamodel.cpp src/models/telemetrydatamodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		src/common/datastructures.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDataStream \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o telemetrydatamodel.o src/models/telemetrydatamodel.cpp

historytreewidget.o: src/widgets/historytreewidget.cpp src/widgets/historytreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreeview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidgetitemiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidgetItem \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		src/common/enums.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QHeaderView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qheaderview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMessageBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmessagebox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDir \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileinfo.h \
		src/common/constants.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		src/utils/xmlconfigreader.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		src/database/dbconnectionfactory.h \
		src/utils/devicenamemapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o historytreewidget.o src/widgets/historytreewidget.cpp

querywidget.o: src/widgets/querywidget.cpp src/widgets/querywidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		ui_querywidget.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/utils/devicenamemapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMessageBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmessagebox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractItemModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QFile \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTextStream \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QPainter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpen.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrintDialog \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprintdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qabstractprintdialog.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o querywidget.o src/widgets/querywidget.cpp

soehistorywidget.o: src/widgets/soehistorywidget.cpp src/widgets/soehistorywidget.h \
		src/widgets/querywidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/models/customquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		src/common/enums.h \
		src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		src/widgets/providers/telemetrydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		src/common/datastructures.h \
		src/models/telemetrydatamodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreeview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtreewidgetitemiterator.h \
		ui_querywidget.h \
		src/widgets/filters/filtermanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QGridLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qgridlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qboxlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QHBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QVBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLabel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlabel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QComboBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qcombobox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLineEdit \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlineedit.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpen.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QDateTimeEdit \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdatetimeedit.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QFrame \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QSpacerItem \
		src/widgets/tables/tablemanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTableView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtableview.h \
		src/models/station/stationmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/common/logger.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMessageBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmessagebox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QFileDialog \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qfiledialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdir.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfileinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDir \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlRecord \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h \
		src/widgets/historytreewidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTreeWidgetItem
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o soehistorywidget.o src/widgets/soehistorywidget.cpp

filtermanager.o: src/widgets/filters/filtermanager.cpp src/widgets/filters/filtermanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QWidget \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QGridLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qgridlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlayoutitem.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qboxlayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QHBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QVBoxLayout \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLabel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlabel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QComboBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qcombobox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QLineEdit \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qlineedit.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpen.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QDateTimeEdit \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdatetimeedit.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QFrame \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QSpacerItem \
		src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		src/common/enums.h \
		src/models/station/stationmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/utils/typemappingmanager.h \
		src/utils/devicenamemapper.h \
		src/common/logger.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o filtermanager.o src/widgets/filters/filtermanager.cpp

historydataprovider.o: src/widgets/providers/historydataprovider.cpp src/widgets/providers/historydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		src/common/enums.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/models/station/stationmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		src/common/constants.h \
		src/common/logger.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QRegExp
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o historydataprovider.o src/widgets/providers/historydataprovider.cpp

telemetrydataprovider.o: src/widgets/providers/telemetrydataprovider.cpp src/widgets/providers/telemetrydataprovider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVariant \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QVector \
		src/common/enums.h \
		src/common/datastructures.h \
		src/database/dbmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTimer \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtimer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasictimer.h \
		src/common/constants.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		src/models/telemetrydatamodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QAbstractTableModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		src/utils/xmlconfigreader.h \
		src/database/dbconnectionfactory.h \
		src/common/tablenamemanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDate \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlRecord \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QSet \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMapIterator \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QSortFilterProxyModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsortfilterproxymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractproxymodel.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o telemetrydataprovider.o src/widgets/providers/telemetrydataprovider.cpp

tablemanager.o: src/widgets/tables/tablemanager.cpp src/widgets/tables/tablemanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QTableView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtableview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgetsglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtguiglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtgui-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtwidgets-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractscrollarea.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qframe.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qwindowdefs_win.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmargins.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrect.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpalette.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcolor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgb.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qrgba64.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qbrush.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qmatrix.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpolygon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qregion.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatastream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qline.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtransform.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainterpath.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qimage.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixelformat.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpixmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfont.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontmetrics.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qfontinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qsizepolicy.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qcursor.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qkeysequence.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qurlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvector2d.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtouchdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qabstractitemmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qitemselectionmodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractitemdelegate.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyleoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractspinbox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qvalidator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregularexpression.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qicon.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qabstractslider.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qstyle.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabbar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qtabwidget.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qrubberband.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQueryModel \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquerymodel.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QHeaderView \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qheaderview.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QFile \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QTextStream \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/QMessageBox \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qmessagebox.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtWidgets/qdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrinter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprinter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupportglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qtprintsupport-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagedpaintdevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagelayout.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpagesize.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/QPrintDialog \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qprintdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtPrintSupport/qabstractprintdialog.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/QPainter \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpainter.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qtextoption.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtGui/qpen.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tablemanager.o src/widgets/tables/tablemanager.cpp

tablestructuremanager.o: src/utils/db/tablestructuremanager.cpp src/utils/db/tablestructuremanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QStringList \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlRecord \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlrecord.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tablestructuremanager.o src/utils/db/tablestructuremanager.cpp

xmlconfigreader.o: src/utils/xmlconfigreader.cpp src/utils/xmlconfigreader.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QFile \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfile.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qfiledevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtXml/QDomDocument \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtXml/qdom.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtXml/qtxmlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtXml/qtxml-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o xmlconfigreader.o src/utils/xmlconfigreader.cpp

devicenamemapper.o: src/utils/devicenamemapper.cpp src/utils/devicenamemapper.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlDatabase \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqldatabase.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qtsqlglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlQuery \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlquery.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/QSqlError \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtSql/qsqlerror.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		src/utils/xmlconfigreader.h \
		src/database/dbconnectionfactory.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o devicenamemapper.o src/utils/devicenamemapper.cpp

typemappingmanager.o: src/utils/typemappingmanager.cpp src/utils/typemappingmanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QObject \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QMap \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o typemappingmanager.o src/utils/typemappingmanager.cpp

logger.o: src/common/logger.cpp src/common/logger.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDebug \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdebug.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtextstream.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiodevice.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobjectdefs_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcoreevent.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qscopedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmetatype.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvarlengtharray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontainerfwd.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qobject_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlocale.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvariant.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qvector.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpoint.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qset.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcontiguouscache.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsharedpointer_impl.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDateTime \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o logger.o src/common/logger.cpp

tablenamemanager.o: src/common/tablenamemanager.cpp src/common/tablenamemanager.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QString \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstring.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qchar.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobal.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig-bootstrapped.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qconfig.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtcore-config.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsystemdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qprocessordetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qcompilerdetection.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qtypeinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qsysinfo.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlogging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qflags.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbasicatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_bootstrap.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qgenericatomic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_cxx11.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qatomic_msvc.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qglobalstatic.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qmutex.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnumeric.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qversiontagging.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearray.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qrefcount.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qnamespace.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qarraydata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringbuilder.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/QDate \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qdatetime.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qshareddata.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhash.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qiterator.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qalgorithms.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qhashfunctions.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qpair.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qbytearraylist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringlist.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qregexp.h \
		../../../Qt5.9.2/5.9.2/gcc_64/include/QtCore/qstringmatcher.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o tablenamemanager.o src/common/tablenamemanager.cpp

moc_mainwindow.o: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.o moc_mainwindow.cpp

moc_dbmanager.o: moc_dbmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_dbmanager.o moc_dbmanager.cpp

moc_stationmodel.o: moc_stationmodel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_stationmodel.o moc_stationmodel.cpp

moc_customquerymodel.o: moc_customquerymodel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_customquerymodel.o moc_customquerymodel.cpp

moc_telemetrydatamodel.o: moc_telemetrydatamodel.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_telemetrydatamodel.o moc_telemetrydatamodel.cpp

moc_historytreewidget.o: moc_historytreewidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_historytreewidget.o moc_historytreewidget.cpp

moc_querywidget.o: moc_querywidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_querywidget.o moc_querywidget.cpp

moc_soehistorywidget.o: moc_soehistorywidget.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_soehistorywidget.o moc_soehistorywidget.cpp

moc_filtermanager.o: moc_filtermanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_filtermanager.o moc_filtermanager.cpp

moc_historydataprovider.o: moc_historydataprovider.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_historydataprovider.o moc_historydataprovider.cpp

moc_telemetrydataprovider.o: moc_telemetrydataprovider.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_telemetrydataprovider.o moc_telemetrydataprovider.cpp

moc_tablemanager.o: moc_tablemanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_tablemanager.o moc_tablemanager.cpp

moc_tablestructuremanager.o: moc_tablestructuremanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_tablestructuremanager.o moc_tablestructuremanager.cpp

moc_devicenamemapper.o: moc_devicenamemapper.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_devicenamemapper.o moc_devicenamemapper.cpp

moc_typemappingmanager.o: moc_typemappingmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_typemappingmanager.o moc_typemappingmanager.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

