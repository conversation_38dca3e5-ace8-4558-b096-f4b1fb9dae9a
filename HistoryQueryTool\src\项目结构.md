# HistoryQueryTool 项目结构

HistoryQueryTool是一个用于历史数据查询的工具，主要用于查询SOE历史数据、遥信变位、越限告警等历史数据。

## 项目整体架构

项目采用模块化设计，主要分为以下几个部分：

1. **数据访问层**：负责数据库连接和操作
2. **数据模型层**：定义数据结构和数据交互模型
3. **界面层**：用户交互界面
4. **工具与公共组件**：通用功能和辅助工具

## 目录结构

```
HistoryQueryTool/
├── src/                        # 源代码目录
│   ├── database/               # 数据库访问层
│   │   ├── dbmanager.h         # 数据库管理器接口
│   │   └── dbmanager.cpp       # 数据库管理器实现
│   ├── models/                 # 数据模型层
│   │   ├── customquerymodel.h  # 自定义查询模型
│   │   ├── customquerymodel.cpp
│   │   ├── telemetrydatamodel.h  # 遥测数据模型
│   │   ├── telemetrydatamodel.cpp # 遥测数据模型实现
│   │   └── station/            # 站点模型子目录
│   │       ├── stationmodel.h  # 站点模型接口
│   │       └── stationmodel.cpp # 站点模型实现
│   ├── widgets/                # 界面组件
│   │   ├── filters/            # 过滤器组件
│   │   │   └── filtermanager.* # 过滤器管理器 
│   │   ├── providers/          # 数据提供者
│   │   │   ├── historydataprovider.* # 历史数据提供器
│   │   │   └── telemetrydataprovider.* # 遥测数据提供器
│   │   ├── tables/             # 表格组件
│   │   ├── querywidget.*       # 查询界面组件
│   │   ├── soehistorywidget.*  # SOE历史查询界面
│   │   └── historytreewidget.* # 历史树形控件
│   ├── utils/                  # 工具类
│   │   ├── db/                 # 数据库相关工具
│   │   ├── export/             # 导出功能
│   │   ├── devicenamemapper.*  # 设备名称映射器
│   │   ├── typemappingmanager.*# 类型映射管理器
│   │   └── xmlconfigreader.*   # XML配置读取器
│   ├── common/                 # 公共组件
│   │   ├── constants.h         # 常量定义
│   │   └── enums.h             # 枚举定义
│   ├── config/                 # 配置相关
│   ├── resources/              # 资源文件
│   ├── interfaces/             # 接口定义
│   ├── main.cpp                # 程序入口
│   ├── mainwindow.*            # 主窗口
│   └── CLASSES.md              # 类结构文档
└── HistoryQueryTool.pro        # 项目文件
```

## 核心类说明

### 1. 数据访问层

#### DBManager
- **功能**：数据库连接管理、SQL查询执行
- **实现**：单例模式，提供数据库连接、查询执行等功能

### 2. 数据模型层

#### StationModel
- **功能**：管理站点、设备和事件类型数据
- **实现**：单例模式，提供数据缓存和查询功能

#### CustomQueryModel
- **功能**：自定义查询模型，用于表格数据显示
- **实现**：继承自QSqlQueryModel，提供数据格式化和映射功能

#### TelemetryDataModel
- **功能**：遥测数据模型，专门处理和解析遥测历史数据 (包括分钟级和秒级的BLOB数据)
- **实现**：继承自QAbstractTableModel，提供数据解析、存储和显示接口。包含 `loadDataFromBlob` 方法用于解析数据库中的 `curvevalue` 字段。

### 3. 界面层

#### QueryWidget
- **功能**：通用查询界面
- **实现**：集成过滤器和结果显示组件

#### SOEHistoryWidget
- **功能**：SOE历史数据查询界面
- **实现**：特定于SOE数据的查询界面

#### FilterManager
- **功能**：管理各种过滤条件组件
- **实现**：支持多种类型的筛选器（标准过滤器、系统事件过滤器、用户登录过滤器）

### 4. 工具与公共组件

#### TypeMappingManager
- **功能**：管理所有类型字段(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)的映射关系
- **实现**：单例模式，提供类型值与显示名称之间的双向映射

#### DeviceNameMapper
- **功能**：设备ID与名称的映射
- **实现**：单例模式，提供设备ID到名称的映射

#### XMLConfigReader
- **功能**：读取XML配置文件
- **实现**：解析配置信息

## 数据流程

1. 用户在界面上设置查询条件
2. FilterManager组装过滤条件
3. HistoryDataProvider (或 TelemetryDataProvider) 根据条件从数据库获取数据 (对于遥测数据，`TelemetryDataProvider` 获取原始数据，包括 `curvevalue` BLOB)
4. CustomQueryModel (或 TelemetryDataModel) 处理结果数据并应用格式化 (对于遥测数据，`TelemetryDataModel` 使用 `loadDataFromBlob` 解析 `curvevalue`)
5. 界面组件展示查询结果

## 改进历史

### 类型映射重构
- 原问题：类型映射(VALUETYPE, BREAKLIMITTYPE等)在多处重复定义
- 解决方案：创建TypeMappingManager统一管理所有类型映射
- 优势：
  - 消除代码重复
  - 提高映射一致性
  - 简化维护工作

### 查询功能改进
- 支持不同表的字段特性
- 适应不同数据类型的显示需求
- 对越限告警等特殊类型数据的优化处理 