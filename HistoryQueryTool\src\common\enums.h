#ifndef ENUMS_H
#define ENUMS_H

/**
 * @brief 历史数据类型枚举
 */
enum HistoryDataType {
    SOE_EVENT = 0,       // SOE事件历史数据
    YX_CHANGE,           // 遥信变位历史数据
    LIMIT_ALARM,         // 越限告警历史数据
    SYSTEM_EVENT,        // 系统事件历史数据
    OPERATION_RECORD,    // 操作记录历史数据
    USER_LOGIN,          // 用户登录历史数据
    ANALOG_EVENT,        // 模拟量事件
    MINUTE_TELEMETRY,    // 分钟级遥测历史数据
    SECOND_TELEMETRY     // 秒级遥测历史数据
};

/**
 * @brief 分钟级时间间隔枚举
 */
enum MinuteIntervalType {
    INTERVAL_1MIN = 1,   // 1分钟
    INTERVAL_5MIN = 5,   // 5分钟
    INTERVAL_10MIN = 10, // 10分钟
    INTERVAL_15MIN = 15, // 15分钟
    INTERVAL_30MIN = 30, // 30分钟
    INTERVAL_60MIN = 60  // 60分钟
};

/**
 * @brief 秒级时间间隔枚举
 */
enum SecondIntervalType {
    INTERVAL_1SEC = 1,   // 1秒
    INTERVAL_5SEC = 5,   // 5秒
    INTERVAL_10SEC = 10, // 10秒
    INTERVAL_15SEC = 15, // 15秒
    INTERVAL_30SEC = 30, // 30秒
    INTERVAL_60SEC = 60  // 60秒
};

#endif // ENUMS_H 