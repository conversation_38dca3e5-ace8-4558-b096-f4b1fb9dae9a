#ifndef FILTERMANAGER_H
#define FILTERMANAGER_H

#include <QObject>
#include <QMap>
#include <QDateTime>
#include <QWidget>
#include <QGridLayout>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QComboBox>
#include <QLineEdit>
#include <QDateTimeEdit>
#include <QFrame>
#include <QSpacerItem>
#include <memory>
#include "../providers/historydataprovider.h"
#include "../../common/enums.h"

class StationModel;
class DBManager;

// 前向声明
struct Device;

/**
 * @brief 过滤器管理类 - 重构版本
 * 采用统一的布局管理，解决控件间距不一致和响应式显示问题
 */
class FilterManager : public QObject
{
    Q_OBJECT
public:
    explicit FilterManager(QObject *parent = nullptr);
    ~FilterManager();
    
    /**
     * @brief 设置父控件
     * @param parent 父控件
     */
    void setParentWidget(QWidget *parent);
    
    /**
     * @brief 设置数据库管理器
     * @param dbManager 数据库管理器
     */
    void setDBManager(DBManager *dbManager);
    
    /**
     * @brief 设置站点模型
     * @param stationModel 站点模型
     */
    void setStationModel(StationModel *stationModel);
    
    /**
     * @brief 设置数据提供者
     * @param provider 数据提供者
     */
    void setDataProvider(HistoryDataProvider *provider);
    
    /**
     * @brief 设置过滤器类型
     * @param type 历史数据类型
     */
    void setFilterType(HistoryDataType type);
    
    /**
     * @brief 获取过滤条件
     * @return 过滤条件键值对
     */
    QMap<QString, QString> getFilters();
    
    /**
     * @brief 获取开始时间
     * @return 开始时间
     */
    QDateTime getStartTime() const;
    
    /**
     * @brief 获取结束时间
     * @return 结束时间
     */
    QDateTime getEndTime() const;
    
    /**
     * @brief 设置时间范围
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void setTimeRange(const QDateTime &startTime, const QDateTime &endTime);
    
    /**
     * @brief 初始化所有过滤器控件
     * 在数据库连接后调用，加载过滤器下拉框中的数据
     */
    void initializeFilters();
    
    /**
     * @brief 获取选中的时间间隔（秒）
     * @return 时间间隔，单位为秒
     */
    int getInterval() const;
    
    /**
     * @brief 获取过滤器主要控件，用于添加到外部布局
     * @return 包含所有过滤控件的QWidget
     */
    QWidget* getFilterWidget();
    
    /**
     * @brief 设置控件的可见性
     * @param visible 是否可见
     */
    void setVisible(bool visible);
    
    void loadControlData();
    void setCurrentDevice(int deviceId);
    void setCurrentPoint(int deviceIndex);

private slots:
    void onStationChanged(int index);
    void onBayChanged(int index);
    
private:
    /**
     * @brief 创建统一的过滤器UI
     */
    void createFilterUI();
    
    /**
     * @brief 创建标准控件行（时间选择行）
     */
    void createTimeRow();
    
    /**
     * @brief 创建第一行过滤控件
     */
    void createFirstFilterRow();
    
    /**
     * @brief 创建第二行过滤控件
     */
    void createSecondFilterRow();
    
    /**
     * @brief 创建第三行过滤控件（遥测专用）
     */
    void createThirdFilterRow();
    
    /**
     * @brief 更新控件的显示状态
     */
    void updateControlsVisibility();
    
    /**
     * @brief 创建标准化的标签
     * @param text 标签文本
     * @param parent 父控件
     * @return 创建的标签
     */
    QLabel* createStandardLabel(const QString &text, QWidget *parent);
    
    /**
     * @brief 创建标准化的下拉框
     * @param parent 父控件
     * @return 创建的下拉框
     */
    QComboBox* createStandardComboBox(QWidget *parent);
    
    /**
     * @brief 创建标准化的文本框
     * @param parent 父控件
     * @return 创建的文本框
     */
    QLineEdit* createStandardLineEdit(QWidget *parent);
    
    /**
     * @brief 创建标准化的日期时间选择器
     * @param parent 父控件
     * @return 创建的日期时间选择器
     */
    QDateTimeEdit* createStandardDateTimeEdit(QWidget *parent);
    
    /**
     * @brief 设置标准的控件样式
     */
    void applyStandardStyles();
    
    // 数据成员
    QWidget *m_parentWidget;
    QWidget *m_filterWidget;  // 主过滤器容器
    QVBoxLayout *m_mainLayout; // 主布局
    
    DBManager *m_dbManager;
    StationModel *m_stationModel;
    HistoryDataProvider *m_dataProvider;
    HistoryDataType m_currentType;
    QString m_tableName;
    
    // 时间控件（第0行）
    QHBoxLayout *m_timeLayout;
    QLabel *m_startTimeLabel;
    QDateTimeEdit *m_startDateTimeEdit;
    QLabel *m_endTimeLabel;
    QDateTimeEdit *m_endDateTimeEdit;
    
    // 第一行过滤控件
    QHBoxLayout *m_firstRowLayout;
    QLabel *m_stationLabel;
    QComboBox *m_stationComboBox;
    QLabel *m_typeLabel;        // 通用类型标签（事件类型/日志类型/错误来源等）
    QComboBox *m_typeComboBox;  // 通用类型下拉框
    
    // 第二行过滤控件
    QHBoxLayout *m_secondRowLayout;
    QLabel *m_deviceLabel;
    QComboBox *m_deviceComboBox;
    QLabel *m_signalLabel;
    QLineEdit *m_signalEdit;
    
    // 第三行过滤控件（遥测专用）
    QHBoxLayout *m_thirdRowLayout;
    QLabel *m_intervalLabel;
    QComboBox *m_intervalComboBox;
    
    // 分隔线
    QFrame *m_separatorLine;
    
    bool m_initialized; // 标记筛选器是否已初始化
    
    // 样式常量
    static const int STANDARD_CONTROL_HEIGHT = 28;
    static const int STANDARD_SPACING = 12;
    static const int STANDARD_MARGIN = 8;
    static const int LABEL_MIN_WIDTH = 80;
    static const int CONTROL_MIN_WIDTH = 150;
};

#endif // FILTERMANAGER_H 