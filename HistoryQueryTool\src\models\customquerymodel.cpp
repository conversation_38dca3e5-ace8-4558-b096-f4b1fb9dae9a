#include "customquerymodel.h"
#include "../utils/devicenamemapper.h"
#include "../utils/typemappingmanager.h"
#include <QDebug>
#include <QSqlField>
#include <QSqlRecord>

CustomQueryModel::CustomQueryModel(QObject *parent)
    : QSqlQueryModel(parent),
      m_deviceIdMappingEnabled(true)
{
}

QVariant CustomQueryModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || role != Qt::DisplayRole) {
        return QSqlQueryModel::data(index, role);
    }

    // 获取字段名，并转换为大写进行比较
    QString fieldName = headerData(index.column(), Qt::Horizontal).toString();
    
    // 获取原始值
    QVariant value = QSqlQueryModel::data(index, role);
    
    // 获取类型映射管理器
    TypeMappingManager *typeMappingManager = TypeMappingManager::instance();
    
    // 使用TypeMappingManager处理所有类型字段的映射
    if (typeMappingManager->isMappedTypeField(fieldName)) {
        QString originalValue = value.toString();
        QString mappedValue = typeMappingManager->getNameByValue(fieldName, originalValue);
        
        // 添加调试输出
        qDebug() << "类型映射:" << fieldName << "原值:" << originalValue << "映射后:" << mappedValue;
        
        return mappedValue;
    }

    // 处理equipid字段的映射 - 严格匹配equipid (不区分大小写)
    if (m_deviceIdMappingEnabled && (fieldName.toLower() == "equipid" || 
        fieldName.toUpper() == "EQUIPID" || fieldName == "设备ID")) {
        
        // 检查映射器状态
        DeviceNameMapper *mapper = DeviceNameMapper::instance();
        if (!mapper) {
            return value; // 返回原始值
        }
        
        // 尝试将值转换为整数
        bool ok;
        int deviceId = value.toInt(&ok);
        
        // 如果转换成功，执行映射
        if (ok) {
            // 获取设备名称
            QString deviceName = mapper->getDeviceNameById(deviceId);
            
            // 如果设备名称不为空且不等于设备ID，返回名称+ID格式
            if (!deviceName.isEmpty() && deviceName != QString::number(deviceId)) {
                QString mappedName = QString("%1 [%2]").arg(deviceName).arg(deviceId);
                return mappedName;
            }
        }
    }

    // 处理日期时间字段
    if (isDateTimeField(fieldName)) {
        // 获取字符串值
        QString strValue = value.toString().trimmed();
        
        // 对于MySQL中确定是datetime类型的字段，直接确保显示秒
        if (fieldName.toUpper() == "STARTTIME" || 
            fieldName.toUpper() == "ENDTIME" || 
            fieldName.toUpper() == "TIMEIN") {
            
            // 如果已经包含秒（有两个冒号），直接返回
            if (strValue.count(":") == 2) {
                return strValue;
            }
            
            // 如果只有一个冒号（小时:分钟），添加秒
            if (strValue.count(":") == 1) {
                QDateTime dt = QDateTime::fromString(strValue, "yyyy-MM-dd HH:mm");
                if (dt.isValid()) {
                    return dt.toString("yyyy-MM-dd HH:mm:ss");
                }
            }
        }
        
        // 如果是其他时间字段或上面处理失败，使用常规处理
        
        // 如果是QDateTime类型直接格式化
        if (value.type() == QVariant::DateTime) {
            QDateTime dt = value.toDateTime();
            if (dt.isValid()) {
                return dt.toString("yyyy-MM-dd HH:mm:ss");
            }
        }
        
        // 直接使用标准格式检测
        if (strValue.length() >= 19 && strValue.count(":") == 2) {
            return strValue;  // 已包含秒的完整格式，直接返回
        }
        
        // 其他情况，尝试解析格式并添加秒
        QDateTime dt;
        
        // 判断格式
        if (strValue.count(":") == 1) {
            dt = QDateTime::fromString(strValue, "yyyy-MM-dd HH:mm");
        } else {
            dt = QDateTime::fromString(strValue, "yyyy-MM-dd HH:mm:ss");
        }
        
        if (dt.isValid()) {
            return dt.toString("yyyy-MM-dd HH:mm:ss");
        }
    }

    return value;
}

bool CustomQueryModel::isDateTimeField(const QString &fieldName) const
{
    // 转为大写进行比较
    QString upperField = fieldName.toUpper();
    
    // 直接匹配确定的datetime字段
    if (upperField == "STARTTIME" || upperField == "ENDTIME" || upperField == "TIMEIN") {
        return true;
    }
    
    // 通过关键词进行匹配
    static const QStringList timeKeywords = {"TIME", "DATE", "CREATETIME", "UPDATETIME"};
    
    for (const QString &keyword : timeKeywords) {
        if (upperField.contains(keyword)) {
            return true;
        }
    }
    
    return false;
}

bool CustomQueryModel::isDeviceIdField(const QString &fieldName) const
{
    // 转为小写进行比较
    QString lowerField = fieldName.toLower();
    
    // 移除可能的表前缀（如 table.field 格式）
    if (lowerField.contains(".")) {
        lowerField = lowerField.split(".").last();
    }
    
    // 匹配equipid字段，不区分大小写
    if (lowerField == "equipid" || fieldName.toUpper() == "EQUIPID" || fieldName == "设备ID") {
        return true;
    }
    
    return false;
}

QString CustomQueryModel::mapDeviceIdToName(const QVariant &deviceId) const
{
    // 如果值为0或空，直接返回原值
    if (deviceId.isNull() || deviceId.toInt() == 0) {
        return deviceId.toString();
    }
    
    DeviceNameMapper *mapper = DeviceNameMapper::instance();
    if (mapper && mapper->isInitialized()) {
        int id = deviceId.toInt();
        QString deviceName = mapper->getDeviceNameById(id);
        
        // 如果设备名称为空或等于ID本身，只返回ID
        if (deviceName.isEmpty() || deviceName == QString::number(id)) {
            return QString("未知设备 [%1]").arg(id); // 返回"未知设备 [ID]"格式
        }
        
        // 如果成功获取到设备名称，返回"设备名称[ID]"的格式
        QString result = QString("%1 [%2]").arg(deviceName).arg(id);
        return result;
    }
    
    return QString("ID:%1").arg(deviceId.toString()); // 返回"ID:xxx"格式
}

void CustomQueryModel::setDeviceIdMappingEnabled(bool enable)
{
    m_deviceIdMappingEnabled = enable;
}

// 添加新方法，在设置查询后分析查询结果
void CustomQueryModel::setQuery(const QSqlQuery &query)
{
    // 调用父类的setQuery方法
    QSqlQueryModel::setQuery(query);
    
    // 强制刷新设备名称映射表（减少日志输出）
    DeviceNameMapper *mapper = DeviceNameMapper::instance();
    if (mapper) {
        mapper->refreshCache();
    }
    
    // 检查和输出表头信息
    if (rowCount() > 0) {
        bool hasEquipidField = false;
        
        // 检查每一列
        for (int col = 0; col < columnCount(); ++col) {
            QString fieldName = headerData(col, Qt::Horizontal).toString();
            
            // 检查是否为equipid字段 (大小写不敏感)
            if (fieldName.toLower() == "equipid" || fieldName.toUpper() == "EQUIPID" || fieldName == "设备ID") {
                hasEquipidField = true;
                
                // 确保映射功能已启用
                if (!m_deviceIdMappingEnabled) {
                    const_cast<CustomQueryModel*>(this)->setDeviceIdMappingEnabled(true);
                }
            }
        }
    }
}