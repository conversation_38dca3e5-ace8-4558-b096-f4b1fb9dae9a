#!/bin/bash

# 简化版SFTP下载脚本
# 基于测试验证的可行方法

# 配置信息
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="admin"
REMOTE_PASS="admin"

# 目录映射
declare -A DIR_MAPPINGS=(
    ["/home/<USER>/zhuanJiKong-E<PERSON>an/"]="/home/<USER>/qsjglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian2/"]="/home/<USER>/djglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian3/"]="/home/<USER>/jcglyc/"
)

# 文件类型
FILE_EXTENSIONS=("DQ.WPD" "QXZ.WPD" "NBQ.WPD" "THEROY.WPD" "CDQ.WPD" "NWP.WPD")

# 日志配置
DOWNLOAD_LOG_DIR="/home/<USER>/sftplog"
DOWNLOAD_LOG_FILE="$DOWNLOAD_LOG_DIR/download_history.log"

# 日志函数
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message"
}

# 记录下载日志
log_download() {
    local remote_path="$1"
    local local_path="$2"
    local filename="$3"
    local filesize="$4"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    mkdir -p "$DOWNLOAD_LOG_DIR"
    echo "$timestamp|$remote_path|$local_path|$filename|$filesize" >> "$DOWNLOAD_LOG_FILE"
}

# 检查文件是否已下载
is_file_downloaded() {
    local remote_dir="$1"
    local filename="$2"
    
    if [ -f "$DOWNLOAD_LOG_FILE" ]; then
        grep -q "|$remote_dir|.*|$filename|" "$DOWNLOAD_LOG_FILE"
        return $?
    fi
    return 1
}

# 获取目录文件列表
get_files_in_directory() {
    local remote_dir="$1"
    local temp_file="/tmp/file_list_$(date +%s).txt"
    
    log "INFO" "获取目录文件列表: $remote_dir"
    
    if sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "ls -la '$remote_dir'" > "$temp_file" 2>/dev/null; then
        echo "$temp_file"
        return 0
    else
        rm -f "$temp_file"
        return 1
    fi
}

# 查找最新的指定类型文件
find_latest_files() {
    local file_list="$1"
    local remote_dir="$2"
    local found_files=()
    
    for ext in "${FILE_EXTENSIONS[@]}"; do
        local latest_file=""
        local latest_time=""
        
        # 查找该类型的文件
        while read -r line; do
            # 跳过目录行和特殊行
            if [[ "$line" =~ ^d ]] || [[ "$line" =~ ^total ]] || [[ "$line" =~ ^\. ]]; then
                continue
            fi
            
            # 提取文件名
            local filename=$(echo "$line" | awk '{print $NF}')
            
            # 检查是否是目标文件类型
            if [[ "$filename" == *"$ext" ]]; then
                # 检查是否已下载
                if ! is_file_downloaded "$remote_dir" "$filename"; then
                    # 提取时间信息进行比较
                    local file_time=$(echo "$line" | awk '{print $6" "$7" "$8}')
                    
                    if [ -z "$latest_file" ] || [[ "$file_time" > "$latest_time" ]]; then
                        latest_file="$filename"
                        latest_time="$file_time"
                    fi
                fi
            fi
        done < "$file_list"
        
        if [ -n "$latest_file" ]; then
            found_files+=("$latest_file")
            log "INFO" "发现新文件: $latest_file (类型: $ext)"
        fi
    done
    
    printf '%s\n' "${found_files[@]}"
}

# 下载文件
download_file() {
    local remote_dir="$1"
    local local_dir="$2"
    local filename="$3"
    
    # 确保本地目录存在
    mkdir -p "$local_dir"
    
    local remote_file="$remote_dir/$filename"
    local local_file="$local_dir/$filename"
    
    log "INFO" "下载文件: $filename"
    
    # 使用SCP下载
    if sshpass -p "$REMOTE_PASS" scp -P "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST:$remote_file" "$local_file" 2>/dev/null; then
        if [ -f "$local_file" ]; then
            local filesize=$(stat -c%s "$local_file" 2>/dev/null || echo "0")
            log "INFO" "下载成功: $filename ($filesize bytes)"
            log_download "$remote_dir" "$local_dir" "$filename" "$filesize"
            return 0
        fi
    fi
    
    log "ERROR" "下载失败: $filename"
    return 1
}

# 处理单个目录
process_directory() {
    local remote_dir="$1"
    local local_dir="$2"
    
    log "INFO" "处理目录: $remote_dir -> $local_dir"
    
    # 获取文件列表
    local file_list=$(get_files_in_directory "$remote_dir")
    if [ $? -ne 0 ]; then
        log "ERROR" "无法获取目录文件列表: $remote_dir"
        return 1
    fi
    
    # 查找需要下载的文件
    local files_to_download=$(find_latest_files "$file_list" "$remote_dir")
    rm -f "$file_list"
    
    if [ -z "$files_to_download" ]; then
        log "INFO" "目录 $remote_dir 没有新文件需要下载"
        return 0
    fi
    
    # 下载文件
    local download_count=0
    while IFS= read -r filename; do
        if [ -n "$filename" ]; then
            if download_file "$remote_dir" "$local_dir" "$filename"; then
                ((download_count++))
            fi
        fi
    done <<< "$files_to_download"
    
    log "INFO" "目录处理完成: $remote_dir (下载了 $download_count 个文件)"
    return 0
}

# 主函数
main() {
    log "INFO" "========== 开始SFTP下载任务 =========="
    
    # 测试连接
    if ! sshpass -p "$REMOTE_PASS" ssh -p "$REMOTE_PORT" -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo '连接测试'" >/dev/null 2>&1; then
        log "ERROR" "连接测试失败"
        exit 1
    fi
    log "INFO" "连接测试成功"
    
    # 处理每个目录
    local success_count=0
    local total_count=${#DIR_MAPPINGS[@]}
    
    for remote_dir in "${!DIR_MAPPINGS[@]}"; do
        local local_dir="${DIR_MAPPINGS[$remote_dir]}"
        if process_directory "$remote_dir" "$local_dir"; then
            ((success_count++))
        fi
    done
    
    log "INFO" "下载任务完成: $success_count/$total_count 个目录处理成功"
    
    if [ $success_count -eq $total_count ]; then
        log "INFO" "========== 下载任务成功 =========="
        exit 0
    else
        log "ERROR" "========== 下载任务部分失败 =========="
        exit 1
    fi
}

# 执行主函数
main "$@"
