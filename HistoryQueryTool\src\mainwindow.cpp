#include "mainwindow.h"
#include "ui_mainwindow.h"
#include "widgets/soehistorywidget.h"
#include "common/enums.h"
#include <QVBoxLayout>
#include <QFileDialog>
#include <QFile>
#include <QTextStream>
#include <QDateTime>
#include <QRadioButton>
#include <QLabel>
#include <QDialogButtonBox>
#include <QDir>
#include <QFormLayout>
#include <QLineEdit>
#include <QHBoxLayout>
#include <QPushButton>
#include <QResizeEvent>
#include <QDesktopWidget>
#include <QApplication>

#if defined(QT_PRINTSUPPORT_LIB)
#include <QPrintDialog>
#include <QPrinter>
#endif

#include <QMessageBox>
#include <QSettings>
#include <QDebug>
#include "utils/xmlconfigreader.h"

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow),
    dbManager(DBManager::instance()),
    stationModel(StationModel::instance())
{
    ui->setupUi(this);
    setupUI();
    createActions();
    createToolBar();
    
    // 清除站点模型的缓存，确保事件类型能正确加载
    if (stationModel) {
        stationModel->clearCache();
        
        // 主动预加载SOE事件表数据（如果数据库已连接）
        if (dbManager && dbManager->isConnected()) {
            stationModel->preloadCache("htsoelog");
        }
    }
    
    // 如果数据库已连接，初始化筛选框数据
    if (dbManager && dbManager->isConnected() && soeHistoryWidget) {
        // 主动初始化筛选器
        soeHistoryWidget->initializeFilters();
        
        // 加载遥测参数数据
        if (historyTreeWidget) {
            historyTreeWidget->loadTelemetryParameters();
        }
    }
    
    // 连接数据库状态变化信号，当数据库连接成功时自动初始化组件
    if (dbManager) {
        connect(dbManager, &DBManager::connectionStatusChanged, this, [this](bool connected) {
            if (connected) {
                qDebug() << "数据库连接状态变化：已连接，初始化相关组件";
                
                // 预加载站点数据
                if (stationModel) {
                    stationModel->clearCache();
                    stationModel->preloadCache("htsoelog");
                }
                
                // 初始化筛选器
                if (soeHistoryWidget) {
                    soeHistoryWidget->initializeFilters();
                }
                
                // 加载遥测参数数据
                if (historyTreeWidget) {
                    historyTreeWidget->loadTelemetryParameters();
                }
            } else {
                qDebug() << "数据库连接状态变化：已断开";
            }
        });
    }
    
    // 设置为SOE事件历史数据查询
    if (soeHistoryWidget) {
        soeHistoryWidget->setHistoryDataType(SOE_EVENT);
    }
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::setupUI()
{
    // 创建主分割器
    mainSplitter = new QSplitter(Qt::Horizontal, this);
    
    // 创建历史数据类型树控件
    historyTreeWidget = new HistoryTreeWidget(this);
    
    // 创建SOE历史查询窗口
    soeHistoryWidget = new SOEHistoryWidget(this);
    
    // 设置数据库管理器
    soeHistoryWidget->setDBManager(dbManager);
    
    // 设置站点模型
    soeHistoryWidget->setStationModel(stationModel);
    
    // 将控件添加到分割器
    mainSplitter->addWidget(historyTreeWidget);
    mainSplitter->addWidget(soeHistoryWidget);
    
    // 设置分割器比例
    mainSplitter->setStretchFactor(0, 2); // 左侧树形控件，比例由1改为2
    mainSplitter->setStretchFactor(1, 5); // 右侧内容区域，比例由4改为5
    
    // 设置分割器初始尺寸
    QList<int> sizes;
    sizes << 250 << 750; // 分别设置左侧和右侧的初始宽度
    mainSplitter->setSizes(sizes);
    
    // 将分割器设置为中央窗口
    setCentralWidget(mainSplitter);
    
    // 计算初始窗口大小的合理值（宽高比约为4:3）
    QSize screenSize = QApplication::desktop()->availableGeometry().size();
    int width = qMin(screenSize.width() - 100, 1200);
    int height = qMin(screenSize.height() - 100, 900);
    resize(width, height);
    
    // 设置窗口标题
    setWindowTitle("历史数据查询工具");
    
    // 连接历史树控件的选择信号
    connect(historyTreeWidget, &HistoryTreeWidget::historyTypeSelected, 
            this, &MainWindow::onHistoryTypeSelected);
    connect(historyTreeWidget, &HistoryTreeWidget::telemetryPointSelected,
            soeHistoryWidget, &SOEHistoryWidget::onTelemetryPointSelected);
}

void MainWindow::onHistoryTypeSelected(int type)
{
    if (soeHistoryWidget) {
        // 将int转换为HistoryDataType枚举
        HistoryDataType historyType = static_cast<HistoryDataType>(type);
        
        // 保存当前过滤器状态（如果切换到分钟级遥测查询）
        QDateTime currentStartTime, currentEndTime;
        int currentDeviceId = -1, currentDeviceIndex = -1;
        
        // 获取当前历史数据类型
        HistoryDataType currentHistoryType = soeHistoryWidget->getCurrentHistoryDataType();
        
        // 当从任何其他类型切换到分钟级遥测查询时，保留秒级遥测的过滤器状态
        if (historyType == MINUTE_TELEMETRY && currentHistoryType != MINUTE_TELEMETRY && currentHistoryType != SECOND_TELEMETRY) {
            // 保存秒级遥测的过滤器状态
            soeHistoryWidget->saveFilterState();
        }
        
        // 设置新的历史数据类型
        soeHistoryWidget->setHistoryDataType(historyType);
        
        // 如果从其他类型（不是分钟级和秒级遥测）切换到分钟级遥测查询，恢复秒级遥测的过滤器状态
        if (historyType == MINUTE_TELEMETRY && currentHistoryType != MINUTE_TELEMETRY && currentHistoryType != SECOND_TELEMETRY) {
            // 恢复保存的过滤器状态
            soeHistoryWidget->restoreFilterState();
        }
        
        // 更新窗口标题
        QString title;
        switch (historyType) {
            case SOE_EVENT:
                title = "SOE事件历史数据查询";
                break;
            case YX_CHANGE:
                title = "遥信变位历史数据查询";
                break;
            case LIMIT_ALARM:
                title = "越限告警历史数据查询";
                break;
            case SYSTEM_EVENT:
                title = "系统事件历史数据查询";
                break;
            case OPERATION_RECORD:
                title = "操作记录历史数据查询";
                break;
            case USER_LOGIN:
                title = "用户登录历史数据查询";
                break;
            case ANALOG_EVENT:
                title = "模拟量事件历史数据查询";
                break;
            case MINUTE_TELEMETRY:
                title = "分钟级遥测历史数据查询";
                break;
            case SECOND_TELEMETRY:
                title = "秒级遥测历史数据查询";
                break;
            default:
                title = "历史数据查询";
                break;
        }
        setWindowTitle(title);
        
        // 对于遥测数据类型，连接树控件的点击信号到SOE查询界面
        if (historyType == MINUTE_TELEMETRY || historyType == SECOND_TELEMETRY) {
            // 首先断开所有已有连接
            if (historyTreeWidget) {
                disconnect(historyTreeWidget, nullptr, soeHistoryWidget, nullptr);
                
                // 重新连接树节点点击信号
                connect(historyTreeWidget, SIGNAL(telemetryPointSelected(int, int, const QString&)),
                        soeHistoryWidget, SLOT(onTelemetryPointSelected(int, int, const QString&)));
            }
        }
    }
}

void MainWindow::createActions()
{
    connectAction = new QAction("连接数据库", this);
    connect(connectAction, &QAction::triggered, this, &MainWindow::onConnectDatabase);
    
    printAction = new QAction("打印", this);
    connect(printAction, &QAction::triggered, this, &MainWindow::onPrintResults);
    
    exportAction = new QAction("导出", this);
    connect(exportAction, &QAction::triggered, this, &MainWindow::onExportResults);
}

void MainWindow::createToolBar()
{
    mainToolBar = addToolBar("主工具栏");
    // 隐藏连接数据库按钮，程序启动时会自动连接
    // mainToolBar->addAction(connectAction);
    mainToolBar->addAction(printAction);
    mainToolBar->addAction(exportAction);
}

void MainWindow::onConnectDatabase()
{
    // 检查是否已经连接
    if (dbManager && dbManager->isConnected()) {
        QMessageBox::information(this, "数据库连接", 
                               "数据库已自动连接成功！\n"
                               "如需重新连接，请先断开当前连接。");
        return;
    }
    
    // 创建数据库连接对话框
    QDialog connectDialog(this);
    connectDialog.setWindowTitle("连接数据库");
    connectDialog.setMinimumWidth(350);
    
    QVBoxLayout *layout = new QVBoxLayout(&connectDialog);
    
    // 添加连接参数输入框
    QFormLayout *formLayout = new QFormLayout();
    
    // 从XML配置或设置中读取默认值
    QSettings settings;
    QString defaultHost = settings.value("database/host", "127.0.0.1").toString();
    int defaultPort = settings.value("database/port", 3306).toInt();
    QString defaultUser = settings.value("database/username", "root").toString();
    QString defaultPassword = settings.value("database/password", "").toString();
    QString defaultDbName = settings.value("database/dbname", "").toString();
    
    // 显示连接来源信息
    QLabel *configSourceLabel = new QLabel();
    // 直接检查XMLConfigReader是否已加载配置
    XMLConfigReader *configReader = XMLConfigReader::instance();
    if (configReader && !configReader->getHisDBConfig().server.isEmpty()) {
        configSourceLabel->setText("<small><i>配置来源: AppConfig.xml</i></small>");
    } else {
        configSourceLabel->setText("<small><i>配置来源: 本地设置</i></small>");
    }
    
    QLineEdit *hostEdit = new QLineEdit(defaultHost, &connectDialog);
    QLineEdit *portEdit = new QLineEdit(QString::number(defaultPort), &connectDialog);
    QLineEdit *userEdit = new QLineEdit(defaultUser, &connectDialog);
    QLineEdit *passwordEdit = new QLineEdit(defaultPassword, &connectDialog);
    QLineEdit *databaseEdit = new QLineEdit(defaultDbName, &connectDialog);
    
    // 设置密码输入框为密码模式
    passwordEdit->setEchoMode(QLineEdit::Password);
    
    formLayout->addRow(configSourceLabel);
    formLayout->addRow("主机地址:", hostEdit);
    formLayout->addRow("端口:", portEdit);
    formLayout->addRow("用户名:", userEdit);
    formLayout->addRow("密码:", passwordEdit);
    formLayout->addRow("数据库名:", databaseEdit);
    
    layout->addLayout(formLayout);
    
    // 添加测试连接和确定按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    QPushButton *testButton = new QPushButton("测试连接", &connectDialog);
    QPushButton *connectButton = new QPushButton("连接", &connectDialog);
    QPushButton *cancelButton = new QPushButton("取消", &connectDialog);
    
    buttonLayout->addWidget(testButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(connectButton);
    buttonLayout->addWidget(cancelButton);
    
    layout->addLayout(buttonLayout);
    
    // 连接信号和槽
    connect(testButton, &QPushButton::clicked, [&]() {
        QString host = hostEdit->text();
        int port = portEdit->text().toInt();
        QString user = userEdit->text();
        QString password = passwordEdit->text();
        QString database = databaseEdit->text();
        
        // 测试连接
        bool connected = dbManager->testConnection(host, port, user, password, database);
        
        if (connected) {
            QMessageBox::information(&connectDialog, "连接测试", "数据库连接测试成功！");
        } else {
            QMessageBox::warning(&connectDialog, "连接测试", 
                               "数据库连接测试失败！\n错误信息: " + dbManager->lastError());
        }
    });
    
    connect(connectButton, &QPushButton::clicked, &connectDialog, &QDialog::accept);
    connect(cancelButton, &QPushButton::clicked, &connectDialog, &QDialog::reject);
    
    // 显示对话框
    if (connectDialog.exec() != QDialog::Accepted) {
        return;
    }
    
    // 获取用户输入的连接参数
    QString host = hostEdit->text();
    int port = portEdit->text().toInt();
    QString user = userEdit->text();
    QString password = passwordEdit->text();
    QString database = databaseEdit->text();
    
    // 保存设置
    settings.setValue("database/host", host);
    settings.setValue("database/port", port);
    settings.setValue("database/username", user);
    settings.setValue("database/password", password);
    settings.setValue("database/dbname", database);
    
    // 连接数据库
    bool connected = dbManager->connectToDatabase(host, port, user, password, database);
    
    if (connected) {
        QMessageBox::information(this, "连接成功", "数据库连接成功！");
        
        // 清除站点模型的缓存数据，确保从新数据库获取最新数据
        if (stationModel) {
            stationModel->clearCache();
            
            // 预加载SOE事件表数据，确保事件类型能正确加载
            stationModel->preloadCache("htsoelog");
            
            // 检查主机名是否成功加载
            QString stationField = stationModel->getStationFieldForTable("htsoelog");
            QList<Station> stations = stationModel->getStationsFromTable("htsoelog", stationField);
            
            if (stations.size() <= 1) {  // 只有"所有"选项或空列表
                QMessageBox::warning(this, "数据加载警告", 
                                   "主机名列表加载失败或数据为空！\n请检查数据库中是否有数据。");
            }
        }
        
        // 初始化筛选器
        if (soeHistoryWidget) {
            soeHistoryWidget->initializeFilters();
        }
        
        // 加载遥测参数数据
        if (historyTreeWidget) {
            historyTreeWidget->loadTelemetryParameters();
        }
    } else {
        QMessageBox::critical(this, "连接失败", 
                            "数据库连接失败！\n错误信息: " + dbManager->lastError() + 
                            "\n\n请检查以下可能的原因:\n" +
                            "1. 数据库服务器是否已启动\n" +
                            "2. 主机地址和端口是否正确\n" +
                            "3. 用户名和密码是否正确\n" +
                            "4. 数据库名是否存在\n" +
                            "5. 网络连接是否正常\n" +
                            "6. 防火墙是否允许连接");
    }
}

void MainWindow::onPrintResults()
{
#if defined(QT_PRINTSUPPORT_LIB)
    // 使用soeHistoryWidget打印结果
    if (soeHistoryWidget) {
        soeHistoryWidget->printResults();
    }
#else
    QMessageBox::information(this, "打印功能不可用", "当前版本不支持打印功能。");
#endif
}

void MainWindow::onExportResults()
{
    // 使用soeHistoryWidget导出结果
    if (!soeHistoryWidget) {
        QMessageBox::warning(this, "导出错误", "没有可导出的数据！");
        return;
    }
    
    // 创建导出选项对话框
    QDialog exportDialog(this);
    exportDialog.setWindowTitle("导出选项");
    exportDialog.setMinimumWidth(300);
    
    QVBoxLayout *layout = new QVBoxLayout(&exportDialog);
    
    // 添加导出格式选择
    QRadioButton *csvRadio = new QRadioButton("CSV格式 (.csv)", &exportDialog);
    QRadioButton *excelRadio = new QRadioButton("Excel格式 (.xlsx)", &exportDialog);
    csvRadio->setChecked(true);
    
    layout->addWidget(new QLabel("请选择导出格式："));
    layout->addWidget(csvRadio);
    layout->addWidget(excelRadio);
    
    // 添加按钮
    QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(buttonBox, &QDialogButtonBox::accepted, &exportDialog, &QDialog::accept);
    connect(buttonBox, &QDialogButtonBox::rejected, &exportDialog, &QDialog::reject);
    
    layout->addWidget(buttonBox);
    
    // 显示对话框
    if (exportDialog.exec() != QDialog::Accepted) {
        return;
    }
    
    // 根据选择的格式导出
    if (csvRadio->isChecked()) {
        QString fileName = QFileDialog::getSaveFileName(this, "导出CSV文件", 
                                                      QDir::homePath() + "/查询结果_" + 
                                                      QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".csv", 
                                                      "CSV文件 (*.csv)");
        if (!fileName.isEmpty()) {
            exportToCSV(fileName);
        }
    } else {
        QString fileName = QFileDialog::getSaveFileName(this, "导出Excel文件", 
                                                      QDir::homePath() + "/查询结果_" + 
                                                      QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".xlsx", 
                                                      "Excel文件 (*.xlsx)");
        if (!fileName.isEmpty()) {
            exportToExcel(fileName);
        }
    }
}

void MainWindow::exportToCSV(const QString &filePath)
{
    // 使用soeHistoryWidget导出结果
    if (!soeHistoryWidget) {
        return;
    }
    
    // 调用SOEHistoryWidget的导出方法，传递文件路径
    soeHistoryWidget->exportResults(filePath);
}

void MainWindow::exportToExcel(const QString &filePath)
{
    // 由于Qt没有内置的Excel导出功能，我们可以使用CSV格式，然后让用户用Excel打开
    
    if (!soeHistoryWidget) {
        return;
    }
    
    // 调用SOEHistoryWidget的导出方法
    soeHistoryWidget->exportResults(filePath);
    
    QMessageBox::information(this, "导出提示", 
                           "由于系统限制，数据已导出为CSV格式。\n"
                           "您可以在Excel中打开此文件，然后另存为Excel格式。");
}

// 移除未使用的action槽函数实现，避免Qt自动连接警告
// void MainWindow::on_actionConnect_triggered()
// {
//     // 调用已有的连接方法
//     onConnectDatabase();
// }

// void MainWindow::on_actionDisconnect_triggered()
// {
//     // 断开数据库连接
//     if (dbManager && dbManager->isConnected()) {
//         dbManager->disconnectFromDatabase();
//         QMessageBox::information(this, "断开连接", "已断开数据库连接！");
//     } else {
//         QMessageBox::information(this, "断开连接", "当前没有活动的数据库连接！");
//     }
// }

// void MainWindow::on_actionExit_triggered()
// {
//     // 退出应用程序
//     QApplication::quit();
// }

// 实现缺失的数据加载方法
void MainWindow::loadMinData()
{
    // 设置当前历史数据类型为分钟级遥测数据
    if (soeHistoryWidget) {
        soeHistoryWidget->setHistoryDataType(MINUTE_TELEMETRY);
        setWindowTitle("分钟级遥测历史数据查询");
    }
    
    // 通知历史树控件更新选择
    if (historyTreeWidget) {
        historyTreeWidget->setCurrentType(MINUTE_TELEMETRY);
    }
}

void MainWindow::loadSecData()
{
    // 设置当前历史数据类型为秒级遥测数据
    if (soeHistoryWidget) {
        soeHistoryWidget->setHistoryDataType(SECOND_TELEMETRY);
        setWindowTitle("秒级遥测历史数据查询");
    }
    
    // 通知历史树控件更新选择
    if (historyTreeWidget) {
        historyTreeWidget->setCurrentType(SECOND_TELEMETRY);
    }
}

// 实现缺失的其他辅助方法
void MainWindow::setupDatabase()
{
    // 根据配置文件或设置初始化数据库连接
    QSettings settings;
    QString host = settings.value("database/host", "127.0.0.1").toString();
    int port = settings.value("database/port", 3306).toInt();
    QString user = settings.value("database/username", "root").toString();
    QString password = settings.value("database/password", "").toString();
    QString database = settings.value("database/dbname", "").toString();
    
    // 直接使用已加载的配置（在main.cpp中已经加载过）
    XMLConfigReader* configReader = XMLConfigReader::instance();
    if (configReader) {
        // 获取历史数据库配置
        DBConfig hisDBConfig = configReader->getHisDBConfig();
        
        // 如果配置有效，使用配置信息
        if (!hisDBConfig.server.isEmpty() && !hisDBConfig.name.isEmpty()) {
            host = hisDBConfig.server;
            port = hisDBConfig.port;
            user = hisDBConfig.user;
            password = hisDBConfig.pwd;
            database = hisDBConfig.name;
        }
    }
    
    // 尝试连接数据库
    if (!host.isEmpty() && !database.isEmpty()) {
        dbManager->connectToDatabase(host, port, user, password, database);
    }
}

void MainWindow::setupTableView()
{
    // 初始化表格视图
    if (!m_tableView) {
        m_tableView = new QTableView(this);
        m_model = new QStandardItemModel(this);
        m_tableView->setModel(m_model);
        
        // 设置表格样式
        m_tableView->setAlternatingRowColors(true);
        m_tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
        m_tableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
        m_tableView->horizontalHeader()->setStretchLastSection(true);
        m_tableView->verticalHeader()->setVisible(false);
    }
}

QList<HIS_DATA_DEVICE_VALUE> MainWindow::parseCurveValue(const QByteArray& blobData, bool isMinuteData)
{
    QList<HIS_DATA_DEVICE_VALUE> result;
    
    // 处理空数据
    if (blobData.isEmpty()) {
        return result;
    }
    
    // 定义点数
    int numPoints = isMinuteData ? 25 : 300; // 分钟数据25点，秒数据300点
    
    // 每个结构体的大小 (unsigned char status + float val)
    const int structSize = 1 + sizeof(float);
    
    // 校验数据大小
    if (blobData.size() < numPoints * structSize) {
        numPoints = blobData.size() / structSize; // 调整点数以适应实际数据
        if (numPoints == 0) {
            return result;
        }
    }
    
    // 创建数据流以读取二进制数据
    QDataStream stream(blobData);
    stream.setByteOrder(QDataStream::LittleEndian); // 使用小端字节序
    
    // 逐点解析数据
    for (int i = 0; i < numPoints; ++i) {
        HIS_DATA_DEVICE_VALUE point;
        quint8 status;
        
        // 读取status (unsigned char)
        stream >> status;
        point.status = status;
        
        // 读取val (float)
        stream >> point.val;
        
        // 将解析的点添加到结果列表
        result.append(point);
    }
    
    return result;
} 