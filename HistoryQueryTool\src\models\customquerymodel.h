#ifndef CUSTOMQUERYMODEL_H
#define CUSTOMQUERYMODEL_H

#include <QSqlQueryModel>
#include <QDateTime>
#include <QVariant>

/**
 * @brief 自定义查询模型，专门用于处理日期时间字段的显示格式以及设备ID映射
 * 
 * 该模型继承自QSqlQueryModel，重写了data方法以确保日期时间类型的字段
 * 在显示时包含秒，格式为yyyy-MM-dd HH:mm:ss，并且将设备ID映射为设备名称
 */
class CustomQueryModel : public QSqlQueryModel
{
    Q_OBJECT

public:
    explicit CustomQueryModel(QObject *parent = nullptr);

    /**
     * @brief 重写data方法，处理日期时间的显示和设备ID映射
     * @param index 模型索引
     * @param role 数据角色
     * @return 处理后的数据
     */
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;

    /**
     * @brief 设置是否启用设备ID映射
     * @param enable 是否启用
     */
    void setDeviceIdMappingEnabled(bool enable);

    /**
     * @brief 获取是否启用设备ID映射
     * @return 是否启用
     */
    bool isDeviceIdMappingEnabled() const { return m_deviceIdMappingEnabled; }

    /**
     * @brief 重新实现setQuery方法，添加列信息调试输出
     * @param query SQL查询对象
     */
    void setQuery(const QSqlQuery &query);

private:
    /**
     * @brief 判断字段是否为日期时间类型
     * @param fieldName 字段名称
     * @return 是否为日期时间字段
     */
    bool isDateTimeField(const QString &fieldName) const;

    /**
     * @brief 判断字段是否为设备ID字段
     * @param fieldName 字段名称
     * @return 是否为设备ID字段
     */
    bool isDeviceIdField(const QString &fieldName) const;

    /**
     * @brief 将设备ID转换为设备名称
     * @param deviceId 设备ID
     * @return 设备名称
     */
    QString mapDeviceIdToName(const QVariant &deviceId) const;

    // 是否启用设备ID映射
    bool m_deviceIdMappingEnabled;
};

#endif // CUSTOMQUERYMODEL_H