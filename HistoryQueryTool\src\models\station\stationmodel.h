#ifndef STATIONMODEL_H
#define STATIONMODEL_H

#include <QObject>
#include <QList>
#include <QString>
#include <QSqlQuery>
#include <QMap>
#include <QDateTime>

// 主机信息结构体
struct Station {
    int id;
    QString name;
};

// 事件类型信息结构体
struct Bay {
    int id;
    int stationId;  // 关联的主机ID
    QString name;
};

// 设备信息结构体
struct Device {
    int id;
    int bayId;      // 关联的事件类型ID
    QString name;
    QString type;   // 设备类型
};

class StationModel : public QObject
{
    Q_OBJECT
public:
    static StationModel* instance();
    
    // 检查表结构
    void checkTableStructure();
    
    // 从指定表获取主机列表
    QList<Station> getStationsFromTable(const QString &tableName, const QString &stationField);
    
    // 从指定表获取指定主机的事件类型列表
    QList<Bay> getBaysFromTable(const QString &tableName, const QString &stationName);
    
    // 从指定表获取事件类型列表
    QList<Bay> getEventTypesFromTable(const QString &tableName, const QString &typeField, const QString &stationName);
    
    // 从指定表获取指定主机和事件类型的设备列表
    QList<Device> getDevicesFromTable(const QString &tableName, const QString &stationField,
                                    const QString &bayField, const QString &deviceField,
                                    const QString &stationName, const QString &bayName);
    
    // 检查表是否存在指定字段
    bool tableHasField(const QString &tableName, const QString &fieldName);
    
    // 清除缓存
    void clearCache();
    
    // 获取历史数据类型对应的表名
    QString getHistoryTypeTableName(int historyType);
    
    // 获取表对应的主机字段名
    QString getStationFieldForTable(const QString &tableName);
    
    // 获取表对应的事件类型字段名
    QString getBayFieldForTable(const QString &tableName);
    
    // 获取表对应的设备字段名
    QString getDeviceFieldForTable(const QString &tableName);
    
    // 预加载缓存数据
    void preloadCache(const QString &tableName);
    
private:
    explicit StationModel(QObject *parent = nullptr);
    ~StationModel();
    
    static StationModel *m_instance;
    
    // 从数据库获取类型数据的辅助方法
    void getTypesFromDatabase(const QString &tableName, const QString &typeField, 
                             const QString &stationName, const QString &stationField,
                             class TypeMappingManager* typeMappingManager, 
                             QList<Bay> &results, QSet<QString> &uniqueTypes);
    
    // 缓存每个表的主机数据
    QMap<QString, QList<Station>> stationsCache;
    
    // 缓存每个表+主机的事件类型数据
    QMap<QString, QMap<QString, QList<Bay>>> baysCache;
    
    // 缓存每个表+主机+事件类型的设备数据
    QMap<QString, QMap<QString, QMap<QString, QList<Device>>>> devicesCache;
    
    // 缓存表字段信息
    QMap<QString, QStringList> tableFieldsCache;
    
    // 缓存更新时间
    QDateTime lastCacheUpdateTime;
    
    // 缓存失效时间(秒)
    int cacheExpirationSeconds;
    
    // 记录每个表是否已经预加载过
    QMap<QString, bool> preloadedTables;
};

#endif // STATIONMODEL_H 