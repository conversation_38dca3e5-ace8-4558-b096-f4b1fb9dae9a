QT       += core gui sql printsupport xml

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

TARGET = HistoryQueryTool
TEMPLATE = app

SOURCES += \
    src/main.cpp \
    src/mainwindow.cpp \
    src/database/dbmanager.cpp \
    src/database/dbconnectionfactory.cpp \
    src/models/station/stationmodel.cpp \
    src/models/customquerymodel.cpp \
    src/models/telemetrydatamodel.cpp \
    src/widgets/historytreewidget.cpp \
    src/widgets/querywidget.cpp \
    src/widgets/soehistorywidget.cpp \
    src/widgets/filters/filtermanager.cpp \
    src/widgets/providers/historydataprovider.cpp \
    src/widgets/providers/telemetrydataprovider.cpp \
    src/widgets/tables/tablemanager.cpp \
    src/utils/db/tablestructuremanager.cpp \
    src/utils/xmlconfigreader.cpp \
    src/utils/devicenamemapper.cpp \
    src/utils/typemappingmanager.cpp \
    src/common/logger.cpp \
    src/common/tablenamemanager.cpp

HEADERS += \
    src/mainwindow.h \
    src/database/dbmanager.h \
    src/database/dbconnectionfactory.h \
    src/models/station/stationmodel.h \
    src/models/customquerymodel.h \
    src/models/telemetrydatamodel.h \
    src/widgets/historytreewidget.h \
    src/widgets/querywidget.h \
    src/widgets/soehistorywidget.h \
    src/widgets/filters/filtermanager.h \
    src/widgets/providers/historydataprovider.h \
    src/widgets/providers/telemetrydataprovider.h \
    src/widgets/tables/tablemanager.h \
    src/utils/db/tablestructuremanager.h \
    src/common/enums.h \
    src/common/constants.h \
    src/common/datastructures.h \
    src/utils/xmlconfigreader.h \
    src/utils/devicenamemapper.h \
    src/utils/typemappingmanager.h \
    src/common/logger.h \
    src/common/tablenamemanager.h

FORMS += \
    src/mainwindow.ui \
    src/widgets/querywidget.ui

CONFIG += c++11 

# 忽略Qt自身的一些弃用警告
QMAKE_CXXFLAGS += -Wno-deprecated-copy 