#include "dbconnectionfactory.h"
#include "../utils/xmlconfigreader.h"
#include <QSqlDatabase>
#include <QSqlError>
#include <QDebug>

QSqlDatabase DBConnectionFactory::createParaDBConnection(const QString &connectionName)
{
    XMLConfigReader *configReader = XMLConfigReader::instance();
    if (!configReader) {
        qWarning() << "XMLConfigReader实例不存在";
        return QSqlDatabase();
    }
    
    DBConfig paraDBConfig = configReader->getParaDBConfig();
    if (paraDBConfig.server.isEmpty() || paraDBConfig.name.isEmpty()) {
        qWarning() << "参数库配置无效，请检查AppConfig.xml文件";
        return QSqlDatabase();
    }
    
    return createConnectionFromConfig(paraDBConfig, connectionName);
}

QSqlDatabase DBConnectionFactory::createHisDBConnection(const QString &connectionName)
{
    XMLConfigReader *configReader = XMLConfigReader::instance();
    if (!configReader) {
        qWarning() << "XMLConfigReader实例不存在";
        return QSqlDatabase();
    }
    
    DBConfig hisDBConfig = configReader->getHisDBConfig();
    if (hisDBConfig.server.isEmpty() || hisDBConfig.name.isEmpty()) {
        qWarning() << "历史库配置无效，请检查AppConfig.xml文件";
        return QSqlDatabase();
    }
    
    return createConnectionFromConfig(hisDBConfig, connectionName);
}

QSqlDatabase DBConnectionFactory::createConnectionFromConfig(const DBConfig &config, const QString &connectionName)
{
    QSqlDatabase db = QSqlDatabase::addDatabase("QMYSQL", connectionName);
    db.setHostName(config.server);
    db.setPort(config.port);
    db.setDatabaseName(config.name);
    db.setUserName(config.user);
    db.setPassword(config.pwd);
    
    return db;
} 