#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QToolBar>
#include <QAction>
#include <QSplitter>
#include "database/dbmanager.h"
#include "models/station/stationmodel.h"
#include "widgets/soehistorywidget.h"
#include "widgets/historytreewidget.h"
#include "common/datastructures.h"
#include <QtSql>
#include <QTableView>
#include <QStandardItemModel>

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onConnectDatabase();
    void onPrintResults();
    void onExportResults();
    void exportToCSV(const QString &filePath);
    void exportToExcel(const QString &filePath);
    void onHistoryTypeSelected(int type);
    // 移除未使用的action槽函数，避免Qt自动连接警告
    // void on_actionConnect_triggered();
    // void on_actionDisconnect_triggered();
    // void on_actionExit_triggered();
    void loadMinData(); // 新增：加载分钟数据槽函数
    void loadSecData(); // 新增：加载秒数据槽函数

protected:
    void setupUI();
    void createActions();
    void createToolBar();

private:
    Ui::MainWindow *ui;
    QToolBar *mainToolBar;
    QAction *connectAction;
    QAction *printAction;
    QAction *exportAction;
    
    // 分割器
    QSplitter *mainSplitter;
    
    // 历史树控件
    HistoryTreeWidget *historyTreeWidget;
    
    // SOE历史查询窗口
    SOEHistoryWidget *soeHistoryWidget;
    
    DBManager *dbManager;
    StationModel *stationModel;
    QSqlDatabase m_db; // 新增：数据库连接对象
    QTableView *m_tableView; // 新增：表格视图
    QStandardItemModel *m_model; // 新增：表格模型

    void setupDatabase(); // 新增：初始化数据库连接
    void setupTableView(); // 新增：初始化表格视图
    QList<HIS_DATA_DEVICE_VALUE> parseCurveValue(const QByteArray& blobData, bool isMinuteData); // 新增：解析 curvevalue
};

#endif // MAINWINDOW_H 