#ifndef TYPEMAPPINGMANAGER_H
#define TYPEMAPPINGMANAGER_H

#include <QObject>
#include <QString>
#include <QMap>

/**
 * @brief TypeMappingManager类 - 管理所有类型映射的单例类
 * 
 * 该类负责集中管理系统中所有类型字段(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)的映射关系，
 * 提供了在显示名称和数据库存储值之间转换的功能。
 */
class TypeMappingManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取TypeMappingManager的单例实例
     * @return TypeMappingManager单例实例的指针
     */
    static TypeMappingManager* instance();

    /**
     * @brief 根据类型字段名和值获取对应的显示名称
     * @param fieldName 类型字段名(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)
     * @param value 数据库中存储的值(通常是数字)
     * @return 对应的显示名称
     */
    QString getNameByValue(const QString& fieldName, const QString& value);

    /**
     * @brief 根据类型字段名和显示名称获取对应的数据库存储值
     * @param fieldName 类型字段名(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)
     * @param name 显示名称
     * @return 对应的数据库存储值
     */
    QString getValueByName(const QString& fieldName, const QString& name);

    /**
     * @brief 获取指定类型字段的所有显示名称列表
     * @param fieldName 类型字段名(VALUETYPE, ERRTYPE, OPERTYPE, BREAKLIMITTYPE)
     * @return 显示名称列表
     */
    QStringList getAllNames(const QString& fieldName);

    /**
     * @brief 判断给定的字段名是否是支持的类型映射字段
     * @param fieldName 字段名
     * @return 是否支持类型映射
     */
    bool isMappedTypeField(const QString& fieldName);

private:
    // 构造函数和析构函数私有化，确保单例模式
    explicit TypeMappingManager(QObject* parent = nullptr);
    ~TypeMappingManager() override = default;

    // 禁止拷贝和赋值
    TypeMappingManager(const TypeMappingManager&) = delete;
    TypeMappingManager& operator=(const TypeMappingManager&) = delete;

    // 初始化所有映射关系
    void initializeMappings();

    // 存储各类型字段的名称到值的映射
    QMap<QString, QMap<QString, QString>> m_nameToValueMaps;
    
    // 存储各类型字段的值到名称的映射
    QMap<QString, QMap<QString, QString>> m_valueToNameMaps;

    // 单例实例
    static TypeMappingManager* m_instance;
};

#endif // TYPEMAPPINGMANAGER_H 