#ifndef SOEHISTORYWIDGET_H
#define SOEHISTORYWIDGET_H

#include "querywidget.h"
#include "../common/enums.h"
#include "providers/historydataprovider.h"
#include "providers/telemetrydataprovider.h"
#include "../models/telemetrydatamodel.h"
#include <QTreeWidget>

class FilterManager;
class TableManager;
class StationModel;
class DBManager;
class QSqlQuery;

namespace Ui {
class QueryWidget;
}

/**
 * @brief 历史数据查询窗口类
 * 重构后的SOEHistoryWidget, 使用组合模式而非继承
 */
class SOEHistoryWidget : public QueryWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit SOEHistoryWidget(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SOEHistoryWidget() override;

    /**
     * @brief 设置站点模型
     * @param model 站点模型
     */
    void setStationModel(StationModel *model);
    
    /**
     * @brief 设置数据库管理器
     * @param manager 数据库管理器
     */
    void setDBManager(DBManager *manager) override;
    
    /**
     * @brief 设置历史数据类型
     * @param type 历史数据类型
     */
    void setHistoryDataType(HistoryDataType type);
    
    /**
     * @brief 获取历史数据类型
     * @return 当前历史数据类型
     */
    HistoryDataType getHistoryDataType() const;
    
    /**
     * @brief 获取当前历史数据类型
     * @return 当前历史数据类型
     */
    HistoryDataType getCurrentHistoryDataType() const { return m_historyDataType; }
    
    /**
     * @brief 保存当前过滤器状态
     * 保存遥测查询的时间范围、设备ID和设备索引等过滤条件
     */
    void saveFilterState();
    
    /**
     * @brief 恢复过滤器状态
     * 恢复之前保存的过滤器状态
     */
    void restoreFilterState();
    
    /**
     * @brief 打印查询结果
     */
    void printResults();
    
    /**
     * @brief 导出查询结果
     * @param filePath 可选的文件路径，如果为空将弹出文件选择对话框
     */
    void exportResults(const QString &filePath = QString());
    
    /**
     * @brief 刷新查询数据
     */
    void refreshData() override;
    
    /**
     * @brief 初始化筛选器
     * 主动初始化筛选框数据，不需要等待查询
     */
    void initializeFilters();

    /**
     * @brief 设置当前遥测点信息
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @param name 遥测点名称
     */
    void setCurrentTelemetryPoint(int deviceId, int deviceIndex, const QString &name);
    
protected:
    /**
     * @brief 初始化UI
     */
    void initUI() override;
    
    /**
     * @brief 初始化信号连接
     */
    void initConnections() override;
    
    /**
     * @brief 构建查询SQL
     * @return 构建的SQL语句
     */
    QString buildQuerySQL() const override;
    
    /**
     * @brief 设置表格表头
     */
    void setupTableHeaders() override;
    
    /**
     * @brief 执行查询
     */
    void executeQuery() override;

public slots:
    void onTelemetryPointSelected(int deviceId, int deviceIndex, const QString &name);

private slots:
    /**
     * @brief 查询按钮点击事件
     */
    void onQueryButtonClicked();
    
    /**
     * @brief 打印按钮点击事件
     */
    void onPrintButtonClicked();
    
    /**
     * @brief 导出按钮点击事件
     */
    void onExportButtonClicked();
    
    /**
     * @brief 结果计数事件
     * @param count 结果数量
     */
    void onResultCounted(int count);
    
    /**
     * @brief 消息通知事件
     * @param title 消息标题
     * @param message 消息内容
     */
    void onMessageNotification(const QString &title, const QString &message);
    
    /**
     * @brief 树节点点击事件处理
     * @param item 点击的树节点
     */
    void onTreeItemClicked(QTreeWidgetItem *item);

private:
    /**
     * @brief 获取导出文件名
     * @return 选择的文件名
     */
    QString getExportFileName();
    
    /**
     * @brief 执行遥测历史数据查询
     * @param deviceId 设备ID
     * @param deviceIndex 设备索引
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void executeTelemetryQuery(int deviceId, int deviceIndex, 
                              const QDateTime &startTime, 
                              const QDateTime &endTime);
    
    /**
     * @brief 解析SQL查询结果到遥测数据模型
     * @param query SQL查询结果
     * @param model 遥测数据模型
     */
    void parseTelemetryQueryResult(QSqlQuery &query, TelemetryDataModel *model);
    
    /**
     * @brief 获取当前活动模型
     * 根据当前历史数据类型，返回相应的模型
     * @return 当前活动的模型
     */
    QAbstractItemModel* getCurrentModel() const;
    
    /**
     * @brief 将遥测数据导出为CSV文件
     * @param fileName 文件名
     * @param model 遥测数据模型
     */
    void exportTelemetryDataToCSV(const QString &fileName, TelemetryDataModel *model);
    
    /**
     * @brief 优化遥测表格列宽
     * 为遥测数据表格设置合适的列宽
     */
    void optimizeTelemetryTableColumnWidths();
    
    HistoryDataType m_historyDataType;
    QString m_currentTable;
    
    // 当前遥测点信息
    int m_currentDeviceId;
    int m_currentDeviceIndex;
    QString m_currentPointName;
    
    // 保存的过滤器状态（用于切换查询类型时保持状态）
    struct {
        QDateTime startTime;
        QDateTime endTime;
        int deviceId;
        int deviceIndex;
        QString pointName;
        int interval;
    } m_savedFilterState;
    
    // 数据提供者
    HistoryDataProvider *m_dataProvider;
    
    // 遥测数据提供者
    TelemetryDataProvider *m_telemetryDataProvider;
    
    // 遥测数据模型
    TelemetryDataModel *m_telemetryDataModel;
    
    // 过滤器管理器
    FilterManager *m_filterManager;
    
    // 表格管理器
    TableManager *m_tableManager;
    
    // 站点模型
    StationModel *m_stationModel;
    
    // 数据库管理器
    DBManager *m_dbManager;
};

#endif // SOEHISTORYWIDGET_H 