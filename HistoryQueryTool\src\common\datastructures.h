#ifndef DATASTRUCTURES_H
#define DATASTRUCTURES_H

#include <QDateTime>

/**
 * @brief 遥测数据点结构
 */
struct TelemetryDataPoint {
    QDateTime timestamp;  // 时间戳
    double value;         // 值
    int quality;          // 质量码（0为异常，1为正常）
    
    TelemetryDataPoint() : value(0.0), quality(0) {}
    
    TelemetryDataPoint(const QDateTime &ts, double val = 0.0, int qual = 0)
        : timestamp(ts), value(val), quality(qual) {}
};

// 新增：历史遥测数据点结构
typedef struct his_data_device_value {
    unsigned char status; // 写入状态
    float val;
} HIS_DATA_DEVICE_VALUE;

#endif // DATASTRUCTURES_H 