<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QueryWidget</class>
 <widget class="QWidget" name="QueryWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QLabel" name="titleLabel">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>历史数据查询</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="queryConditionGroup">
     <property name="title">
      <string>查询条件</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="stationLabel">
        <property name="text">
         <string>主机名:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="stationComboBox"/>
      </item>
      <item row="0" column="2">
       <widget class="QLabel" name="bayLabel">
        <property name="text">
         <string>事件类型:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="3">
       <widget class="QComboBox" name="bayComboBox"/>
      </item>
      <item row="0" column="4">
       <widget class="QLabel" name="deviceLabel">
        <property name="text">
         <string>设备:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <widget class="QComboBox" name="deviceComboBox"/>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="signalFilterLabel">
        <property name="text">
         <string>信号过滤:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1" colspan="5">
       <widget class="QLineEdit" name="signalFilterEdit"/>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="startTimeLabel">
        <property name="text">
         <string>起始时间:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QDateEdit" name="startDateEdit">
        <property name="calendarPopup">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QTimeEdit" name="startTimeEdit"/>
      </item>
      <item row="2" column="3">
       <widget class="QLabel" name="endTimeLabel">
        <property name="text">
         <string>结束时间:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="QDateEdit" name="endDateEdit">
        <property name="calendarPopup">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="2" column="5">
       <widget class="QTimeEdit" name="endTimeEdit"/>
      </item>
      <item row="3" column="0" colspan="5">
       <widget class="QLabel" name="pageInfoLabel">
        <property name="text">
         <string>第 0/0 页</string>
        </property>
       </widget>
      </item>
      <item row="3" column="5">
       <widget class="QPushButton" name="queryButton">
        <property name="text">
         <string>查询</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QTableView" name="resultTableView">
     <property name="alternatingRowColors">
      <bool>true</bool>
     </property>
     <property name="selectionBehavior">
      <enum>QAbstractItemView::SelectRows</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
