#include "telemetrydatamodel.h"
#include <QDebug>
#include <QDataStream>
#include <QDateTime>

// TelemetryDataPoint 结构体定义
// 注意: 这个结构体的定义通常放在 telemetrydatamodel.h 头文件中。
// 如果它已在头文件中定义，请移除这里的定义。
// struct TelemetryDataPoint { // <--- 移除此结构体定义
//     qint64 timestampMs; 
//     double value;       
//     quint8 quality;     
//
//     TelemetryDataPoint(qint64 ts, double val, quint8 qual)
//         : timestampMs(ts), value(val), quality(qual) {}
// };

TelemetryDataModel::TelemetryDataModel(QObject *parent)
    : QAbstractTableModel(parent)
{
}

int TelemetryDataModel::rowCount(const QModelIndex &parent) const
{
    if (parent.isValid())
        return 0;
    
    return m_dataPoints.size();
}

int TelemetryDataModel::columnCount(const QModelIndex &parent) const
{
    if (parent.isValid())
        return 0;
    
    return COLUMN_COUNT;
}

QVariant TelemetryDataModel::data(const QModelIndex &index, int role) const
{
    if (!index.isValid() || index.row() >= m_dataPoints.size())
        return QVariant();
    
    if (role == Qt::DisplayRole || role == Qt::EditRole) {
        const TelemetryDataPoint &dataPoint = m_dataPoints.at(index.row());
        
        switch (index.column()) {
            case TIMESTAMP:
                return dataPoint.timestamp.toString("yyyy-MM-dd HH:mm:ss");
            case VALUE: {
                // 应用系数和偏移量
                double rawValue = dataPoint.value;
                double displayValue = rawValue * rate() + offset();
                return QString::number(displayValue, 'f', 3);
            }
            case QUALITY:
                if (dataPoint.quality == 1)
                    return "正常";
                else if (dataPoint.quality == 0)
                    return "异常";
                else
                    return QString("质量码: %1").arg(dataPoint.quality);
            default:
                return QVariant();
        }
    } else if (role == Qt::TextAlignmentRole) {
        // 数值右对齐，其他居中
        if (index.column() == VALUE)
            return Qt::AlignRight + Qt::AlignVCenter;
        else
            return Qt::AlignCenter;
    } else if (role == Qt::ToolTipRole) {
        // 提供更详细的提示信息
        const TelemetryDataPoint &dataPoint = m_dataPoints.at(index.row());
        
        switch (index.column()) {
            case TIMESTAMP:
                return dataPoint.timestamp.toString("yyyy-MM-dd HH:mm:ss.zzz");
            case VALUE: {
                double rawValue = dataPoint.value;
                double displayValue = rawValue * rate() + offset();
                QString valueString = QString::number(displayValue, 'f', 3);
                if (!unit().isEmpty())
                    valueString += " " + unit();
                return valueString;
            }
            case QUALITY:
                if (dataPoint.quality == 1)
                    return "正常";
                else if (dataPoint.quality == 0)
                    return "异常";
                else
                    return QString("质量码: %1").arg(dataPoint.quality);
            default:
                return QVariant();
        }
    }
    
    return QVariant();
}

QVariant TelemetryDataModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole) {
        switch (section) {
            case TIMESTAMP:
                return "时间";
            case VALUE:
                return QString("值") + (!unit().isEmpty() ? " (" + unit() + ")" : "");
            case QUALITY:
                return "质量";
            default:
                return QVariant();
        }
    }
    
    return QVariant();
}

void TelemetryDataModel::setData(const QVector<TelemetryDataPoint> &dataPoints)
{
    beginResetModel();
    m_dataPoints = dataPoints;
    endResetModel();
}

void TelemetryDataModel::addDataPoint(const TelemetryDataPoint &dataPoint)
{
    beginInsertRows(QModelIndex(), m_dataPoints.size(), m_dataPoints.size());
    m_dataPoints.append(dataPoint);
    endInsertRows();
}

void TelemetryDataModel::clear()
{
    beginResetModel();
    m_dataPoints.clear();
    endResetModel();
}

TelemetryDataPoint TelemetryDataModel::dataPointAt(int index) const
{
    if (index >= 0 && index < m_dataPoints.size())
        return m_dataPoints.at(index);
    
    return TelemetryDataPoint();
}

QVector<TelemetryDataPoint> TelemetryDataModel::dataPoints() const
{
    return m_dataPoints;
}

void TelemetryDataModel::setProperties(const QMap<QString, QVariant> &properties)
{
    m_properties = properties;
    
    // 当属性改变时，可能需要重新显示数据（例如单位变化）
    if (!m_dataPoints.isEmpty()) {
        // 通知视图数据已改变
        emit dataChanged(index(0, VALUE), index(m_dataPoints.size() - 1, VALUE));
        emit headerDataChanged(Qt::Horizontal, VALUE, VALUE);
    }
}

QMap<QString, QVariant> TelemetryDataModel::properties() const
{
    return m_properties;
}

QString TelemetryDataModel::name() const
{
    return m_properties.value("Name").toString();
}

QString TelemetryDataModel::unit() const
{
    return m_properties.value("Unit").toString();
}

double TelemetryDataModel::rate() const
{
    bool ok;
    double r = m_properties.value("Rate").toDouble(&ok);
    return ok ? r : 1.0; // 默认系数为1
}

double TelemetryDataModel::offset() const
{
    bool ok;
    double o = m_properties.value("Offset").toDouble(&ok);
    return ok ? o : 0.0; // 默认偏移为0
}

// 新增方法：从 BLOB 加载和解析数据
// 注意：此方法的声明应在 telemetrydatamodel.h 文件中：
// public:
//    void loadDataFromBlob(const QByteArray &blobData, qint64 baseTimestampEpochSecs, bool isMinuteData);
//
void TelemetryDataModel::loadDataFromBlob(const QByteArray &blobData, qint64 baseTimestampEpochSecs, bool isMinuteData)
{
    //beginResetModel(); // 不再由这个方法直接重置模型
    //m_dataPoints.clear(); 

    QVector<TelemetryDataPoint> parsedPoints = parseBlobData(blobData, baseTimestampEpochSecs, isMinuteData);
    setData(parsedPoints); // 调用setData来更新整个模型

    //endResetModel();
}

// 新增静态方法的实现
QVector<TelemetryDataPoint> TelemetryDataModel::parseBlobData(const QByteArray &blobData, qint64 baseTimestampEpochSecs, bool isMinuteData)
{
    QVector<TelemetryDataPoint> parsedDataPoints;
    // HIS_DATA_DEVICE_VALUE 结构: unsigned char status (1 byte), float val (4 bytes)
    const int pointStructSize = 1 + 4; // 每个数据点的字节大小
    int numPoints = 0;
    double totalDurationSeconds = 0.0; // BLOB 数据所代表的总时长（秒）

    // 转换基准时间戳为可读格式，用于调试
    QDateTime baseDateTime = QDateTime::fromSecsSinceEpoch(baseTimestampEpochSecs);
    qDebug() << "parseBlobData: 基准时间戳=" << baseTimestampEpochSecs 
             << "基准日期时间=" << baseDateTime.toString("yyyy-MM-dd HH:mm:ss");

    if (isMinuteData) {
        // 分钟级数据: 5个点分布在5分钟内，每个点间隔1分钟
        numPoints = 5;
        totalDurationSeconds = 300.0; // 5个点分布在5分钟 (300秒) 内
    } else {
        // 秒级数据: 300个点分布在5分钟内，每个点间隔1秒
        numPoints = 300;
        totalDurationSeconds = 300.0;  // 300个点分布在5分钟 (300秒) 内
    }

    if (numPoints == 0) {
        qWarning() << "TelemetryDataModel::parseBlobData: numPoints is zero, check input parameters.";
        return parsedDataPoints; // 返回空集合
    }

    // 校验 BLOB 数据大小
    if (blobData.size() < numPoints * pointStructSize) {
        qWarning() << "TelemetryDataModel::parseBlobData: Blob data size is insufficient."
                   << "Expected at least" << (numPoints * pointStructSize) << "bytes,"
                   << "but got" << blobData.size() << "bytes."
                   << "Attempting to process" << (blobData.size() / pointStructSize) << "available points.";
        numPoints = blobData.size() / pointStructSize; // 只处理可用的完整数据点
        if (numPoints == 0) {
             return parsedDataPoints;
        }
    } else if (blobData.size() > numPoints * pointStructSize) {
         qWarning() << "TelemetryDataModel::parseBlobData: Blob data size is larger than expected."
                   << "Expected" << (numPoints * pointStructSize) << "bytes,"
                   << "but got" << blobData.size() << "bytes."
                   << "Processing only the first" << numPoints << "points.";
    }

    double intervalMs = 0.0;
    if (numPoints > 0) { // 避免除以零
        intervalMs = (totalDurationSeconds * 1000.0) / static_cast<double>(numPoints);
    }
    
    if (numPoints == 1) {
        intervalMs = 0.0;
    }

    QDataStream stream(blobData);
    stream.setByteOrder(QDataStream::LittleEndian);
    stream.setFloatingPointPrecision(QDataStream::SinglePrecision);

    // 应用时间偏移修正：insertDate记录的是写入时间，实际采集时间早5分钟
    qint64 baseTimestampMs = (baseTimestampEpochSecs + TIME_OFFSET_SECONDS) * 1000LL;

    for (int i = 0; i < numPoints; ++i) {
        if (stream.atEnd()) {
            qWarning() << "TelemetryDataModel::parseBlobData: Stream ended prematurely at point index" << i
                       << "while expecting to read" << numPoints << "points.";
            break;
        }

        quint8 status_val;
        float value_float;

        stream >> status_val;
        if (stream.status() != QDataStream::Ok) {
            qWarning() << "TelemetryDataModel::parseBlobData: Error reading status at point index" << i
                       << "Stream status:" << stream.status();
            break;
        }

        if (stream.device()->bytesAvailable() < static_cast<qint64>(sizeof(float))) {
            qWarning() << "TelemetryDataModel::parseBlobData: Not enough data to read float value at point index" << i;
            break;
        }
        stream >> value_float;
        if (stream.status() != QDataStream::Ok) {
            qWarning() << "TelemetryDataModel::parseBlobData: Error reading value_float at point index" << i
                       << "Stream status:" << stream.status();
            break;
        }
        
        qint64 currentPointTimestampMs = baseTimestampMs + static_cast<qint64>(i * intervalMs);
        QDateTime currentPointDateTime = QDateTime::fromMSecsSinceEpoch(currentPointTimestampMs);
        
        // 输出第一个和最后一个点的时间戳，用于调试
        if (i == 0 || i == numPoints - 1) {
            qDebug() << "数据点[" << i << "]: 时间戳=" << (currentPointTimestampMs/1000) 
                     << "日期时间=" << currentPointDateTime.toString("yyyy-MM-dd HH:mm:ss");
        }
        
        parsedDataPoints.append(TelemetryDataPoint(currentPointDateTime, static_cast<double>(value_float), static_cast<int>(status_val)));
    }
    return parsedDataPoints;
} 