#ifndef QUERYWIDGET_H
#define QUERYWIDGET_H

#include <QWidget>
#include <QDateTime>
#include "../models/customquerymodel.h"

#if defined(QT_PRINTSUPPORT_LIB)
#include <QPrinter>
#endif

// 前向声明
class DBManager;

namespace Ui {
class QueryWidget;
}

class QSqlTableModel;

class QueryWidget : public QWidget
{
    Q_OBJECT

public:
    // 静态常量定义
    static const QString HOSTNAME_FIELD;
    static const QString DEVICE_FIELD;

    explicit QueryWidget(QWidget *parent = nullptr);
    virtual ~QueryWidget();

    // 获取查询模型
    CustomQueryModel* model() const;

    // 设置数据库管理器
    virtual void setDBManager(DBManager *dbManager);

    // 获取自动查询状态
    bool getAutoQueryEnabled() const { return autoQueryEnabled; }

public slots:
    // 执行查询
    virtual void executeQuery();
    
    // 刷新数据
    virtual void refreshData();
    
    // 清空表格
    virtual void clear();
    
#if defined(QT_PRINTSUPPORT_LIB)
    // 打印结果
    virtual void printResults(QPrinter *printer);
    virtual void printResults();
#endif

    // 导出为CSV
    bool exportToCSV(const QString &fileName);
    
    // 导出为Excel
    bool exportToExcel(const QString &fileName);

    // 保存表格过滤设置
    void saveTableFilterSettings();

    // 加载表格过滤设置
    void loadTableFilterSettings();

    // 设置是否启用自动查询
    void setAutoQueryEnabled(bool enabled);

protected slots:
    // 表格类型改变
    virtual void onTableTypeChanged(int index);
    
    // 强制刷新
    void onForceRefresh();

protected:
    // 自定义SQL查询
    virtual QString buildQuerySQL() const;
    
    // 设置表头
    virtual void setupTableHeaders();
    
    // 修改SQL的选择子句
    virtual QString modifySelectClause(const QString &sql) const;
    
    // 确保查询模型有效
    bool ensureModelValid();
    
    // 安全地清除模型
    void safeModelClear();
    
    // 检查表结构并锁定UI
    void checkTableStructureAndLockUI();
    
    // 检查表是否包含指定字段
    bool tableHasField(const QString &tableName, const QString &fieldName);
    
    // 应用表结构锁定到UI
    void applyTableStructureLock(const QString &tableName);
    
    // 初始化过滤条件
    void initFilters();
    
    // 初始化UI
    virtual void initUI();
    
    // 初始化连接
    virtual void initConnections();
    
    // 初始化下拉框
    virtual void initComboBoxes();

    Ui::QueryWidget *ui;
    CustomQueryModel *queryModel;  // 改为使用自定义查询模型
    DBManager *dbManager;
    QDateTime startTime;
    QDateTime endTime;
    QString signalFilter;
    bool autoQueryEnabled;
    
    QString currentTable;
    
    // 存储表结构信息的数据结构
    struct TableFieldInfo {
        bool hasHostnameField;  // 是否有主机名字段
        bool hasDeviceField;   // 是否有设备字段
        QStringList fields;    // 所有字段
    };
    
    // 表结构信息映射
    QMap<QString, TableFieldInfo> tableFieldInfoMap;
};

#endif // QUERYWIDGET_H 