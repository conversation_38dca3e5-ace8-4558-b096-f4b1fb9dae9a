#!/bin/bash

# SFTP自动下载脚本 - 特定文件类型下载
# 功能：每分钟自动从远程SFTP服务器下载指定类型的最新文件
# 作者：系统管理员
# 版本：3.0

#==================== 配置区域 ====================
# 请根据实际情况修改以下配置

# SFTP服务器配置
REMOTE_HOST="*************"
REMOTE_PORT="22"
REMOTE_USER="your_username"        # 请替换为实际用户名
REMOTE_PASS="your_password"        # 请替换为实际密码

# 可选：SSH密钥文件路径（如果使用密钥认证，请取消注释并设置路径）
# SSH_KEY_FILE="/home/<USER>/.ssh/id_rsa"

# 目录映射配置（远程目录:本地目录）
declare -A DIR_MAPPINGS=(
    ["/home/<USER>/zhuanJiKong-Ewenjian/"]="/home/<USER>/qsjglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian2/"]="/home/<USER>/djglyc/"
    ["/home/<USER>/zhuanJiKong-Ewenjian3/"]="/home/<USER>/jcglyc/"
)

# 文件类型配置 - 需要下载的文件后缀
FILE_EXTENSIONS=("DQ.WPD" "QXZ.WPD" "NBQ.WPD" "THEROY.WPD" "CDQ.WPD" "NWP.WPD")

# 日志配置
LOG_FILE="/var/log/sftp_auto_download.log"
DOWNLOAD_LOG_DIR="/home/<USER>/sftplog"
DOWNLOAD_LOG_FILE="$DOWNLOAD_LOG_DIR/download_history.log"
LOG_LEVEL="INFO"  # DEBUG, INFO, WARN, ERROR

# 下载配置
RETRY_COUNT=3           # 重试次数
RETRY_DELAY=5           # 重试间隔（秒）
CONNECTION_TIMEOUT=30   # 连接超时（秒）
CHECK_INTERVAL=60       # 检查间隔（秒）

#==================== 脚本主体 ====================

# 全局变量
SCRIPT_NAME="$(basename "$0")"
TEMP_DIR="/tmp/sftp_sync_$$"
LOCK_FILE="/var/lock/sftp_auto_sync.lock"

# 日志函数
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_entry="[$timestamp] [$level] $message"

    # 输出到控制台
    echo "$log_entry"

    # 输出到日志文件
    if [ -w "$(dirname "$LOG_FILE" 2>/dev/null)" ] || [ -w "$LOG_FILE" 2>/dev/null ]; then
        echo "$log_entry" >> "$LOG_FILE" 2>/dev/null
    fi
}

# 下载日志函数
log_download() {
    local remote_path="$1"
    local local_path="$2"
    local filename="$3"
    local filesize="$4"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 确保日志目录存在
    mkdir -p "$DOWNLOAD_LOG_DIR"

    # 记录下载信息
    echo "$timestamp|$remote_path|$local_path|$filename|$filesize" >> "$DOWNLOAD_LOG_FILE"
    log "INFO" "记录下载: $filename ($filesize bytes)"
}

# 读取上次下载的文件列表
get_downloaded_files() {
    local remote_dir="$1"
    local downloaded_files=()

    if [ -f "$DOWNLOAD_LOG_FILE" ]; then
        while IFS='|' read -r timestamp remote_path local_path filename filesize; do
            if [ "$remote_path" = "$remote_dir" ]; then
                downloaded_files+=("$filename")
            fi
        done < "$DOWNLOAD_LOG_FILE"
    fi

    printf '%s\n' "${downloaded_files[@]}"
}

# 检查并创建锁文件（防止重复执行）
acquire_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
            log "WARN" "另一个实例正在运行 (PID: $pid)"
            exit 1
        else
            log "INFO" "清理过期的锁文件"
            rm -f "$LOCK_FILE"
        fi
    fi
    
    echo $$ > "$LOCK_FILE"
    trap 'rm -f "$LOCK_FILE"; cleanup' EXIT INT TERM
}

# 清理函数
cleanup() {
    [ -d "$TEMP_DIR" ] && rm -rf "$TEMP_DIR"
    [ -f "$LOCK_FILE" ] && rm -f "$LOCK_FILE"
}

# 检查依赖
check_dependencies() {
    local missing=()
    
    # 检查基本命令
    for cmd in sftp ssh; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing+=("openssh-client")
            break
        fi
    done
    
    # 检查sshpass（如果不使用密钥）
    if [ -z "${SSH_KEY_FILE:-}" ] && ! command -v sshpass >/dev/null 2>&1; then
        missing+=("sshpass")
    fi
    
    # 检查rsync（可选）
    if [ "$USE_RSYNC" = "true" ] && ! command -v rsync >/dev/null 2>&1; then
        log "WARN" "rsync未安装，将使用sftp方式"
        USE_RSYNC=false
    fi
    
    if [ ${#missing[@]} -gt 0 ]; then
        log "ERROR" "缺少依赖包: ${missing[*]}"
        log "INFO" "安装命令："
        if command -v apt-get >/dev/null 2>&1; then
            log "INFO" "  sudo apt-get install ${missing[*]}"
        elif command -v yum >/dev/null 2>&1; then
            log "INFO" "  sudo yum install ${missing[*]}"
        fi
        return 1
    fi
    return 0
}

# 创建本地目录
ensure_dir() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        if mkdir -p "$dir" 2>/dev/null; then
            log "INFO" "创建目录: $dir"
        else
            log "ERROR" "无法创建目录: $dir"
            return 1
        fi
    fi
    return 0
}

# 测试SFTP连接
test_connection() {
    local test_cmd
    
    if [ -n "${SSH_KEY_FILE:-}" ] && [ -f "$SSH_KEY_FILE" ]; then
        test_cmd="echo 'quit' | sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT '$REMOTE_USER@$REMOTE_HOST'"
    else
        test_cmd="echo 'quit' | sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT '$REMOTE_USER@$REMOTE_HOST'"
    fi
    
    if eval "$test_cmd" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 获取远程目录文件列表
get_remote_files() {
    local remote_dir="$1"
    local temp_list="$TEMP_DIR/remote_files_$(date +%s).txt"

    # 创建SFTP批处理文件获取文件列表
    local batch_file="$TEMP_DIR/list_batch_$(date +%s).txt"
    cat > "$batch_file" << EOF
cd $remote_dir
ls -la
quit
EOF

    # 执行SFTP命令获取文件列表
    local sftp_cmd
    if [ -n "${SSH_KEY_FILE:-}" ] && [ -f "$SSH_KEY_FILE" ]; then
        sftp_cmd="sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
    else
        sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
    fi

    if eval "$sftp_cmd" > "$temp_list" 2>/dev/null; then
        rm -f "$batch_file"
        echo "$temp_list"
        return 0
    else
        rm -f "$batch_file" "$temp_list"
        return 1
    fi
}

# 解析文件列表，获取指定类型的最新文件
get_latest_files() {
    local file_list="$1"
    local remote_dir="$2"
    local downloaded_files_list="$3"
    local latest_files=()

    # 读取已下载文件列表到数组
    local downloaded_files=()
    if [ -n "$downloaded_files_list" ]; then
        while IFS= read -r line; do
            [ -n "$line" ] && downloaded_files+=("$line")
        done <<< "$downloaded_files_list"
    fi

    # 为每种文件类型找到最新的文件
    for ext in "${FILE_EXTENSIONS[@]}"; do
        local latest_file=""
        local latest_time=""

        # 解析文件列表，查找指定扩展名的文件
        while read -r line; do
            # 跳过目录和特殊行
            if [[ "$line" =~ ^d ]] || [[ "$line" =~ ^total ]] || [[ "$line" =~ ^\. ]]; then
                continue
            fi

            # 提取文件名（假设是标准ls -la格式）
            local filename=$(echo "$line" | awk '{print $NF}')

            # 检查文件是否以指定扩展名结尾
            if [[ "$filename" == *"$ext" ]]; then
                # 检查是否已下载
                local already_downloaded=false
                for downloaded in "${downloaded_files[@]}"; do
                    if [ "$downloaded" = "$filename" ]; then
                        already_downloaded=true
                        break
                    fi
                done

                if [ "$already_downloaded" = false ]; then
                    # 提取时间信息（月 日 时间/年）
                    local file_time=$(echo "$line" | awk '{print $6" "$7" "$8}')

                    # 简单比较：如果没有记录或者文件更新，则更新最新文件
                    if [ -z "$latest_file" ] || [[ "$file_time" > "$latest_time" ]]; then
                        latest_file="$filename"
                        latest_time="$file_time"
                    fi
                fi
            fi
        done < "$file_list"

        # 如果找到新的最新文件，添加到列表
        if [ -n "$latest_file" ]; then
            latest_files+=("$latest_file")
            log "INFO" "发现新文件: $latest_file (类型: $ext)"
        fi
    done

    printf '%s\n' "${latest_files[@]}"
}

# 下载指定文件
download_file() {
    local remote_dir="$1"
    local local_dir="$2"
    local filename="$3"
    local attempt=1

    ensure_dir "$local_dir" || return 1

    while [ $attempt -le $RETRY_COUNT ]; do
        log "INFO" "下载文件尝试 ($attempt/$RETRY_COUNT): $filename"

        # 创建批处理文件
        local batch_file="$TEMP_DIR/download_batch_$(date +%s).txt"
        cat > "$batch_file" << EOF
cd $remote_dir
lcd $local_dir
get $filename
quit
EOF

        # 执行SFTP命令
        local sftp_cmd
        if [ -n "${SSH_KEY_FILE:-}" ] && [ -f "$SSH_KEY_FILE" ]; then
            sftp_cmd="sftp -i '$SSH_KEY_FILE' -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        else
            sftp_cmd="sshpass -p '$REMOTE_PASS' sftp -P '$REMOTE_PORT' -o StrictHostKeyChecking=no -o ConnectTimeout=$CONNECTION_TIMEOUT -b '$batch_file' '$REMOTE_USER@$REMOTE_HOST'"
        fi

        if eval "$sftp_cmd" >/dev/null 2>&1; then
            rm -f "$batch_file"

            # 检查文件是否成功下载
            local local_file="$local_dir/$filename"
            if [ -f "$local_file" ]; then
                local filesize=$(stat -c%s "$local_file" 2>/dev/null || echo "0")
                log "INFO" "文件下载成功: $filename ($filesize bytes)"
                log_download "$remote_dir" "$local_dir" "$filename" "$filesize"
                return 0
            else
                log "WARN" "文件下载后未找到: $local_file"
            fi
        else
            rm -f "$batch_file"
            if [ $attempt -lt $RETRY_COUNT ]; then
                log "WARN" "下载失败，等待 $RETRY_DELAY 秒后重试..."
                sleep $RETRY_DELAY
            fi
        fi

        ((attempt++))
    done

    log "ERROR" "文件下载最终失败: $filename"
    return 1
}

# 处理单个目录的下载任务
process_directory() {
    local remote_dir="$1"
    local local_dir="$2"

    log "INFO" "开始处理目录: $remote_dir -> $local_dir"

    # 获取已下载文件列表
    local downloaded_files=$(get_downloaded_files "$remote_dir")

    # 获取远程文件列表
    local file_list=$(get_remote_files "$remote_dir")
    if [ $? -ne 0 ] || [ ! -f "$file_list" ]; then
        log "ERROR" "无法获取远程目录文件列表: $remote_dir"
        return 1
    fi

    # 获取需要下载的最新文件
    local latest_files=$(get_latest_files "$file_list" "$remote_dir" "$downloaded_files")
    rm -f "$file_list"

    if [ -z "$latest_files" ]; then
        log "INFO" "目录 $remote_dir 没有新文件需要下载"
        return 0
    fi

    # 下载每个新文件
    local download_count=0
    local total_files=$(echo "$latest_files" | wc -l)

    while IFS= read -r filename; do
        if [ -n "$filename" ]; then
            if download_file "$remote_dir" "$local_dir" "$filename"; then
                ((download_count++))
            fi
        fi
    done <<< "$latest_files"

    log "INFO" "目录处理完成: $remote_dir ($download_count/$total_files 文件下载成功)"
    return 0
}

# 执行所有下载任务
run_download() {
    local success_count=0
    local total_count=${#DIR_MAPPINGS[@]}

    log "INFO" "开始执行下载任务，共 $total_count 个目录"

    for remote_dir in "${!DIR_MAPPINGS[@]}"; do
        local local_dir="${DIR_MAPPINGS[$remote_dir]}"

        if process_directory "$remote_dir" "$local_dir"; then
            ((success_count++))
        fi
    done

    log "INFO" "下载任务完成: $success_count/$total_count 个目录处理成功"

    if [ $success_count -eq $total_count ]; then
        return 0
    else
        return 1
    fi
}

# 安装为systemd服务
install_service() {
    if [ "$EUID" -ne 0 ]; then
        log "ERROR" "安装服务需要root权限"
        return 1
    fi

    local script_path="$(realpath "$0")"
    local service_name="sftp-auto-download"

    # 创建systemd服务文件
    cat > "/etc/systemd/system/${service_name}.service" << EOF
[Unit]
Description=SFTP Auto Download Service
After=network.target

[Service]
Type=oneshot
ExecStart=$script_path --run
User=admin
Group=admin
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 创建systemd定时器文件 - 每分钟执行
    cat > "/etc/systemd/system/${service_name}.timer" << EOF
[Unit]
Description=SFTP Auto Download Timer
Requires=${service_name}.service

[Timer]
OnCalendar=*:*:00
Persistent=true
RandomizedDelaySec=5

[Install]
WantedBy=timers.target
EOF

    # 重新加载systemd并启用服务
    systemctl daemon-reload
    systemctl enable "${service_name}.timer"
    systemctl start "${service_name}.timer"

    log "INFO" "服务安装完成并已启动"
    log "INFO" "查看状态: systemctl status ${service_name}.timer"
    log "INFO" "查看日志: journalctl -u ${service_name}.service -f"
}

# 卸载服务
uninstall_service() {
    if [ "$EUID" -ne 0 ]; then
        log "ERROR" "卸载服务需要root权限"
        return 1
    fi

    local service_name="sftp-auto-download"

    systemctl stop "${service_name}.timer" 2>/dev/null || true
    systemctl disable "${service_name}.timer" 2>/dev/null || true

    rm -f "/etc/systemd/system/${service_name}.service"
    rm -f "/etc/systemd/system/${service_name}.timer"

    systemctl daemon-reload

    log "INFO" "服务卸载完成"
}

# 显示下载历史
show_history() {
    if [ ! -f "$DOWNLOAD_LOG_FILE" ]; then
        log "INFO" "暂无下载历史记录"
        return 0
    fi

    echo "=== 下载历史记录 ==="
    echo "时间                 | 远程路径                                    | 本地路径              | 文件名                | 大小(bytes)"
    echo "-------------------- | ------------------------------------------- | -------------------- | -------------------- | -----------"

    tail -20 "$DOWNLOAD_LOG_FILE" | while IFS='|' read -r timestamp remote_path local_path filename filesize; do
        printf "%-20s | %-43s | %-20s | %-20s | %s\n" "$timestamp" "$remote_path" "$(basename "$local_path")" "$filename" "$filesize"
    done
}

# 显示帮助信息
show_help() {
    cat << EOF
SFTP自动下载脚本 v3.0

用法: $0 [选项]

选项:
    --run           执行下载任务
    --test          测试SFTP连接
    --install       安装为systemd服务（需要root权限）
    --uninstall     卸载systemd服务（需要root权限）
    --status        显示服务状态
    --history       显示下载历史记录
    --help          显示此帮助信息

配置:
    请编辑脚本顶部的配置区域，设置正确的服务器信息和目录映射

文件类型:
    脚本会自动下载以下类型的最新文件：
    ${FILE_EXTENSIONS[*]}

示例:
    $0 --test       # 测试连接
    $0 --run        # 手动执行下载
    $0 --history    # 查看下载历史
    sudo $0 --install   # 安装服务

EOF
}

# 显示服务状态
show_status() {
    local service_name="sftp-auto-download"

    echo "=== 定时器状态 ==="
    systemctl status "${service_name}.timer" --no-pager -l 2>/dev/null || echo "服务未安装"

    echo -e "\n=== 最近执行日志 ==="
    journalctl -u "${service_name}.service" --no-pager -l -n 10 2>/dev/null || echo "无日志记录"

    echo -e "\n=== 下次执行时间 ==="
    systemctl list-timers "${service_name}.timer" --no-pager 2>/dev/null || echo "定时器未运行"

    echo -e "\n=== 下载统计 ==="
    if [ -f "$DOWNLOAD_LOG_FILE" ]; then
        local total_downloads=$(wc -l < "$DOWNLOAD_LOG_FILE")
        local today_downloads=$(grep "$(date '+%Y-%m-%d')" "$DOWNLOAD_LOG_FILE" 2>/dev/null | wc -l)
        echo "总下载次数: $total_downloads"
        echo "今日下载次数: $today_downloads"
    else
        echo "暂无下载记录"
    fi
}

# 主函数
main() {
    local action="${1:-}"
    
    case "$action" in
        "--run")
            acquire_lock
            mkdir -p "$TEMP_DIR"
            log "INFO" "========== 开始SFTP下载任务 =========="

            if ! check_dependencies; then
                exit 1
            fi

            log "INFO" "测试连接到 $REMOTE_HOST:$REMOTE_PORT..."
            if ! test_connection; then
                log "ERROR" "连接测试失败"
                exit 1
            fi
            log "INFO" "连接测试成功"

            if run_download; then
                log "INFO" "========== 下载任务完成 =========="
                exit 0
            else
                log "ERROR" "========== 下载任务失败 =========="
                exit 1
            fi
            ;;
        "--test")
            log "INFO" "测试SFTP连接到 $REMOTE_HOST:$REMOTE_PORT..."
            if check_dependencies && test_connection; then
                log "INFO" "连接测试成功"
                exit 0
            else
                log "ERROR" "连接测试失败"
                exit 1
            fi
            ;;
        "--install")
            install_service
            ;;
        "--uninstall")
            uninstall_service
            ;;
        "--status")
            show_status
            ;;
        "--history")
            show_history
            ;;
        "--help"|"-h"|"")
            show_help
            ;;
        *)
            log "ERROR" "未知选项: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
