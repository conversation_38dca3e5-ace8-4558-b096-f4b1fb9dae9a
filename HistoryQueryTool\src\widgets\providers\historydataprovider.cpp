#include "historydataprovider.h"
#include "../../database/dbmanager.h"
#include "../../models/station/stationmodel.h"
#include "../../common/constants.h"
#include "../../common/logger.h"
#include <QDebug>
#include <QSqlError>
#include <QRegExp>

// 使用常量定义的表名
using namespace Constants::Tables;

HistoryDataProvider::HistoryDataProvider(QObject *parent)
    : QObject(parent), m_dbManager(nullptr), m_stationModel(nullptr)
{
}

void HistoryDataProvider::setDBManager(DBManager *dbManager)
{
    m_dbManager = dbManager;
}

void HistoryDataProvider::setStationModel(StationModel *model)
{
    m_stationModel = model;
}

QString HistoryDataProvider::getTableNameForType(HistoryDataType type) const
{
    switch (type) {
    case SOE_EVENT:
        return SOE_LOG;
    case YX_CHANGE:
        return COS_LOG;
    case LIMIT_ALARM:
        return BREAK_LIMIT_LOG;
    case SYSTEM_EVENT:
        return SYS_ERROR_INFO;
    case OPERATION_RECORD:
        return OPERATION_LOG;
    case USER_LOGIN:
        return USER_LOG;
    default:
        return SOE_LOG;
    }
}

QSqlQuery HistoryDataProvider::executeQuery(const QString &tableName, 
                                         const QMap<QString, QString> &filters, 
                                         const QDateTime &startTime, 
                                         const QDateTime &endTime)
{
    QSqlQuery emptyQuery;
    
    if (!isDatabaseConnected()) {
        LOG_WARNING("数据库未连接，无法执行查询");
        return emptyQuery;
    }
    
    // 构建查询SQL
    QString sql = buildQuerySQL(tableName, filters, startTime, endTime);
    
    // 修改SQL只选择定义的表头列
    sql = modifySelectClause(sql, tableName);
    
    LOG_DEBUG(QString("执行查询SQL: %1").arg(sql).toStdString().c_str());
    
    // 执行查询
    return m_dbManager->executeQuery(sql);
}

QString HistoryDataProvider::buildQuerySQL(const QString &tableName, 
                                        const QMap<QString, QString> &filters, 
                                        const QDateTime &startTime, 
                                        const QDateTime &endTime)
{
    static const QString SELECT_CLAUSE = "SELECT * FROM %1 WHERE 1=1";
    static const QString TIME_CLAUSE = " AND %1 BETWEEN '%2' AND '%3'";
    static const QString FIELD_CLAUSE = " AND %1='%2'";
    static const QString NUMERIC_FIELD_CLAUSE = " AND %1=%2";  // 数值字段不需要引号
    static const QString LIKE_CLAUSE = " AND %1 LIKE '%%2%'";
    static const QString ORDER_CLAUSE = " ORDER BY %1 DESC";
    
    QString sql;
    
    // 检查表名是否为空
    if (tableName.isEmpty()) {
        LOG_WARNING(QString("警告: 表名为空，使用默认表 %1").arg(SOE_LOG).toStdString().c_str());
        sql = QString(SELECT_CLAUSE).arg(SOE_LOG);
    } else {
        sql = QString(SELECT_CLAUSE).arg(tableName);
    }
    
    // 添加时间范围条件
    QString timeField = Constants::Fields::Common::START_TIME; // 使用常量定义的字段名
    sql += QString(TIME_CLAUSE).arg(timeField,
                                 startTime.toString("yyyy-MM-dd HH:mm:ss"),
                                 endTime.toString("yyyy-MM-dd HH:mm:ss"));
    
    // 定义数值类型字段列表
    QStringList numericFields;
    numericFields << "VALUETYPE" << "ERRTYPE" << "OPERTYPE" << "EQUIPID" << "YXID" 
                << "OLDVALUE" << "NEWVALUE" << "FLAGACK" << "STARTMS" << "MSEC"
                << "BREAKLIMITTYPE" << "ERROBJID" << "ERRSTATE" << "LOGTYPE" << "RECOVERD"
                << "OPERRESULT" << "CTRLTYPE";
    
    // 定义特殊值列表 - 这些值表示"所有"选项，不应添加到SQL条件中
    QStringList specialValues;
    specialValues << "所有" << "所有设备" << "所有事件" << "所有类型";
    
    // 添加其他过滤条件
    QMapIterator<QString, QString> i(filters);
    while (i.hasNext()) {
        i.next();
        
        QString fieldName = i.key();
        QString fieldValue = i.value();
        
        // 检查是否为特殊值，如果是则跳过添加此条件
        if (specialValues.contains(fieldValue)) {
            LOG_DEBUG(QString("跳过特殊值条件: %1=%2").arg(fieldName).arg(fieldValue).toStdString().c_str());
            continue;
        }
        
        // 如果是LIKE查询
        if (fieldName.startsWith("LIKE:")) {
            fieldName.remove(0, 5); // 移除LIKE:前缀
            sql += QString(LIKE_CLAUSE).arg(fieldName, fieldValue);
        } else {
            // 检查是否为数值类型字段
            bool isNumeric = numericFields.contains(fieldName.toUpper());
            
            // 检查fieldValue是否可以转换为数字
            bool ok;
            fieldValue.toInt(&ok);
            
            if (isNumeric && ok) {
                // 数值字段使用不带引号的SQL子句
                sql += QString(NUMERIC_FIELD_CLAUSE).arg(fieldName, fieldValue);
            } else {
                // 普通字符串字段使用带引号的SQL子句
                sql += QString(FIELD_CLAUSE).arg(fieldName, fieldValue);
            }
        }
    }
    
    // 添加排序条件
    sql += QString(ORDER_CLAUSE).arg(timeField);
    
    // 打印最终生成的SQL，用于调试
    LOG_DEBUG(QString("SQL查询语句: %1").arg(sql).toStdString().c_str());
    
    return sql;
}

QStringList HistoryDataProvider::getDistinctValuesFromTable(const QString &tableName, const QString &fieldName)
{
    QStringList values;
    
    // 检查参数
    if (tableName.isEmpty() || fieldName.isEmpty()) {
        LOG_WARNING("获取唯一值失败：表名或字段名为空");
        return values;
    }
    
    // 构建查询SQL
    QString queryStr = QString(
        "SELECT DISTINCT %1 FROM %2 "
        "WHERE %1 IS NOT NULL "
        "ORDER BY %1").arg(fieldName.toLower(), tableName.toLower());
    
    // 执行查询
    DBManager *dbManager = DBManager::instance();
    if (dbManager->isConnected()) {
        QSqlQuery query = dbManager->executeQuery(queryStr);
    
    if (query.lastError().isValid()) {
            LOG_WARNING(QString("执行查询失败: %1").arg(query.lastError().text()).toStdString().c_str());
        return values;
    }
    
    // 处理结果
    while (query.next()) {
            QString value = query.value(0).toString().trimmed();
            if (!values.contains(value)) {
                values.append(value);
        }
    }
    
        LOG_DEBUG(QString("从表 %1 获取字段 %2 的唯一值: %3 个").arg(tableName).arg(fieldName).arg(values.size()).toStdString().c_str());
    } else {
        LOG_WARNING("数据库未连接，无法获取唯一值");
    }
    
    return values;
}

QStringList HistoryDataProvider::getTableFields(const QString &tableName) const
{
    // 如果表字段映射表为空，初始化它
    if (m_tableFieldsMap.isEmpty()) {
        initTableFieldsMap();
    }
    
    // 转换为小写进行比较
    QString lowerTableName = tableName.toLower();
    
    // 返回指定表的字段
    return m_tableFieldsMap.value(lowerTableName, m_tableFieldsMap.value(SOE_LOG));
}

void HistoryDataProvider::initTableFieldsMap() const
{
    // SOE事件表字段 - 根据实际数据库结构
    m_tableFieldsMap["htsoelog"] = QStringList() 
        << "ID"          // 主键ID
        << "HOSTNAME"    // 主机名
        << "EQUIPID"     // 设备ID
        << "YXID"        // 遥信ID
        << "YXNAME"      // 遥信名称
        << "VALUETYPE"   // 事件类型
        << "STARTTIME"   // 事件发生时间
        << "STARTMS"     // 毫秒值
        << "FLAGACK"     // 确认标志
        << "TIMEIN"      // 入库时间
        << "ACKPERSON"   // 确认人
        << "OLDVALUE"    // 原状态值
        << "NEWVALUE";   // 新状态值

    // 遥信变位表字段
    m_tableFieldsMap["htcoslog"] = QStringList() 
        << "ID"          // 主键ID
        << "EQUIPID"     // 设备ID
        << "YXID"        // 遥信ID
        << "YXNAME"      // 遥信名称
        << "OLDVALUE"    // 原状态值
        << "NEWVALUE"    // 新状态值
        << "STARTTIME"   // 变位时间
        << "MSEC"        // 毫秒值
        << "FLAGACK"     // 确认标志
        << "ACKPERSON"   // 确认人
        << "HOSTNAME";   // 主机名
    
    // 用户登录表字段
    m_tableFieldsMap["htuserlog"] = QStringList() 
        << "ID"          // 主键ID
        << "USERNAME"    // 用户名
        << "STARTTIME"   // 操作时间
        << "LOGTYPE"     // 日志类型
        << "HOSTNAME"    // 主机名
        << "APPNAME";    // 应用名称
    
    // 越限告警表字段
    m_tableFieldsMap["htbreaklimitlog"] = QStringList() 
        << "ID"              // 主键ID
        << "HOSTNAME"        // 主机名
        << "EQUIPID"         // 设备ID
        << "YCID"            // 遥测ID
        << "BREAKLIMITTYPE"  // 越限类型
        << "ycname"          // 遥测名称 - 注意：实际数据库字段是ycname
        << "STARTTIME"       // 越限开始时间
        << "ENDTIME"         // 越限结束时间
        << "LIMITVALUE"      // 限值阈值
        << "BREAKVALUE"      // 越限值
        << "RECOVERD"        // 恢复标志
        << "FLAGACK"         // 确认标志
        << "ACKPERSON";      // 确认人
    
    // 系统错误信息表字段
    m_tableFieldsMap["syserrorinfo"] = QStringList() 
        << "ID"          // 主键ID
        << "HOSTNAME"    // 主机名
        << "SOURCENAME"  // 错误来源
        << "ERRORINFO"   // 错误信息
        << "STARTTIME"   // 事件发生时间
        << "FLAGACK"     // 确认标志
        << "ERROBJID"    // 错误对象ID
        << "ERRSTATE"    // 错误状态
        << "ERRTYPE"     // 错误类型
        << "ACKPERSON";  // 确认人
    
    // 操作日志表字段
    m_tableFieldsMap["htoperationlog"] = QStringList() 
        << "ID"              // 主键ID
        << "EQUIPID"         // 设备ID
        << "EQUIPNAME"       // 设备名称
        << "CTRLSIGID"       // 控制信号ID
        << "CTRLSIGNAME"     // 控制信号名称
        << "CTRLTYPE"        // 控制类型
        << "OPERTYPE"        // 操作类型
        << "OPERVALUE"       // 操作值
        << "OPERRESULT"      // 操作结果
        << "STARTTIME"       // 操作时间
        << "OPERATORNAME"    // 操作员
        << "SUPERVISORNAME"  // 监护人
        << "HOSTNAME";       // 主机名
}

QString HistoryDataProvider::modifySelectClause(const QString &sql, const QString &tableName)
{
    // 获取当前表对应的字段列表
    QStringList fields = getTableFields(tableName);
    
    if (fields.isEmpty()) {
        // 如果没有定义字段，返回原始SQL
        LOG_WARNING(QString("警告: 没有为表 %1 定义字段，使用所有列").arg(tableName).toStdString().c_str());
        return sql;
    }
    
    // 表名转小写进行比较
    QString lowerTableName = tableName.toLower();
    
    // 根据不同表格处理时间列
    if (lowerTableName == "htsoelog") {
        // 替换原始的时间字段，格式化为精确到秒
        int startTimeIndex = fields.indexOf("STARTTIME");
        if (startTimeIndex >= 0) {
            fields[startTimeIndex] = "DATE_FORMAT(starttime, '%Y-%m-%d %H:%i:%s') AS STARTTIME";
        }
        
        int timeInIndex = fields.indexOf("TIMEIN");
        if (timeInIndex >= 0) {
            fields[timeInIndex] = "DATE_FORMAT(timein, '%Y-%m-%d %H:%i:%s') AS TIMEIN";
        }
    } 
    else if (lowerTableName == "htcoslog") {
        // 处理htcoslog表的时间列
        int startTimeIndex = fields.indexOf("STARTTIME");
        if (startTimeIndex >= 0) {
            fields[startTimeIndex] = "DATE_FORMAT(starttime, '%Y-%m-%d %H:%i:%s') AS STARTTIME";
        }
    }
    else if (lowerTableName == "htbreaklimitlog") {
        // 处理htbreaklimitlog表的时间列
        int startTimeIndex = fields.indexOf("STARTTIME");
        if (startTimeIndex >= 0) {
            fields[startTimeIndex] = "DATE_FORMAT(starttime, '%Y-%m-%d %H:%i:%s') AS STARTTIME";
        }
        
        int endTimeIndex = fields.indexOf("ENDTIME");
        if (endTimeIndex >= 0) {
            fields[endTimeIndex] = "DATE_FORMAT(endtime, '%Y-%m-%d %H:%i:%s') AS ENDTIME";
        }
    }
    
    // 替换SQL语句
    QString newSql = sql;
    if (newSql.contains(QRegExp("SELECT\\s+\\*\\s+FROM", Qt::CaseInsensitive))) {
        newSql.replace(QRegExp("SELECT\\s+\\*\\s+FROM", Qt::CaseInsensitive), 
                     QString("SELECT %1 FROM").arg(fields.join(", ")));
        LOG_DEBUG(QString("SQL已修改为只选择 %1 个列: %2").arg(fields.size()).arg(fields.join(", ")).toStdString().c_str());
    } else {
        LOG_WARNING("SQL查询不包含 'SELECT * FROM' 模式，无法修改");
    }
    
    return newSql;
}

bool HistoryDataProvider::isDatabaseConnected() const
{
    return m_dbManager && m_dbManager->isConnected();
} 