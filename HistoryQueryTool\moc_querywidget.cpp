/****************************************************************************
** Meta object code from reading C++ file 'querywidget.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.9.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "src/widgets/querywidget.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'querywidget.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.9.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_QueryWidget_t {
    QByteArrayData data[18];
    char stringdata0[226];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_QueryWidget_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_QueryWidget_t qt_meta_stringdata_QueryWidget = {
    {
QT_MOC_LITERAL(0, 0, 11), // "QueryWidget"
QT_MOC_LITERAL(1, 12, 12), // "executeQuery"
QT_MOC_LITERAL(2, 25, 0), // ""
QT_MOC_LITERAL(3, 26, 11), // "refreshData"
QT_MOC_LITERAL(4, 38, 5), // "clear"
QT_MOC_LITERAL(5, 44, 12), // "printResults"
QT_MOC_LITERAL(6, 57, 9), // "QPrinter*"
QT_MOC_LITERAL(7, 67, 7), // "printer"
QT_MOC_LITERAL(8, 75, 11), // "exportToCSV"
QT_MOC_LITERAL(9, 87, 8), // "fileName"
QT_MOC_LITERAL(10, 96, 13), // "exportToExcel"
QT_MOC_LITERAL(11, 110, 23), // "saveTableFilterSettings"
QT_MOC_LITERAL(12, 134, 23), // "loadTableFilterSettings"
QT_MOC_LITERAL(13, 158, 19), // "setAutoQueryEnabled"
QT_MOC_LITERAL(14, 178, 7), // "enabled"
QT_MOC_LITERAL(15, 186, 18), // "onTableTypeChanged"
QT_MOC_LITERAL(16, 205, 5), // "index"
QT_MOC_LITERAL(17, 211, 14) // "onForceRefresh"

    },
    "QueryWidget\0executeQuery\0\0refreshData\0"
    "clear\0printResults\0QPrinter*\0printer\0"
    "exportToCSV\0fileName\0exportToExcel\0"
    "saveTableFilterSettings\0loadTableFilterSettings\0"
    "setAutoQueryEnabled\0enabled\0"
    "onTableTypeChanged\0index\0onForceRefresh"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_QueryWidget[] = {

 // content:
       7,       // revision
       0,       // classname
       0,    0, // classinfo
      12,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,   74,    2, 0x0a /* Public */,
       3,    0,   75,    2, 0x0a /* Public */,
       4,    0,   76,    2, 0x0a /* Public */,
       5,    1,   77,    2, 0x0a /* Public */,
       5,    0,   80,    2, 0x0a /* Public */,
       8,    1,   81,    2, 0x0a /* Public */,
      10,    1,   84,    2, 0x0a /* Public */,
      11,    0,   87,    2, 0x0a /* Public */,
      12,    0,   88,    2, 0x0a /* Public */,
      13,    1,   89,    2, 0x0a /* Public */,
      15,    1,   92,    2, 0x09 /* Protected */,
      17,    0,   95,    2, 0x09 /* Protected */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void,
    QMetaType::Bool, QMetaType::QString,    9,
    QMetaType::Bool, QMetaType::QString,    9,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   14,
    QMetaType::Void, QMetaType::Int,   16,
    QMetaType::Void,

       0        // eod
};

void QueryWidget::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        QueryWidget *_t = static_cast<QueryWidget *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->executeQuery(); break;
        case 1: _t->refreshData(); break;
        case 2: _t->clear(); break;
        case 3: _t->printResults((*reinterpret_cast< QPrinter*(*)>(_a[1]))); break;
        case 4: _t->printResults(); break;
        case 5: { bool _r = _t->exportToCSV((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 6: { bool _r = _t->exportToExcel((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< bool*>(_a[0]) = std::move(_r); }  break;
        case 7: _t->saveTableFilterSettings(); break;
        case 8: _t->loadTableFilterSettings(); break;
        case 9: _t->setAutoQueryEnabled((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 10: _t->onTableTypeChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 11: _t->onForceRefresh(); break;
        default: ;
        }
    }
}

const QMetaObject QueryWidget::staticMetaObject = {
    { &QWidget::staticMetaObject, qt_meta_stringdata_QueryWidget.data,
      qt_meta_data_QueryWidget,  qt_static_metacall, nullptr, nullptr}
};


const QMetaObject *QueryWidget::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *QueryWidget::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_QueryWidget.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int QueryWidget::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 12)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 12;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 12)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 12;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
