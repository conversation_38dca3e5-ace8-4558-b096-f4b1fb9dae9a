#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql
import random
import datetime
import time
import sys

# 数据库连接信息
DB_CONFIG = {
    'host': '************',
    'user': 'root',
    'password': 'root',
    'database': 'his_mysql',
    'charset': 'utf8mb4'
}

# 每个表生成的记录数
RECORDS_PER_TABLE = 200000

# 设备ID范围
EQUIP_ID_MIN = 1
EQUIP_ID_MAX = 100

# 中文设备名称和遥信名称基础
EQUIP_NAME_PREFIX = ["变电站", "配电站", "开关站", "线路", "母线", "断路器", "隔离开关", "变压器", "电容器", "电抗器"]
YX_NAME_PREFIX = ["状态", "位置", "告警", "故障", "过流", "过压", "欠压", "过温", "油位", "压力"]

def print_progress(current, total, prefix='', suffix='', decimals=1, bar_length=50):
    """打印进度条"""
    percent = ("{0:." + str(decimals) + "f}").format(100 * (current / float(total)))
    filled_length = int(bar_length * current // total)
    bar = '#' * filled_length + '-' * (bar_length - filled_length)
    sys.stdout.write('\r%s |%s| %s%% %s' % (prefix, bar, percent, suffix))
    sys.stdout.flush()
    if current == total:
        print()

def get_random_datetime(start_year=2022, end_year=2025):
    """生成随机日期时间"""
    year = random.randint(start_year, end_year)
    month = random.randint(1, 12)
    day = random.randint(1, 28)  # 为简化，使用28天
    hour = random.randint(0, 23)
    minute = random.randint(0, 59)
    second = random.randint(0, 59)
    return datetime.datetime(year, month, day, hour, minute, second)

def get_random_name(prefix_list):
    """生成随机名称"""
    prefix = random.choice(prefix_list)
    suffix = str(random.randint(1, 100))
    return f"{prefix}{suffix}"

def generate_soe_data(cursor, count):
    """生成SOE事件表数据"""
    print("正在生成SOE事件表数据...")
    
    for i in range(count):
        equip_id = random.randint(EQUIP_ID_MIN, EQUIP_ID_MAX)
        yx_id = random.randint(1, 10000)
        yx_name = get_random_name(YX_NAME_PREFIX)
        value_type = random.randint(0, 5)
        start_time = get_random_datetime()
        start_ms = random.randint(0, 999)
        flag_ack = random.randint(0, 1)
        time_in = start_time + datetime.timedelta(seconds=random.randint(1, 10))
        ack_person = f"操作员{random.randint(1, 20)}"
        old_value = random.randint(0, 1)
        new_value = 1 - old_value  # 确保新值与旧值不同
        hostname = f"HOST{random.randint(1, 10)}"
        
        sql = """
        INSERT INTO htsoelog (
            HOSTNAME, EQUIPID, YXID, YXNAME, VALUETYPE, 
            STARTTIME, STARTMS, FLAGACK, TIMEIN, 
            ACKPERSON, OLDVALUE, NEWVALUE
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(sql, (
            hostname, equip_id, yx_id, yx_name, value_type,
            start_time, start_ms, flag_ack, time_in,
            ack_person, old_value, new_value
        ))
        
        if (i + 1) % 10000 == 0:
            conn.commit()
            print_progress(i + 1, count, prefix='进度:', suffix='完成', bar_length=50)
    
    conn.commit()
    print("SOE事件表数据生成完成!")

def generate_cos_data(cursor, count):
    """生成遥信变位表数据"""
    print("正在生成遥信变位表数据...")
    
    for i in range(count):
        equip_id = random.randint(EQUIP_ID_MIN, EQUIP_ID_MAX)
        yx_id = random.randint(1, 10000)
        yx_name = get_random_name(YX_NAME_PREFIX)
        old_value = random.randint(0, 1)
        new_value = 1 - old_value  # 确保新值与旧值不同
        start_time = get_random_datetime()
        msec = random.randint(0, 999)
        flag_ack = random.randint(0, 1)
        ack_person = f"操作员{random.randint(1, 20)}" if flag_ack == 1 else None
        hostname = f"HOST{random.randint(1, 10)}"
        
        sql = """
        INSERT INTO htcoslog (
            EQUIPID, YXID, YXNAME, OLDVALUE, NEWVALUE,
            STARTTIME, MSEC, FLAGACK, ACKPERSON, HOSTNAME
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(sql, (
            equip_id, yx_id, yx_name, old_value, new_value,
            start_time, msec, flag_ack, ack_person, hostname
        ))
        
        if (i + 1) % 10000 == 0:
            conn.commit()
            print_progress(i + 1, count, prefix='进度:', suffix='完成', bar_length=50)
    
    conn.commit()
    print("遥信变位表数据生成完成!")

def generate_break_limit_data(cursor, count):
    """生成越限告警表数据"""
    print("正在生成越限告警表数据...")
    
    for i in range(count):
        hostname = f"HOST{random.randint(1, 10)}"
        equip_id = random.randint(EQUIP_ID_MIN, EQUIP_ID_MAX)
        yc_id = random.randint(1, 5000)
        break_limit_type = random.randint(1, 4)  # 1-上限，2-上上限，3-下限，4-下下限
        yct_name = f"测点{random.randint(1, 100)}"
        start_time = get_random_datetime()
        
        # 50%的概率已恢复，50%未恢复
        recovered = random.randint(0, 1)
        end_time = start_time + datetime.timedelta(minutes=random.randint(1, 60)) if recovered == 1 else None
        
        limit_value = round(random.uniform(80, 120), 2)
        break_value = round(limit_value + random.uniform(10, 20), 2) if break_limit_type <= 2 else round(limit_value - random.uniform(10, 20), 2)
        
        flag_ack = random.randint(0, 1)
        ack_person = f"操作员{random.randint(1, 20)}" if flag_ack == 1 else None
        
        sql = """
        INSERT INTO htbreaklimitlog (
            HOSTNAME, EQUIPID, YCID, BREAKLIMITTYPE, YCTNAME,
            STARTTIME, ENDTIME, LIMITVALUE, BREAKVALUE, RECOVERD,
            FLAGACK, ACKPERSON
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(sql, (
            hostname, equip_id, yc_id, break_limit_type, yct_name,
            start_time, end_time, limit_value, break_value, recovered,
            flag_ack, ack_person
        ))
        
        if (i + 1) % 10000 == 0:
            conn.commit()
            print_progress(i + 1, count, prefix='进度:', suffix='完成', bar_length=50)
    
    conn.commit()
    print("越限告警表数据生成完成!")

def generate_sys_error_data(cursor, count):
    """生成系统错误信息表数据"""
    print("正在生成系统错误信息表数据...")
    
    error_sources = ["数据库", "通信服务", "前端应用", "后台服务", "网络组件", "操作系统"]
    error_types = [1, 2, 3, 4, 5]  # 不同的错误类型
    error_infos = [
        "连接超时",
        "通信中断",
        "数据库查询失败",
        "内存溢出",
        "设备不响应",
        "配置错误",
        "文件访问权限不足",
        "服务启动失败",
        "用户验证失败",
        "网络不可达"
    ]
    
    for i in range(count):
        hostname = f"HOST{random.randint(1, 10)}"
        source_name = random.choice(error_sources)
        error_info = random.choice(error_infos) + f"，错误码：E{random.randint(1000, 9999)}"
        start_time = get_random_datetime()
        flag_ack = random.randint(0, 1)
        err_obj_id = random.randint(1, 1000) if random.random() < 0.7 else None
        err_state = random.randint(0, 3)
        err_type = random.choice(error_types)
        ack_person = f"操作员{random.randint(1, 20)}" if flag_ack == 1 else None
        
        sql = """
        INSERT INTO syserrorinfo (
            HOSTNAME, SOURCENAME, ERRORINFO, STARTTIME,
            FLAGACK, ERROBJID, ERRSTATE, ERRTYPE, ACKPERSON
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(sql, (
            hostname, source_name, error_info, start_time,
            flag_ack, err_obj_id, err_state, err_type, ack_person
        ))
        
        if (i + 1) % 10000 == 0:
            conn.commit()
            print_progress(i + 1, count, prefix='进度:', suffix='完成', bar_length=50)
    
    conn.commit()
    print("系统错误信息表数据生成完成!")

def create_tables_if_not_exist(cursor):
    """创建表（如果不存在）"""
    print("检查并创建必要的表...")
    
    # SOE事件表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS htsoelog (
        ID INT(11) AUTO_INCREMENT PRIMARY KEY,
        HOSTNAME VARCHAR(50),
        EQUIPID INT(11),
        YXID INT(11),
        YXNAME VARCHAR(200),
        VALUETYPE TINYINT(4),
        STARTTIME DATETIME,
        STARTMS SMALLINT(6),
        FLAGACK TINYINT(4),
        TIMEIN DATETIME,
        ACKPERSON VARCHAR(80),
        OLDVALUE SMALLINT(6),
        NEWVALUE SMALLINT(6)
    )
    """)
    
    # 遥信变位表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS htcoslog (
        ID INT(11) AUTO_INCREMENT PRIMARY KEY,
        EQUIPID INT(11),
        YXID INT(11),
        YXNAME VARCHAR(200),
        OLDVALUE SMALLINT(6),
        NEWVALUE SMALLINT(6),
        STARTTIME DATETIME,
        MSEC SMALLINT(6),
        FLAGACK TINYINT(4),
        ACKPERSON VARCHAR(80),
        HOSTNAME VARCHAR(50)
    )
    """)
    
    # 越限告警表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS htbreaklimitlog (
        ID INT(11) AUTO_INCREMENT PRIMARY KEY,
        HOSTNAME VARCHAR(50),
        EQUIPID INT(11),
        YCID INT(11),
        BREAKLIMITTYPE TINYINT(4),
        YCTNAME VARCHAR(50),
        STARTTIME DATETIME,
        ENDTIME DATETIME,
        LIMITVALUE DECIMAL(12,4),
        BREAKVALUE DECIMAL(12,4),
        RECOVERD TINYINT(4),
        FLAGACK TINYINT(4),
        ACKPERSON VARCHAR(80)
    )
    """)
    
    # 系统错误信息表
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS syserrorinfo (
        ID INT(11) AUTO_INCREMENT PRIMARY KEY,
        HOSTNAME VARCHAR(50),
        SOURCENAME VARCHAR(50),
        ERRORINFO VARCHAR(2048),
        STARTTIME DATETIME,
        FLAGACK TINYINT(4),
        ERROBJID INT(11),
        ERRSTATE SMALLINT(6),
        ERRTYPE TINYINT(4),
        ACKPERSON VARCHAR(80)
    )
    """)
    
    conn.commit()
    print("表创建完成!")

if __name__ == "__main__":
    try:
        print(f"正在连接到 {DB_CONFIG['host']} 的数据库 {DB_CONFIG['database']}...")
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        print("数据库连接成功!")
        
        # 创建表（如果不存在）
        create_tables_if_not_exist(cursor)
        
        # 生成数据
        generate_soe_data(cursor, RECORDS_PER_TABLE)
        generate_cos_data(cursor, RECORDS_PER_TABLE)
        generate_break_limit_data(cursor, RECORDS_PER_TABLE)
        generate_sys_error_data(cursor, RECORDS_PER_TABLE)
        
        cursor.close()
        conn.close()
        print("数据生成完成!")
        
    except Exception as e:
        print(f"错误: {e}") 
