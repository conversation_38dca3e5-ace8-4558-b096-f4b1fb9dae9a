#ifndef HISTORYDATAPROVIDER_H
#define HISTORYDATAPROVIDER_H

#include <QObject>
#include <QSqlQuery>
#include <QDateTime>
#include <QStringList>
#include <QMap>
#include "../../common/enums.h"

class DBManager;
class StationModel;

/**
 * @brief 历史数据提供者类
 * 负责数据库查询、SQL构建等数据层操作
 */
class HistoryDataProvider : public QObject
{
    Q_OBJECT
public:
    explicit HistoryDataProvider(QObject *parent = nullptr);
    
    /**
     * @brief 设置数据库管理器
     * @param dbManager 数据库管理器指针
     */
    void setDBManager(DBManager *dbManager);
    
    /**
     * @brief 设置站点模型
     * @param model 站点模型指针
     */
    void setStationModel(StationModel *model);
    
    /**
     * @brief 执行查询获取数据
     * @param tableName 表名
     * @param filters 过滤条件键值对
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    QSqlQuery executeQuery(const QString &tableName, const QMap<QString, QString> &filters, 
                         const QDateTime &startTime, const QDateTime &endTime);
    
    /**
     * @brief 构建查询SQL语句
     * @param tableName 表名
     * @param filters 过滤条件键值对
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return SQL查询语句
     */
    QString buildQuerySQL(const QString &tableName, const QMap<QString, QString> &filters,
                        const QDateTime &startTime, const QDateTime &endTime);
    
    /**
     * @brief 根据历史数据类型获取对应的表名
     * @param type 历史数据类型
     * @return 表名
     */
    QString getTableNameForType(HistoryDataType type) const;
    
    /**
     * @brief 获取表的字段列表
     * @param tableName 表名
     * @return 字段列表
     */
    QStringList getTableFields(const QString &tableName) const;
    
    /**
     * @brief 从表中获取字段的唯一值列表
     * @param tableName 表名
     * @param fieldName 字段名
     * @return 唯一值列表
     */
    QStringList getDistinctValuesFromTable(const QString &tableName, const QString &fieldName);
    
    /**
     * @brief 修改SQL查询，只选择指定字段
     * @param sql 原始SQL查询
     * @param tableName 表名
     * @return 修改后的SQL查询
     */
    QString modifySelectClause(const QString &sql, const QString &tableName);
    
    /**
     * @brief 检查数据库连接是否有效
     * @return 是否连接有效
     */
    bool isDatabaseConnected() const;
    
private:
    // 数据库管理器
    DBManager *m_dbManager;
    
    // 站点模型
    StationModel *m_stationModel;
    
    // 表字段映射表缓存
    mutable QMap<QString, QStringList> m_tableFieldsMap;
    
    // 初始化表字段映射
    void initTableFieldsMap() const;
};

#endif // HISTORYDATAPROVIDER_H 