#ifndef XMLCONFIGREADER_H
#define XMLCONFIGREADER_H

#include <QString>
#include <QMap>

struct DBConfig {
    QString name;
    QString type;
    QString server;
    QString user;
    QString pwd;
    int port;
};

class XMLConfigReader {
public:
    static XMLConfigReader* instance();
    
    bool loadConfig(const QString &filePath);
    DBConfig getParaDBConfig() const;
    DBConfig getHisDBConfig() const;
    
    bool isParaDBConfigValid() const;
    bool isHisDBConfigValid() const;
    bool isConfigLoaded() const { return m_configLoaded; }
    
private:
    XMLConfigReader();
    ~XMLConfigReader();
    
    static XMLConfigReader *m_instance;
    
    DBConfig m_paraDBConfig;
    DBConfig m_hisDBConfig;
    bool m_configLoaded;
};

#endif // XMLCONFIGREADER_H 